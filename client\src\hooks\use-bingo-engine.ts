"use client";

import React, { useReducer, useEffect, useCallback } from 'react';
import { generateTickets, checkPrizes, type Ticket, type Prize, type PrizeWinner } from '@/lib/bingo';
import { useToast } from './use-toast';
import { Trophy } from 'lucide-react';
import { getSocket } from '@/lib/socket';
import { Socket } from 'socket.io-client';

// State and Action Types
export type GameState = 'idle' | 'running' | 'paused' | 'ended';

type Settings = {
  numTickets: number;
  speed: number;
  isAutoPlay: boolean;
  isMuted: boolean;
};

export type BingoState = {
  gameState: GameState;
  tickets: Ticket[];
  players: Array<String>;
  allNumbers: number[];
  calledNumbers: Set<number>;
  calledNumbersHistory: number[];
  currentNumber: number | null;
  prizeWinners: Partial<Record<Prize, PrizeWinner>>;
  settings: Settings;
};

export type BingoAction =
  | { type: 'START_GAME', socket: Socket }
  | { type: 'PAUSE_GAME' }
  | { type: 'RESET_GAME' }
  | { type: 'CALL_NUMBER', socket: Socket }
  | { type: 'UPDATE_SETTINGS'; payload: Partial<Settings> }
  | { type: 'CLAIM_PRIZE'; payload: { prize: Prize; winner: PrizeWinner } }
  | { type: 'UPDATE_PLAYER'; payload: { players: Array<String> } };

// Initial State
const initialStateFactory = (players: Array<String> = [], tickets: Ticket[] = [], settings: Partial<Settings> = {}): BingoState => {
  const defaultSettings: Settings = {
    numTickets: 3,
    speed: 3000,
    isAutoPlay: true,
    isMuted: false,
    ...settings,
  };

  return {
    gameState: 'idle',
    players: players,
    tickets: tickets,
    allNumbers: Array.from({ length: 90 }, (_, i) => i + 1),
    calledNumbers: new Set(),
    calledNumbersHistory: [],
    currentNumber: null,
    prizeWinners: {},
    settings: defaultSettings,
  };
};

// Reducer
function bingoReducer(state: BingoState, action: BingoAction): BingoState {
  switch (action.type) {
    case 'START_GAME':
      if (state.gameState === 'idle' || state.gameState === 'ended') {
        const newState = initialStateFactory(state.players, state.tickets, state.settings);
        action.socket.emit('update-game-state', { roomId: window && window.location.pathname.split("?")[0].slice(1), state: newState });
        newState.gameState = 'running';
        return newState;
      }
       if (state.gameState === 'paused') {
        return { ...state, gameState: 'running' };
      }
      return state;

    case 'PAUSE_GAME':
      if (state.gameState === 'running') {
        return { ...state, gameState: 'paused' };
      }
       if (state.gameState === 'paused') {
        return { ...state, gameState: 'running' };
      }
      return state;

    case 'RESET_GAME':
      return initialStateFactory(state.players, state.tickets, state.settings);

    case 'CALL_NUMBER': {
      if (state.gameState !== 'running') return state;
      const remainingNumbers = state.allNumbers.filter(n => !state.calledNumbers.has(n));
      if (remainingNumbers.length === 0) {
        return { ...state, gameState: 'ended' };
      }
      const randomIndex = Math.floor(Math.random() * remainingNumbers.length);
      const newNumber = remainingNumbers[randomIndex];
      const newCalledNumbers = new Set(state.calledNumbers).add(newNumber);
      action.socket.emit('update-game-state', { roomId: window && window.location.pathname.split("?")[0].slice(1), state: {
        ...state,
        calledNumbers: newCalledNumbers,
        currentNumber: newNumber,
        calledNumbersHistory: [newNumber, ...state.calledNumbersHistory],
      }});
      
      return {
        ...state,
        calledNumbers: newCalledNumbers,
        currentNumber: newNumber,
        calledNumbersHistory: [newNumber, ...state.calledNumbersHistory],
      };
    }
    
    case 'CLAIM_PRIZE':
        if(state.prizeWinners[action.payload.prize]){
            return state; // Already claimed
        }
        return {
            ...state,
            prizeWinners: {
                ...state.prizeWinners,
                [action.payload.prize]: action.payload.winner
            }
        };

    case 'UPDATE_SETTINGS':
      const newSettings = { ...state.settings, ...action.payload };
      if (state.gameState === 'idle') {
          let generatedTickets: Ticket[] = [];
          state.players.forEach(player => {
            generatedTickets = [...generatedTickets, ...generateTickets(newSettings.numTickets, player as string)];
          })
          return {
              ...state,
              settings: newSettings,
              tickets: generatedTickets 
          }
      }
      return { ...state, settings: newSettings };
    
    case 'UPDATE_PLAYER':
      const newPlayers = action.payload.players;
      if (state.gameState === 'idle') {
        let generatedTickets: Ticket[] = [];
        newPlayers.forEach(player => {
          generatedTickets = [...generatedTickets, ...generateTickets(state.settings.numTickets, player as string)];
        })
        return {
            ...state,
            players: newPlayers,
            tickets: generatedTickets 
        }
      }
      return { ...state, players: newPlayers };

    default:
      return state;
  }
}

// Custom Hook
export function useBingoEngine() {
  const [state, dispatch] = useReducer(bingoReducer, initialStateFactory());
  const socket = getSocket();
  const { toast } = useToast();

  // Auto-play interval
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;
    if (state.gameState === 'running' && state.settings.isAutoPlay) {
      interval = setInterval(() => {
        dispatch({ type: 'CALL_NUMBER', socket });
      }, state.settings.speed);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [state.gameState, state.settings.isAutoPlay, state.settings.speed]);

  // Prize checking logic
  const checkAllPrizes = useCallback(() => {
    if (state.gameState !== 'running') return;
    
    state.tickets.forEach((ticket, ticketIndex) => {
        const wonPrizes = checkPrizes(ticket, state.calledNumbers);
        (Object.keys(wonPrizes) as Prize[]).forEach(prize => {
            if (wonPrizes[prize] && !state.prizeWinners[prize]) {
                dispatch({ type: 'CLAIM_PRIZE', payload: { prize, winner: { ticketIndex } } });
                toast({
                    title: "Prize Won!",
                    description: `Ticket #${ticketIndex + 1} won ${prize}!`,
                  })
            }
        });
    });

  }, [state.tickets, state.calledNumbers, state.prizeWinners, state.gameState, toast]);

  useEffect(() => {
    checkAllPrizes();
  }, [state.calledNumbers, checkAllPrizes]);


  return { state, dispatch };
}
