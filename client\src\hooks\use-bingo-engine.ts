"use client";

import React, { useReducer, useEffect, useCallback } from 'react';
import { generateTickets, checkPrizes, type Ticket, type Prize, type PrizeWinner } from '@/lib/bingo';
import { useToast } from './use-toast';
import { Trophy } from 'lucide-react';
import { getSocket } from '@/lib/socket';
import { Socket } from 'socket.io-client';
import { speak } from '@/lib/speech';

// State and Action Types
export type GameState = 'idle' | 'running' | 'paused' | 'ended';

type Settings = {
  speed: number;
  isAutoPlay: boolean;
  isMuted: boolean;
  isMaleVoice: boolean;
};

export type BingoState = {
  gameState: GameState;
  tickets: Ticket[];
  players: Array<String>;
  allNumbers: number[];
  calledNumbers: Set<number>;
  calledNumbersHistory: number[];
  currentNumber: number | null;
  prizeWinners: Partial<Record<Prize, PrizeWinner>>;
  settings: Settings;
  isAdmin: boolean;
};

export type BingoAction =
  | { type: 'START_GAME', socket: Socket }
  | { type: 'PAUSE_GAME' }
  | { type: 'RESET_GAME' }
  | { type: 'CALL_NUMBER', socket: Socket }
  | { type: 'UPDATE_SETTINGS'; payload: Partial<Settings> }
  | { type: 'CLAIM_PRIZE'; payload: { prize: Prize; winner: PrizeWinner } }
  | { type: 'UPDATE_PLAYER'; payload: { players: Array<String> } }
  | { type: 'UPDATE_EVERYTHING'; payload: { state: BingoState }}
  | { type: 'PURCHASE_TICKET'; socket: Socket }
  | { type: 'LOCAL_ADD_TICKET'; payload: { ticket: Ticket, socket: Socket } };

// Initial State
const initialStateFactory = (players: Array<String> = [], tickets: Ticket[] = [], settings: Partial<Settings> = {}): BingoState => {
  const defaultSettings: Settings = {
    speed: 3000,
    isAutoPlay: true,
    isMuted: false,
    isMaleVoice: true,
    ...settings,
  };

  return {
    gameState: 'idle',
    players: players,
    tickets: tickets,
    allNumbers: Array.from({ length: 90 }, (_, i) => i + 1),
    calledNumbers: new Set(),
    calledNumbersHistory: [],
    currentNumber: null,
    prizeWinners: {},
    settings: defaultSettings,
    isAdmin: false,
  };
};

// Reducer
function bingoReducer(state: BingoState, action: BingoAction): BingoState {
  switch (action.type) {
    case 'START_GAME':
      if (state.gameState === 'idle' || state.gameState === 'ended') {
        const newState = initialStateFactory(state.players, state.tickets, state.settings);
        newState.isAdmin = true;
        action.socket.emit('update-game-state', JSON.stringify({ roomId: window && window.location.pathname.split("?")[0].slice(1), state: newState }));
        newState.gameState = 'running';
        return newState;
      }
       if (state.gameState === 'paused') {
        return { ...state, gameState: 'running' };
      }
      return state;

    case 'PAUSE_GAME':
      if (state.gameState === 'running') {
        return { ...state, gameState: 'paused' };
      }
       if (state.gameState === 'paused') {
        return { ...state, gameState: 'running' };
      }
      return state;

    case 'RESET_GAME':
      return initialStateFactory(state.players, state.tickets, state.settings);

    case 'CALL_NUMBER': {
      if (state.gameState !== 'running') return state;
      const remainingNumbers = state.allNumbers.filter(n => !state.calledNumbersHistory.includes(n));
      if (remainingNumbers.length === 0) {
        return { ...state, gameState: 'ended' };
      }
      const randomIndex = Math.floor(Math.random() * remainingNumbers.length);
      const newNumber = remainingNumbers[randomIndex];
      const newCalledNumbers = new Set(state.calledNumbersHistory).add(newNumber);
      action.socket.emit('update-game-state', JSON.stringify({ roomId: window && window.location.pathname.split("?")[0].slice(1), state: {
        ...state,
        calledNumbers: newCalledNumbers,
        currentNumber: newNumber,
        calledNumbersHistory: [newNumber, ...state.calledNumbersHistory],
      }}));
      
      return {
        ...state,
        calledNumbers: newCalledNumbers,
        currentNumber: newNumber,
        calledNumbersHistory: [newNumber, ...state.calledNumbersHistory],
      };
    }
    
    case 'CLAIM_PRIZE':
        if(state.prizeWinners[action.payload.prize]){
            return state; // Already claimed
        }
        return {
            ...state,
            prizeWinners: {
                ...state.prizeWinners,
                [action.payload.prize]: action.payload.winner
            }
        };

    case 'UPDATE_SETTINGS':
      const newSettings = { ...state.settings, ...action.payload };
      if (state.gameState === 'idle') {
          let generatedTickets: Ticket[] = [];
          state.players.forEach(player => {
            generatedTickets = [...generatedTickets, ...generateTickets(player as string)];
          })
          return {
              ...state,
              settings: newSettings,
              tickets: generatedTickets 
          }
      }
      return { ...state, settings: newSettings };
    
    case 'UPDATE_PLAYER':
      const newPlayers = action.payload.players;
      if (state.gameState === 'idle') {
        return {
            ...state,
            players: newPlayers,
        }
      }
      return { ...state, players: newPlayers };
    
    case 'UPDATE_EVERYTHING':
      return action.payload.state;
    
    case 'PURCHASE_TICKET':
      if (state.gameState !== 'idle' || !action.socket) return state;
      const newTicket = generateTickets(action.socket.id!);
      action.socket.emit('add-ticket', JSON.stringify({ roomId: window && window.location.pathname.split("?")[0].slice(1), ticket: newTicket[0] }));
      return {
        ...state,
        tickets: [...state.tickets, newTicket[0]]
      };

    case 'LOCAL_ADD_TICKET':
      if (state.tickets.find(ticket => ticket.id === action.payload.ticket.id)) return state;
      action.payload.socket.emit('update-game-state', JSON.stringify({ roomId: window && window.location.pathname.split("?")[0].slice(1), state: {
        ...state,
        tickets: [...state.tickets, action.payload.ticket]
      }}));
      return {
        ...state,
        tickets: [...state.tickets, action.payload.ticket]
      };

    default:
      return state;
  }
}

// Custom Hook
export function useBingoEngine() {
  const [state, dispatch] = useReducer(bingoReducer, initialStateFactory());
  const socket = getSocket();
  const { toast } = useToast();

  // Auto-play interval
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;
    if (state.gameState === 'running' && state.settings.isAutoPlay && state.isAdmin) {
      interval = setInterval(() => {
        dispatch({ type: 'CALL_NUMBER', socket });
      }, state.settings.speed);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [state.gameState, state.settings.isAutoPlay, state.settings.speed]);

  // Prize checking logic
  const checkAllPrizes = useCallback(() => {
    if (state.gameState !== 'running') return;
    
    state.tickets.forEach((ticket, ticketIndex) => {
        const wonPrizes = checkPrizes(ticket, state.calledNumbersHistory);
        (Object.keys(wonPrizes) as Prize[]).forEach(prize => {
            if (wonPrizes[prize] && !state.prizeWinners[prize]) {
                dispatch({ type: 'CLAIM_PRIZE', payload: { prize, winner: { belongsTo: ticket.belongsTo, ticketIndex } } });
                toast({
                    title: "Prize Won!",
                    description: `Ticket #${ticketIndex + 1} won ${prize}!`,
                  })
            }
        });
    });

  }, [state.tickets, state.calledNumbers, state.prizeWinners, state.gameState, toast]);

  useEffect(() => {
    socket.on("ticket-added", (ticket: Ticket) => {
      console.log(`Received ticket update: ${ticket.belongsTo}`);
      dispatch({ type: 'LOCAL_ADD_TICKET', payload: { ticket, socket } });
    });
  }, [])

  useEffect(() => {
    checkAllPrizes();
  }, [state.calledNumbers, checkAllPrizes]);


  return { state, dispatch };
}
