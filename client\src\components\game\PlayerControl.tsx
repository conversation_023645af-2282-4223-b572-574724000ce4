"use client";

import React, { useEffect, useRef, useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Play, Pause, RefreshCw, Settings, Volume2, VolumeX, Venus, Mars } from 'lucide-react';

export function PlayerControls() {
	const [isMusicOn, setIsMusicOn] = useState(true);
	const [isCallerOn, setIsCallerOn] = useState(true);
	const bgmRef = useRef<HTMLAudioElement | null>(null);

	useEffect(() => {
		if (!bgmRef.current) {
			bgmRef.current = new Audio("../../assets/music/videoplayback.mp3");
			bgmRef.current.loop = true;
		}

		return () => {
			bgmRef.current?.pause();
		}
	}, []);

  return (
    <Card className="bg-card/50 backdrop-blur-sm">
      <CardHeader>
        <CardTitle className="font-headline text-2xl flex items-center gap-2 glow-primary">
          <Settings className="w-6 h-6" />
          Controls
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
           <div className="flex items-center justify-between">
            <Label htmlFor="sound-toggle" className="flex items-center gap-2">
               {!isMusicOn ? <VolumeX className="w-5 h-5" /> : <Volume2 className="w-5 h-5" />}
              Background Music
            </Label>
            <Switch
              id="sound-toggle"
              checked={isMusicOn}
              onCheckedChange={(checked) =>
                {
									if (!bgmRef.current) return;
									if (checked) {
										bgmRef.current.play();
									} else {
										bgmRef.current.pause();
									}
									setIsMusicOn(checked);
								}
              }
            />
          </div>
           <div className="flex items-center justify-between">
            <Label htmlFor="sound-toggle" className="flex items-center gap-2">
               {!isCallerOn ? <VolumeX className="w-5 h-5" /> : <Volume2 className="w-5 h-5" />}
							 Caller Sound
            </Label>
            <Switch
              id="sound-toggle"
              checked={isCallerOn}
              onCheckedChange={(checked) =>
                {
									setIsCallerOn(checked);
								}
              }
            />
          </div>
      </CardContent>
    </Card>
  );
}
