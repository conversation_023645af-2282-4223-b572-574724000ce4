"use client";

import React, { useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { speak } from '@/lib/speech';

type CallerProps = {
  currentNumber: number | null;
  calledNumbersHistory: number[];
  isMuted: boolean;
  isAdmin: boolean;
  isMale: boolean;
};

export function Caller({ currentNumber, calledNumbersHistory, isMuted, isAdmin, isMale }: CallerProps) {
  useEffect(() => {
    if (!isMuted && currentNumber !== null && isAdmin) {
      speak(`Number ${currentNumber}`, isMale);
    }
  }, [currentNumber, isMuted, isAdmin]);

  return (
    <Card className="text-center bg-card/50 backdrop-blur-sm h-full">
      <CardHeader>
        <CardTitle className="font-headline text-2xl glow-primary">Caller</CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col items-center justify-center gap-4">
        <div className="relative w-40 h-40 flex items-center justify-center">
          <div className="absolute inset-0 bg-primary/20 rounded-full animate-pulse blur-xl"></div>
          <div className="relative z-10 flex items-center justify-center w-36 h-36 bg-background rounded-full border-4 border-primary">
            {currentNumber !== null ? (
              <span key={currentNumber} className="font-headline text-7xl caller-animation text-accent">
                {currentNumber}
              </span>
            ) : (
              <span className="text-muted-foreground">-</span>
            )}
          </div>
        </div>
        <div className="flex gap-2 mt-2">
          <span className="text-muted-foreground">History:</span>
          {calledNumbersHistory.slice(0, 5).map((num, index) => (
            <span
              key={index}
              className="flex items-center justify-center w-8 h-8 rounded-full bg-secondary text-secondary-foreground font-bold"
              style={{ opacity: 1 - index * 0.2 }}
            >
              {num}
            </span>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
