"use client"

export function speak(text: string, isMaleVoice: boolean) {
  if (typeof window === 'undefined' || !window.speechSynthesis) {
    return;
  }

  // Cancel any previous speech
  window.speechSynthesis.cancel();
  
  const utterance = new SpeechSynthesisUtterance(text);
  utterance.lang = 'en-US';
  
  // Optional: find a specific voice
  const voices = window.speechSynthesis.getVoices();
  console.log(voices);
  if (isMaleVoice) {
    utterance.voice = voices.find(v => v.name.includes("<PERSON>")) || voices[0];
  } else {
    utterance.voice = voices.find(v => v.name.includes("<PERSON><PERSON>")) || voices[0];
  }
  
  window.speechSynthesis.speak(utterance);
}
