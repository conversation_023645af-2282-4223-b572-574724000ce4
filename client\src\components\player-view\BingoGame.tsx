"use client";

import React, { useState } from 'react';
import { useBingoEngine } from '@/hooks/use-bingo-engine';
import { Caller } from '@/components/game/Caller';
import { Controls } from '@/components/game/Controls';
import { Prizes } from '@/components/game/Prizes';
import { Ticket } from '@/components/game/Ticket';
import { BingoBoard } from '@/components/game/BingoBoard';
import { ScrollArea } from '@/components/ui/scroll-area';
import { getSocket } from '@/lib/socket';

export function BingoGame() {
  const { state } = useBingoEngine();
	const [displayState, setDisplayState] = useState(state);
	const socket = getSocket();

	useState(() => {
		socket.on("update-game-state", (data: any) => {
			console.log(data);
			setDisplayState(data);
		});
	});

  return (
    <div className="container mx-auto p-4 flex flex-col lg:flex-row gap-8">
      {/* Left Column */}
      <div className="w-full lg:w-2/3 flex flex-col gap-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <Caller
            currentNumber={displayState.currentNumber}
            calledNumbersHistory={displayState.calledNumbersHistory}
            isMuted={displayState.settings.isMuted}
          />
          <Prizes prizeWinners={displayState.prizeWinners} />
        </div>
        <div>
          <h2 className="text-2xl font-headline glow-primary mb-4">Your Tickets</h2>
          <ScrollArea className="h-[400px] w-full pr-4">
            <div className="grid grid-cols-1 gap-6">
              {displayState.tickets.map((ticket, index) => (
                <Ticket
                  key={index}
                  ticket={ticket}
                  calledNumbers={displayState.calledNumbers}
                  winner={displayState.prizeWinners.fullHouse?.ticketIndex === index}
                />
              ))}
            </div>
          </ScrollArea>
        </div>
      </div>

      {/* Right Column */}
      <div className="w-full lg:w-1/3 flex flex-col gap-8">
        <div className="flex-grow">
           <h2 className="text-2xl font-headline glow-primary mb-4">Number Board</h2>
          <BingoBoard calledNumbers={displayState.calledNumbers} />
        </div>
      </div>
    </div>
  );
}
