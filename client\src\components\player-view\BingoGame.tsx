"use client";

import React, { useState, useEffect } from 'react';
import { BingoState, useBingoEngine } from '@/hooks/use-bingo-engine';
import { Caller } from '@/components/game/Caller';
import { Prizes } from '@/components/game/Prizes';
import { Ticket } from '@/components/game/Ticket';
import { BingoBoard } from '@/components/game/BingoBoard';
import { ScrollArea } from '@/components/ui/scroll-area';
import { getSocket } from '@/lib/socket';
import { speak } from '@/lib/speech';
import PurchaseTicket from '../game/PurchaseTicket';
import { PlayerControls } from '../game/PlayerControl';

export function BingoGame() {
  const { state, dispatch } = useBingoEngine();
  const socket = getSocket();

  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') return;

    const handleGameStateUpdate = (data: BingoState) => {
      console.log('Received game state update:', data);
      if (data.currentNumber !== null)
        speak(`Number ${data.currentNumber}`, state.settings.isMaleVoice);
			dispatch({ type: 'UPDATE_EVERYTHING', payload: { state: data } });
    };

    socket.on("update-game-state", handleGameStateUpdate);

    // Cleanup function
    return () => {
      socket.off("update-game-state", handleGameStateUpdate);
    };
  }, []);

  return (
    <div className="container mx-auto p-4 flex flex-col lg:flex-row gap-8">
      {/* Left Column */}
      <div className="w-full lg:w-2/3 flex flex-col gap-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <Caller
            currentNumber={state.currentNumber}
            calledNumbersHistory={state.calledNumbersHistory}
            isMuted={state.settings.isMuted}
            isAdmin={false}
            isMale={state.settings.isMaleVoice}
          />
          <Prizes prizeWinners={state.prizeWinners} />
        </div>
        <div>
          <h2 className="text-2xl font-headline glow-primary mb-4">Your Tickets</h2>
          <ScrollArea className="h-[400px] w-full pr-4">
            {state.gameState === 'idle' && <PurchaseTicket />}
            <div className="grid grid-cols-1 gap-6">
              {state.tickets.map((ticket, index) => (
                ticket.belongsTo === socket.id && 
                <Ticket
                  key={index}
                  ticket={ticket}
                  calledNumbers={state.calledNumbersHistory}
                  winner={state.prizeWinners.fullHouse?.ticketIndex === index}
                />
              ))}
            </div>
          </ScrollArea>
        </div>
      </div>

      {/* Right Column */}
      <div className="w-full lg:w-1/3 flex flex-col gap-8">
        <PlayerControls />
        <div className="flex-grow">
           <h2 className="text-2xl font-headline glow-primary mb-4">Number Board</h2>
          <BingoBoard calledNumbers={state.calledNumbersHistory} />
        </div>
      </div>
    </div>
  );
}
