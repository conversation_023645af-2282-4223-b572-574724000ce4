"use client";

import React, { useEffect } from 'react';
import { useBingoEngine, BingoState } from '@/hooks/use-bingo-engine';
import { Caller } from '@/components/game/Caller';
import { Controls } from '@/components/game/Controls';
import { Prizes } from '@/components/game/Prizes';
import { Ticket } from '@/components/game/Ticket';
import { BingoBoard } from '@/components/game/BingoBoard';
import { ScrollArea } from '@/components/ui/scroll-area';
import { PlayerList } from './PlayerList';
import { getSocket } from '@/lib/socket';

export function BingoGame({playerList}: {playerList: Array<String>}) {
  const { state, dispatch } = useBingoEngine();
  const socket = getSocket();

  useEffect(() => {
    socket.on("update-game-state", (data: BingoState) => {
      console.log('Received game state update:', data);
      dispatch({ type: 'UPDATE_EVERYTHING', payload: { state: data } });
    });

    dispatch({ type: 'UPDATE_PLAYER', payload: { players: playerList } });
  }, []);

  return (
    <div className="container mx-auto p-4 flex flex-col lg:flex-row gap-8">
      {/* Left Column */}
      <div className="w-full lg:w-2/3 flex flex-col gap-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <Caller
            currentNumber={state.currentNumber}
            calledNumbersHistory={state.calledNumbersHistory}
            isMuted={state.settings.isMuted}
            isAdmin={true}
            isMale={state.settings.isMaleVoice}
          />
          <Prizes prizeWinners={state.prizeWinners} />
          <PlayerList players={playerList} />
        </div>
        <div>
          <h2 className="text-2xl font-headline glow-primary mb-4">Player Tickets</h2>
          <ScrollArea className="h-[400px] w-full pr-4">
            <div className="grid grid-cols-1 gap-6">
              {state.tickets.map((ticket, index) => (
                <>
                  <p>{ticket.belongsTo}</p>
                  <Ticket
                    key={index}
                    ticket={ticket}
                    calledNumbers={state.calledNumbersHistory}
                    winner={state.prizeWinners.fullHouse?.ticketIndex === index}
                  />
                </>
              ))}
            </div>
          </ScrollArea>
        </div>
      </div>

      {/* Right Column */}
      <div className="w-full lg:w-1/3 flex flex-col gap-8">
        <Controls state={state} dispatch={dispatch} />
        <div className="flex-grow">
           <h2 className="text-2xl font-headline glow-primary mb-4">Number Board</h2>
          <BingoBoard calledNumbers={state.calledNumbersHistory} />
        </div>
      </div>
    </div>
  );
}
