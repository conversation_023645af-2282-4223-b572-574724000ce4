{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/lib/speech.ts"], "sourcesContent": ["\"use client\"\r\n\r\nexport function speak(text: string, lang = 'en-US') {\r\n  if (typeof window === 'undefined' || !window.speechSynthesis) {\r\n    return;\r\n  }\r\n\r\n  // Cancel any previous speech\r\n  window.speechSynthesis.cancel();\r\n  \r\n  const utterance = new SpeechSynthesisUtterance(text);\r\n  utterance.lang = lang;\r\n  \r\n  // Optional: find a specific voice\r\n  // const voices = window.speechSynthesis.getVoices();\r\n  // utterance.voice = voices.find(v => v.name === 'Google UK English Male') || voices[0];\r\n  \r\n  window.speechSynthesis.speak(utterance);\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AAEO,SAAS,MAAM,IAAY,EAAE,OAAO,OAAO;IAChD,IAAI,aAAkB,eAAe,CAAC,OAAO,eAAe,EAAE;QAC5D;IACF;IAEA,6BAA6B;IAC7B,OAAO,eAAe,CAAC,MAAM;IAE7B,MAAM,YAAY,IAAI,yBAAyB;IAC/C,UAAU,IAAI,GAAG;IAEjB,kCAAkC;IAClC,qDAAqD;IACrD,wFAAwF;IAExF,OAAO,eAAe,CAAC,KAAK,CAAC;AAC/B", "debugId": null}}, {"offset": {"line": 136, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/components/game/Caller.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect } from 'react';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { speak } from '@/lib/speech';\r\n\r\ntype CallerProps = {\r\n  currentNumber: number | null;\r\n  calledNumbersHistory: number[];\r\n  isMuted: boolean;\r\n};\r\n\r\nexport function Caller({ currentNumber, calledNumbersHistory, isMuted }: CallerProps) {\r\n  useEffect(() => {\r\n    if (currentNumber !== null && !isMuted) {\r\n      speak(`Number ${currentNumber}`);\r\n    }\r\n  }, [currentNumber, isMuted]);\r\n\r\n  return (\r\n    <Card className=\"text-center bg-card/50 backdrop-blur-sm h-full\">\r\n      <CardHeader>\r\n        <CardTitle className=\"font-headline text-2xl glow-primary\">Caller</CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"flex flex-col items-center justify-center gap-4\">\r\n        <div className=\"relative w-40 h-40 flex items-center justify-center\">\r\n          <div className=\"absolute inset-0 bg-primary/20 rounded-full animate-pulse blur-xl\"></div>\r\n          <div className=\"relative z-10 flex items-center justify-center w-36 h-36 bg-background rounded-full border-4 border-primary\">\r\n            {currentNumber !== null ? (\r\n              <span key={currentNumber} className=\"font-headline text-7xl caller-animation text-accent\">\r\n                {currentNumber}\r\n              </span>\r\n            ) : (\r\n              <span className=\"text-muted-foreground\">-</span>\r\n            )}\r\n          </div>\r\n        </div>\r\n        <div className=\"flex gap-2 mt-2\">\r\n          <span className=\"text-muted-foreground\">History:</span>\r\n          {calledNumbersHistory.slice(0, 5).map((num, index) => (\r\n            <span\r\n              key={index}\r\n              className=\"flex items-center justify-center w-8 h-8 rounded-full bg-secondary text-secondary-foreground font-bold\"\r\n              style={{ opacity: 1 - index * 0.2 }}\r\n            >\r\n              {num}\r\n            </span>\r\n          ))}\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAYO,SAAS,OAAO,EAAE,aAAa,EAAE,oBAAoB,EAAE,OAAO,EAAe;;IAClF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,IAAI,kBAAkB,QAAQ,CAAC,SAAS;gBACtC,CAAA,GAAA,uHAAA,CAAA,QAAK,AAAD,EAAE,CAAC,OAAO,EAAE,eAAe;YACjC;QACF;2BAAG;QAAC;QAAe;KAAQ;IAE3B,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oBAAC,WAAU;8BAAsC;;;;;;;;;;;0BAE7D,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;0CACZ,kBAAkB,qBACjB,6LAAC;oCAAyB,WAAU;8CACjC;mCADQ;;;;yDAIX,6LAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;;;;;;kCAI9C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAwB;;;;;;4BACvC,qBAAqB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBAC1C,6LAAC;oCAEC,WAAU;oCACV,OAAO;wCAAE,SAAS,IAAI,QAAQ;oCAAI;8CAEjC;mCAJI;;;;;;;;;;;;;;;;;;;;;;;AAWnB;GAxCgB;KAAA", "debugId": null}}, {"offset": {"line": 273, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,4VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 340, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n)\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\r\n    VariantProps<typeof labelVariants>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(labelVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG,oKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 379, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/components/ui/slider.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SliderPrimitive from \"@radix-ui/react-slider\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Slider = React.forwardRef<\r\n  React.ElementRef<typeof SliderPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <SliderPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex w-full touch-none select-none items-center\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <SliderPrimitive.Track className=\"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary\">\r\n      <SliderPrimitive.Range className=\"absolute h-full bg-primary\" />\r\n    </SliderPrimitive.Track>\r\n    <SliderPrimitive.Thumb className=\"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\" />\r\n  </SliderPrimitive.Root>\r\n))\r\nSlider.displayName = SliderPrimitive.Root.displayName\r\n\r\nexport { Slider }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;0BAET,6LAAC,qKAAA,CAAA,QAAqB;gBAAC,WAAU;0BAC/B,cAAA,6LAAC,qKAAA,CAAA,QAAqB;oBAAC,WAAU;;;;;;;;;;;0BAEnC,6LAAC,qKAAA,CAAA,QAAqB;gBAAC,WAAU;;;;;;;;;;;;;AAGrC,OAAO,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 438, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Switch = React.forwardRef<\r\n  React.ElementRef<typeof SwitchPrimitives.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <SwitchPrimitives.Root\r\n    className={cn(\r\n      \"peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  >\r\n    <SwitchPrimitives.Thumb\r\n      className={cn(\r\n        \"pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0\"\r\n      )}\r\n    />\r\n  </SwitchPrimitives.Root>\r\n))\r\nSwitch.displayName = SwitchPrimitives.Root.displayName\r\n\r\nexport { Switch }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,OAAqB;QACpB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sXACA;QAED,GAAG,KAAK;QACT,KAAK;kBAEL,cAAA,6LAAC,qKAAA,CAAA,QAAsB;YACrB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;;AAKR,OAAO,WAAW,GAAG,qKAAA,CAAA,OAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 481, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nInput.displayName = \"Input\"\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kYACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 517, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/components/game/Controls.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport type { BingoState, BingoAction } from '@/hooks/use-bingo-engine';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Slider } from '@/components/ui/slider';\r\nimport { Switch } from '@/components/ui/switch';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Play, Pause, RefreshCw, Settings, Volume2, VolumeX } from 'lucide-react';\r\n\r\ntype ControlsProps = {\r\n  state: BingoState;\r\n  dispatch: React.Dispatch<BingoAction>;\r\n};\r\n\r\nexport function Controls({ state, dispatch }: ControlsProps) {\r\n  const { gameState, settings } = state;\r\n\r\n  return (\r\n    <Card className=\"bg-card/50 backdrop-blur-sm\">\r\n      <CardHeader>\r\n        <CardTitle className=\"font-headline text-2xl flex items-center gap-2 glow-primary\">\r\n          <Settings className=\"w-6 h-6\" />\r\n          Admin Controls\r\n        </CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-6\">\r\n        <div className=\"grid grid-cols-3 gap-2\">\r\n          {gameState === 'idle' || gameState === 'ended' ? (\r\n            <Button\r\n              className=\"w-full\"\r\n              onClick={() => dispatch({ type: 'START_GAME' })}\r\n            >\r\n              <Play className=\"mr-2 h-4 w-4\" /> Start\r\n            </Button>\r\n          ) : (\r\n            <Button\r\n              className=\"w-full\"\r\n              variant={gameState === 'paused' ? 'default' : 'secondary'}\r\n              onClick={() => dispatch({ type: 'PAUSE_GAME' })}\r\n            >\r\n              {gameState === 'paused' ? <Play className=\"mr-2 h-4 w-4\" /> : <Pause className=\"mr-2 h-4 w-4\" />}\r\n              {gameState === 'paused' ? 'Resume' : 'Pause'}\r\n            </Button>\r\n          )}\r\n\r\n          <Button\r\n            className=\"w-full\"\r\n            variant=\"destructive\"\r\n            onClick={() => dispatch({ type: 'RESET_GAME' })}\r\n          >\r\n            <RefreshCw className=\"mr-2 h-4 w-4\" /> Reset\r\n          </Button>\r\n          \r\n          <Button\r\n            className=\"w-full\"\r\n            variant=\"outline\"\r\n            disabled={gameState !== 'running'}\r\n            onClick={() => dispatch({ type: 'CALL_NUMBER' })}\r\n          >\r\n            Call Next\r\n          </Button>\r\n        </div>\r\n        <div className=\"space-y-4\">\r\n          <div className=\"space-y-2\">\r\n            <Label htmlFor=\"num-tickets\">Number of Tickets</Label>\r\n            <Input\r\n              id=\"num-tickets\"\r\n              type=\"number\"\r\n              value={settings.numTickets}\r\n              onChange={(e) =>\r\n                dispatch({\r\n                  type: 'UPDATE_SETTINGS',\r\n                  payload: { numTickets: parseInt(e.target.value, 10) || 1 },\r\n                })\r\n              }\r\n              disabled={gameState !== 'idle'}\r\n            />\r\n          </div>\r\n          <div className=\"space-y-2\">\r\n            <Label htmlFor=\"speed\">Auto-call Speed (seconds)</Label>\r\n            <Slider\r\n              id=\"speed\"\r\n              min={1}\r\n              max={10}\r\n              step={0.5}\r\n              value={[settings.speed / 1000]}\r\n              onValueChange={([val]) =>\r\n                dispatch({\r\n                  type: 'UPDATE_SETTINGS',\r\n                  payload: { speed: val * 1000 },\r\n                })\r\n              }\r\n            />\r\n          </div>\r\n          <div className=\"flex items-center justify-between\">\r\n            <Label htmlFor=\"auto-call\" className=\"flex items-center\">\r\n              Auto-call Numbers\r\n            </Label>\r\n            <Switch\r\n              id=\"auto-call\"\r\n              checked={settings.isAutoPlay}\r\n              onCheckedChange={(checked) =>\r\n                dispatch({\r\n                  type: 'UPDATE_SETTINGS',\r\n                  payload: { isAutoPlay: checked },\r\n                })\r\n              }\r\n            />\r\n          </div>\r\n           <div className=\"flex items-center justify-between\">\r\n            <Label htmlFor=\"sound-toggle\" className=\"flex items-center gap-2\">\r\n               {settings.isMuted ? <VolumeX className=\"w-5 h-5\" /> : <Volume2 className=\"w-5 h-5\" />}\r\n              Game Sounds\r\n            </Label>\r\n            <Switch\r\n              id=\"sound-toggle\"\r\n              checked={!settings.isMuted}\r\n              onCheckedChange={(checked) =>\r\n                dispatch({\r\n                  type: 'UPDATE_SETTINGS',\r\n                  payload: { isMuted: !checked },\r\n                })\r\n              }\r\n            />\r\n          </div>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAVA;;;;;;;;;AAiBO,SAAS,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAiB;IACzD,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG;IAEhC,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,6LAAC,6MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAAY;;;;;;;;;;;;0BAIpC,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,6LAAC;wBAAI,WAAU;;4BACZ,cAAc,UAAU,cAAc,wBACrC,6LAAC,qIAAA,CAAA,SAAM;gCACL,WAAU;gCACV,SAAS,IAAM,SAAS;wCAAE,MAAM;oCAAa;;kDAE7C,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;qDAGnC,6LAAC,qIAAA,CAAA,SAAM;gCACL,WAAU;gCACV,SAAS,cAAc,WAAW,YAAY;gCAC9C,SAAS,IAAM,SAAS;wCAAE,MAAM;oCAAa;;oCAE5C,cAAc,yBAAW,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;6DAAoB,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAC9E,cAAc,WAAW,WAAW;;;;;;;0CAIzC,6LAAC,qIAAA,CAAA,SAAM;gCACL,WAAU;gCACV,SAAQ;gCACR,SAAS,IAAM,SAAS;wCAAE,MAAM;oCAAa;;kDAE7C,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGxC,6LAAC,qIAAA,CAAA,SAAM;gCACL,WAAU;gCACV,SAAQ;gCACR,UAAU,cAAc;gCACxB,SAAS,IAAM,SAAS;wCAAE,MAAM;oCAAc;0CAC/C;;;;;;;;;;;;kCAIH,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAc;;;;;;kDAC7B,6LAAC,oIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,UAAU;wCAC1B,UAAU,CAAC,IACT,SAAS;gDACP,MAAM;gDACN,SAAS;oDAAE,YAAY,SAAS,EAAE,MAAM,CAAC,KAAK,EAAE,OAAO;gDAAE;4CAC3D;wCAEF,UAAU,cAAc;;;;;;;;;;;;0CAG5B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAQ;;;;;;kDACvB,6LAAC,qIAAA,CAAA,SAAM;wCACL,IAAG;wCACH,KAAK;wCACL,KAAK;wCACL,MAAM;wCACN,OAAO;4CAAC,SAAS,KAAK,GAAG;yCAAK;wCAC9B,eAAe,CAAC,CAAC,IAAI,GACnB,SAAS;gDACP,MAAM;gDACN,SAAS;oDAAE,OAAO,MAAM;gDAAK;4CAC/B;;;;;;;;;;;;0CAIN,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;kDAAoB;;;;;;kDAGzD,6LAAC,qIAAA,CAAA,SAAM;wCACL,IAAG;wCACH,SAAS,SAAS,UAAU;wCAC5B,iBAAiB,CAAC,UAChB,SAAS;gDACP,MAAM;gDACN,SAAS;oDAAE,YAAY;gDAAQ;4CACjC;;;;;;;;;;;;0CAIL,6LAAC;gCAAI,WAAU;;kDACd,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAe,WAAU;;4CACpC,SAAS,OAAO,iBAAG,6LAAC,+MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;qEAAe,6LAAC,+MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAa;;;;;;;kDAGzF,6LAAC,qIAAA,CAAA,SAAM;wCACL,IAAG;wCACH,SAAS,CAAC,SAAS,OAAO;wCAC1B,iBAAiB,CAAC,UAChB,SAAS;gDACP,MAAM;gDACN,SAAS;oDAAE,SAAS,CAAC;gDAAQ;4CAC/B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB;KAnHgB", "debugId": null}}, {"offset": {"line": 844, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Separator = React.forwardRef<\r\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\r\n>(\r\n  (\r\n    { className, orientation = \"horizontal\", decorative = true, ...props },\r\n    ref\r\n  ) => (\r\n    <SeparatorPrimitive.Root\r\n      ref={ref}\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"shrink-0 bg-border\",\r\n        orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n)\r\nSeparator.displayName = SeparatorPrimitive.Root.displayName\r\n\r\nexport { Separator }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAI/B,CACE,EAAE,SAAS,EAAE,cAAc,YAAY,EAAE,aAAa,IAAI,EAAE,GAAG,OAAO,EACtE,oBAEA,6LAAC,wKAAA,CAAA,OAAuB;QACtB,KAAK;QACL,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,eAAe,mBAAmB,kBAClD;QAED,GAAG,KAAK;;;;;;;AAIf,UAAU,WAAW,GAAG,wKAAA,CAAA,OAAuB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 882, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/lib/bingo.ts"], "sourcesContent": ["export type Ticket = {\r\n  content: (number | null)[][],\r\n  belongsTo: string;\r\n};\r\nexport type Prize = 'earlyFive' | 'topLine' | 'middleLine' | 'bottomLine' | 'fullHouse';\r\nexport type PrizeWinner = { ticketIndex: number };\r\n\r\nexport const PRIZES: Record<Prize, string> = {\r\n  earlyFive: 'Early Five',\r\n  topLine: 'Top Line',\r\n  middleLine: 'Middle Line',\r\n  bottomLine: 'Bottom Line',\r\n  fullHouse: 'Full House',\r\n};\r\n\r\nfunction shuffle<T>(array: T[]): T[] {\r\n  let currentIndex = array.length,  randomIndex;\r\n  while (currentIndex !== 0) {\r\n    randomIndex = Math.floor(Math.random() * currentIndex);\r\n    currentIndex--;\r\n    [array[currentIndex], array[randomIndex]] = [array[randomIndex], array[currentIndex]];\r\n  }\r\n  return array;\r\n}\r\n\r\nexport function generateTickets(numTickets: number, belongsTo: string): Ticket[] {\r\n  const tickets: Ticket[] = [];\r\n  for (let i = 0; i < numTickets; i++) {\r\n    tickets.push(generateSingleTicket(belongsTo));\r\n  }\r\n  return tickets;\r\n}\r\n\r\nfunction generateSingleTicket(belongsTo: string): Ticket {\r\n  const ticket: Ticket = { content: Array.from({ length: 3 }, () => Array(9).fill(null)), belongsTo };\r\n  const numbers = new Set<number>();\r\n\r\n  // Determine column fills: 6 columns get 1 number, 3 columns get 2 numbers\r\n  const columns = shuffle(Array.from({length: 9}, (_, i) => i));\r\n  const colsWithTwo = columns.slice(0, 3);\r\n  const colsWithOne = columns.slice(3, 9);\r\n  \r\n  const colFills: number[] = Array(9).fill(0);\r\n  colsWithTwo.forEach(c => colFills[c] = 2);\r\n  colsWithOne.forEach(c => colFills[c] = 1);\r\n\r\n  // Place numbers\r\n  for (let col = 0; col < 9; col++) {\r\n    const numbersInCol = colFills[col];\r\n    const min = col * 10 + 1;\r\n    const max = col * 10 + 10;\r\n    const availableNumbers = shuffle(Array.from({ length: max - min }, (_, i) => min + i));\r\n    const rows = shuffle([0, 1, 2]);\r\n\r\n    for (let i = 0; i < numbersInCol; i++) {\r\n        ticket.content[rows[i]][col] = availableNumbers[i];\r\n    }\r\n  }\r\n\r\n  // Ensure each row has 5 numbers\r\n  for (let row = 0; row < 3; row++) {\r\n    let numbersInRow = ticket.content[row].filter(n => n !== null).length;\r\n    while (numbersInRow < 5) {\r\n        let emptyCol = -1;\r\n        // find an empty column in this row that can accept a number\r\n        const potentialCols = shuffle(Array.from({length: 9}, (_, i) => i));\r\n        for(const col of potentialCols) {\r\n            if(ticket.content[row][col] === null && colFills[col] < 3) {\r\n                emptyCol = col;\r\n                break;\r\n            }\r\n        }\r\n\r\n        if(emptyCol !== -1) {\r\n            const min = emptyCol * 10 + 1;\r\n            const max = emptyCol * 10 + 10;\r\n            const availableNumbers = shuffle(Array.from({ length: max - min }, (_, i) => min + i));\r\n\r\n            let numToAdd;\r\n            let attempt = 0;\r\n            do {\r\n                numToAdd = availableNumbers[attempt++];\r\n            } while (isNumberInTicket(ticket, numToAdd) && attempt < availableNumbers.length);\r\n            \r\n            if (numToAdd && !isNumberInTicket(ticket, numToAdd)) {\r\n                ticket.content[row][emptyCol] = numToAdd;\r\n                colFills[emptyCol]++;\r\n                numbersInRow++;\r\n            }\r\n        } else {\r\n            // Failsafe, should not be hit in normal generation\r\n            break;\r\n        }\r\n    }\r\n  }\r\n\r\n  return ticket;\r\n}\r\n\r\nfunction isNumberInTicket(ticket: Ticket, num: number): boolean {\r\n    return ticket.content.some(row => row.includes(num));\r\n}\r\n\r\n\r\nexport function checkPrizes(ticket: Ticket, calledNumbers: Set<number>): Partial<Record<Prize, boolean>> {\r\n  const results: Partial<Record<Prize, boolean>> = {};\r\n  const ticketNumbers = ticket.content.flat().filter((n): n is number => n !== null);\r\n\r\n  const markedCount = ticketNumbers.filter(n => calledNumbers.has(n)).length;\r\n\r\n  // Early Five\r\n  if (markedCount >= 5) {\r\n    results.earlyFive = true;\r\n  }\r\n\r\n  // Top Line\r\n  const topLineNumbers = ticket.content[0].filter((n): n is number => n !== null);\r\n  if (topLineNumbers.every(n => calledNumbers.has(n))) {\r\n    results.topLine = true;\r\n  }\r\n\r\n  // Middle Line\r\n  const middleLineNumbers = ticket.content[1].filter((n): n is number => n !== null);\r\n  if (middleLineNumbers.every(n => calledNumbers.has(n))) {\r\n    results.middleLine = true;\r\n  }\r\n\r\n  // Bottom Line\r\n  const bottomLineNumbers = ticket.content[2].filter((n): n is number => n !== null);\r\n  if (bottomLineNumbers.every(n => calledNumbers.has(n))) {\r\n    results.bottomLine = true;\r\n  }\r\n  \r\n  // Full House\r\n  if (ticketNumbers.every(n => calledNumbers.has(n))) {\r\n    results.fullHouse = true;\r\n  }\r\n\r\n  return results;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAOO,MAAM,SAAgC;IAC3C,WAAW;IACX,SAAS;IACT,YAAY;IACZ,YAAY;IACZ,WAAW;AACb;AAEA,SAAS,QAAW,KAAU;IAC5B,IAAI,eAAe,MAAM,MAAM,EAAG;IAClC,MAAO,iBAAiB,EAAG;QACzB,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;QACzC;QACA,CAAC,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,YAAY,CAAC,GAAG;YAAC,KAAK,CAAC,YAAY;YAAE,KAAK,CAAC,aAAa;SAAC;IACvF;IACA,OAAO;AACT;AAEO,SAAS,gBAAgB,UAAkB,EAAE,SAAiB;IACnE,MAAM,UAAoB,EAAE;IAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;QACnC,QAAQ,IAAI,CAAC,qBAAqB;IACpC;IACA,OAAO;AACT;AAEA,SAAS,qBAAqB,SAAiB;IAC7C,MAAM,SAAiB;QAAE,SAAS,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,IAAM,MAAM,GAAG,IAAI,CAAC;QAAQ;IAAU;IAClG,MAAM,UAAU,IAAI;IAEpB,0EAA0E;IAC1E,MAAM,UAAU,QAAQ,MAAM,IAAI,CAAC;QAAC,QAAQ;IAAC,GAAG,CAAC,GAAG,IAAM;IAC1D,MAAM,cAAc,QAAQ,KAAK,CAAC,GAAG;IACrC,MAAM,cAAc,QAAQ,KAAK,CAAC,GAAG;IAErC,MAAM,WAAqB,MAAM,GAAG,IAAI,CAAC;IACzC,YAAY,OAAO,CAAC,CAAA,IAAK,QAAQ,CAAC,EAAE,GAAG;IACvC,YAAY,OAAO,CAAC,CAAA,IAAK,QAAQ,CAAC,EAAE,GAAG;IAEvC,gBAAgB;IAChB,IAAK,IAAI,MAAM,GAAG,MAAM,GAAG,MAAO;QAChC,MAAM,eAAe,QAAQ,CAAC,IAAI;QAClC,MAAM,MAAM,MAAM,KAAK;QACvB,MAAM,MAAM,MAAM,KAAK;QACvB,MAAM,mBAAmB,QAAQ,MAAM,IAAI,CAAC;YAAE,QAAQ,MAAM;QAAI,GAAG,CAAC,GAAG,IAAM,MAAM;QACnF,MAAM,OAAO,QAAQ;YAAC;YAAG;YAAG;SAAE;QAE9B,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,IAAK;YACnC,OAAO,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,GAAG,gBAAgB,CAAC,EAAE;QACtD;IACF;IAEA,gCAAgC;IAChC,IAAK,IAAI,MAAM,GAAG,MAAM,GAAG,MAAO;QAChC,IAAI,eAAe,OAAO,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM,MAAM,MAAM;QACrE,MAAO,eAAe,EAAG;YACrB,IAAI,WAAW,CAAC;YAChB,4DAA4D;YAC5D,MAAM,gBAAgB,QAAQ,MAAM,IAAI,CAAC;gBAAC,QAAQ;YAAC,GAAG,CAAC,GAAG,IAAM;YAChE,KAAI,MAAM,OAAO,cAAe;gBAC5B,IAAG,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,QAAQ,CAAC,IAAI,GAAG,GAAG;oBACvD,WAAW;oBACX;gBACJ;YACJ;YAEA,IAAG,aAAa,CAAC,GAAG;gBAChB,MAAM,MAAM,WAAW,KAAK;gBAC5B,MAAM,MAAM,WAAW,KAAK;gBAC5B,MAAM,mBAAmB,QAAQ,MAAM,IAAI,CAAC;oBAAE,QAAQ,MAAM;gBAAI,GAAG,CAAC,GAAG,IAAM,MAAM;gBAEnF,IAAI;gBACJ,IAAI,UAAU;gBACd,GAAG;oBACC,WAAW,gBAAgB,CAAC,UAAU;gBAC1C,QAAS,iBAAiB,QAAQ,aAAa,UAAU,iBAAiB,MAAM,CAAE;gBAElF,IAAI,YAAY,CAAC,iBAAiB,QAAQ,WAAW;oBACjD,OAAO,OAAO,CAAC,IAAI,CAAC,SAAS,GAAG;oBAChC,QAAQ,CAAC,SAAS;oBAClB;gBACJ;YACJ,OAAO;gBAEH;YACJ;QACJ;IACF;IAEA,OAAO;AACT;AAEA,SAAS,iBAAiB,MAAc,EAAE,GAAW;IACjD,OAAO,OAAO,OAAO,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,QAAQ,CAAC;AACnD;AAGO,SAAS,YAAY,MAAc,EAAE,aAA0B;IACpE,MAAM,UAA2C,CAAC;IAClD,MAAM,gBAAgB,OAAO,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC,IAAmB,MAAM;IAE7E,MAAM,cAAc,cAAc,MAAM,CAAC,CAAA,IAAK,cAAc,GAAG,CAAC,IAAI,MAAM;IAE1E,aAAa;IACb,IAAI,eAAe,GAAG;QACpB,QAAQ,SAAS,GAAG;IACtB;IAEA,WAAW;IACX,MAAM,iBAAiB,OAAO,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAmB,MAAM;IAC1E,IAAI,eAAe,KAAK,CAAC,CAAA,IAAK,cAAc,GAAG,CAAC,KAAK;QACnD,QAAQ,OAAO,GAAG;IACpB;IAEA,cAAc;IACd,MAAM,oBAAoB,OAAO,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAmB,MAAM;IAC7E,IAAI,kBAAkB,KAAK,CAAC,CAAA,IAAK,cAAc,GAAG,CAAC,KAAK;QACtD,QAAQ,UAAU,GAAG;IACvB;IAEA,cAAc;IACd,MAAM,oBAAoB,OAAO,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAmB,MAAM;IAC7E,IAAI,kBAAkB,KAAK,CAAC,CAAA,IAAK,cAAc,GAAG,CAAC,KAAK;QACtD,QAAQ,UAAU,GAAG;IACvB;IAEA,aAAa;IACb,IAAI,cAAc,KAAK,CAAC,CAAA,IAAK,cAAc,GAAG,CAAC,KAAK;QAClD,QAAQ,SAAS,GAAG;IACtB;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1026, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/components/game/Prizes.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Separator } from '@/components/ui/separator';\r\nimport { Trophy, CheckCircle2 } from 'lucide-react';\r\nimport type { Prize, PrizeWinner } from '@/lib/bingo';\r\nimport { PRIZES } from '@/lib/bingo';\r\nimport { cn } from '@/lib/utils';\r\n\r\n\r\ntype PrizesProps = {\r\n  prizeWinners: Partial<Record<Prize, PrizeWinner>>;\r\n};\r\n\r\nexport function Prizes({ prizeWinners }: PrizesProps) {\r\n  return (\r\n    <Card className=\"bg-card/50 backdrop-blur-sm h-full\">\r\n      <CardHeader>\r\n        <CardTitle className=\"font-headline text-2xl flex items-center gap-2 glow-primary\">\r\n          <Trophy className=\"w-6 h-6\" />\r\n          Prizes\r\n        </CardTitle>\r\n      </CardHeader>\r\n      <CardContent>\r\n        <ul className=\"space-y-3\">\r\n          {(Object.keys(PRIZES) as Prize[]).map((prizeKey, index) => {\r\n            const winner = prizeWinners[prizeKey];\r\n            const isWon = !!winner;\r\n            return (\r\n              <React.Fragment key={prizeKey}>\r\n                <li\r\n                  className={cn(\r\n                    'flex justify-between items-center transition-all duration-300',\r\n                    isWon ? 'text-accent' : 'text-foreground'\r\n                  )}\r\n                >\r\n                  <span className=\"font-bold text-lg\">{PRIZES[prizeKey]}</span>\r\n                  {isWon ? (\r\n                    <div className=\"flex items-center gap-2 text-sm\">\r\n                      <CheckCircle2 className=\"w-5 h-5\" />\r\n                      <span>Ticket #{winner.ticketIndex + 1}</span>\r\n                    </div>\r\n                  ) : (\r\n                    <span className=\"text-sm text-muted-foreground\">Pending</span>\r\n                  )}\r\n                </li>\r\n                {index < Object.keys(PRIZES).length - 1 && <Separator />}\r\n              </React.Fragment>\r\n            );\r\n          })}\r\n        </ul>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAEA;AACA;AARA;;;;;;;;AAeO,SAAS,OAAO,EAAE,YAAY,EAAe;IAClD,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBAAY;;;;;;;;;;;;0BAIlC,6LAAC,mIAAA,CAAA,cAAW;0BACV,cAAA,6LAAC;oBAAG,WAAU;8BACX,AAAC,OAAO,IAAI,CAAC,sHAAA,CAAA,SAAM,EAAc,GAAG,CAAC,CAAC,UAAU;wBAC/C,MAAM,SAAS,YAAY,CAAC,SAAS;wBACrC,MAAM,QAAQ,CAAC,CAAC;wBAChB,qBACE,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;;8CACb,6LAAC;oCACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA,QAAQ,gBAAgB;;sDAG1B,6LAAC;4CAAK,WAAU;sDAAqB,sHAAA,CAAA,SAAM,CAAC,SAAS;;;;;;wCACpD,sBACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,wNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;8DACxB,6LAAC;;wDAAK;wDAAS,OAAO,WAAW,GAAG;;;;;;;;;;;;iEAGtC,6LAAC;4CAAK,WAAU;sDAAgC;;;;;;;;;;;;gCAGnD,QAAQ,OAAO,IAAI,CAAC,sHAAA,CAAA,SAAM,EAAE,MAAM,GAAG,mBAAK,6LAAC,wIAAA,CAAA,YAAS;;;;;;2BAjBlC;;;;;oBAoBzB;;;;;;;;;;;;;;;;;AAKV;KAxCgB", "debugId": null}}, {"offset": {"line": 1171, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/components/game/Ticket.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport type { Ticket as TicketType } from '@/lib/bingo';\r\nimport { cn } from '@/lib/utils';\r\nimport { motion } from 'framer-motion';\r\n\r\ntype TicketProps = {\r\n  ticket: TicketType;\r\n  calledNumbers: Set<number>;\r\n  winner?: boolean;\r\n};\r\n\r\nexport function Ticket({ ticket, calledNumbers, winner = false }: TicketProps) {\r\n  return (\r\n    <div\r\n      className={cn(\r\n        'p-4 rounded-lg border-2 transition-all duration-500',\r\n        winner\r\n          ? 'bg-primary/20 border-primary shadow-2xl shadow-primary/50'\r\n          : 'bg-card/80 border-border'\r\n      )}\r\n    >\r\n      <div className=\"grid grid-cols-9 gap-1\">\r\n        {ticket.flat().map((number, index) => {\r\n          const isCalled = number !== null && calledNumbers.has(number);\r\n          return (\r\n            <div\r\n              key={index}\r\n              className={cn(\r\n                'aspect-square flex items-center justify-center rounded-md font-bold text-lg transition-all duration-300',\r\n                number === null ? 'bg-transparent' : 'bg-background/50',\r\n                isCalled &&\r\n                  'bg-accent text-accent-foreground scale-110 rotate-12 shadow-lg shadow-accent/50'\r\n              )}\r\n            >\r\n              {number}\r\n            </div>\r\n          );\r\n        })}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAIA;AAJA;;;AAaO,SAAS,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,SAAS,KAAK,EAAe;IAC3E,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uDACA,SACI,8DACA;kBAGN,cAAA,6LAAC;YAAI,WAAU;sBACZ,OAAO,IAAI,GAAG,GAAG,CAAC,CAAC,QAAQ;gBAC1B,MAAM,WAAW,WAAW,QAAQ,cAAc,GAAG,CAAC;gBACtD,qBACE,6LAAC;oBAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2GACA,WAAW,OAAO,mBAAmB,oBACrC,YACE;8BAGH;mBARI;;;;;YAWX;;;;;;;;;;;AAIR;KA9BgB", "debugId": null}}, {"offset": {"line": 1218, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/components/game/BingoBoard.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { Card, CardContent } from '@/components/ui/card';\r\nimport { cn } from '@/lib/utils';\r\n\r\ntype BingoBoardProps = {\r\n  calledNumbers: Set<number>;\r\n};\r\n\r\nexport function BingoBoard({ calledNumbers }: BingoBoardProps) {\r\n  const numbers = Array.from({ length: 90 }, (_, i) => i + 1);\r\n\r\n  return (\r\n    <Card className=\"bg-card/50 backdrop-blur-sm\">\r\n      <CardContent className=\"p-4\">\r\n        <div className=\"grid grid-cols-10 md:grid-cols-15 lg:grid-cols-18 xl:grid-cols-20 gap-2\">\r\n          {numbers.map((number) => {\r\n            const isCalled = calledNumbers.has(number);\r\n            return (\r\n              <div\r\n                key={number}\r\n                className={cn(\r\n                  'flex items-center justify-center aspect-square rounded-full text-sm md:text-base font-bold transition-all duration-300',\r\n                  isCalled\r\n                    ? 'bg-primary text-primary-foreground scale-110 shadow-lg shadow-primary/50'\r\n                    : 'bg-secondary text-secondary-foreground'\r\n                )}\r\n              >\r\n                {number}\r\n              </div>\r\n            );\r\n          })}\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAUO,SAAS,WAAW,EAAE,aAAa,EAAmB;IAC3D,MAAM,UAAU,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAG,GAAG,CAAC,GAAG,IAAM,IAAI;IAEzD,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;kBACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,6LAAC;gBAAI,WAAU;0BACZ,QAAQ,GAAG,CAAC,CAAC;oBACZ,MAAM,WAAW,cAAc,GAAG,CAAC;oBACnC,qBACE,6LAAC;wBAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0HACA,WACI,6EACA;kCAGL;uBARI;;;;;gBAWX;;;;;;;;;;;;;;;;AAKV;KA3BgB", "debugId": null}}, {"offset": {"line": 1277, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst ScrollArea = React.forwardRef<\r\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\r\n>(({ className, children, ...props }, ref) => (\r\n  <ScrollAreaPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\"relative overflow-hidden\", className)}\r\n    {...props}\r\n  >\r\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\r\n      {children}\r\n    </ScrollAreaPrimitive.Viewport>\r\n    <ScrollBar />\r\n    <ScrollAreaPrimitive.Corner />\r\n  </ScrollAreaPrimitive.Root>\r\n))\r\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\r\n\r\nconst ScrollBar = React.forwardRef<\r\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\r\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\r\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\r\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\r\n    ref={ref}\r\n    orientation={orientation}\r\n    className={cn(\r\n      \"flex touch-none select-none transition-colors\",\r\n      orientation === \"vertical\" &&\r\n        \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\r\n      orientation === \"horizontal\" &&\r\n        \"h-2.5 flex-col border-t border-t-transparent p-[1px]\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\r\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\r\n))\r\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\r\n\r\nexport { ScrollArea, ScrollBar }\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,6KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;0BAET,6LAAC,6KAAA,CAAA,WAA4B;gBAAC,WAAU;0BACrC;;;;;;0BAEH,6LAAC;;;;;0BACD,6LAAC,6KAAA,CAAA,SAA0B;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,6KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO,EAAE,oBACpD,6LAAC,6KAAA,CAAA,sBAAuC;QACtC,KAAK;QACL,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cACd,sDACF,gBAAgB,gBACd,wDACF;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,6KAAA,CAAA,kBAAmC;YAAC,WAAU;;;;;;;;;;;MAjB7C;AAoBN,UAAU,WAAW,GAAG,6KAAA,CAAA,sBAAuC,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1354, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/components/game/PlayerList.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Separator } from '@/components/ui/separator';\r\nimport { PersonStanding } from 'lucide-react';\r\nimport { cn } from '@/lib/utils';\r\n\r\nexport function PlayerList({ players }: { players: Array<String> }) {\r\n  return (\r\n    <Card className=\"bg-card/50 backdrop-blur-sm h-full\">\r\n      <CardHeader>\r\n        <CardTitle className=\"font-headline text-2xl flex items-center gap-2 glow-primary\">\r\n          <PersonStanding className='w-6 h-6' />\r\n          Players\r\n        </CardTitle>\r\n      </CardHeader>\r\n      <CardContent>\r\n        <ul className=\"space-y-3\">\r\n          {players.map((player, index) => (\r\n            <li className={cn('transition-all duration-300')} key={index}>\r\n              <span className='font-bold text-lg'>\r\n                {player}\r\n              </span>\r\n              {index < players.length - 1 && <Separator className='mt-2' />}\r\n            </li>\r\n          ))}\r\n        </ul>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AANA;;;;;;AAQO,SAAS,WAAW,EAAE,OAAO,EAA8B;IAChE,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,6LAAC,6NAAA,CAAA,iBAAc;4BAAC,WAAU;;;;;;wBAAY;;;;;;;;;;;;0BAI1C,6LAAC,mIAAA,CAAA,cAAW;0BACV,cAAA,6LAAC;oBAAG,WAAU;8BACX,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,6LAAC;4BAAG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;;8CAChB,6LAAC;oCAAK,WAAU;8CACb;;;;;;gCAEF,QAAQ,QAAQ,MAAM,GAAG,mBAAK,6LAAC,wIAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;2BAJC;;;;;;;;;;;;;;;;;;;;;AAWnE;KAvBgB", "debugId": null}}, {"offset": {"line": 1451, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/components/game/BingoGame.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { useBingoEngine } from '@/hooks/use-bingo-engine';\r\nimport { Caller } from '@/components/game/Caller';\r\nimport { Controls } from '@/components/game/Controls';\r\nimport { Prizes } from '@/components/game/Prizes';\r\nimport { Ticket } from '@/components/game/Ticket';\r\nimport { BingoBoard } from '@/components/game/BingoBoard';\r\nimport { ScrollArea } from '@/components/ui/scroll-area';\r\nimport { PlayerList } from './PlayerList';\r\n\r\nexport function BingoGame({playerList}: {playerList: Array<String>}) {\r\n  const { state, dispatch } = useBingoEngine();\r\n\r\n  return (\r\n    <div className=\"container mx-auto p-4 flex flex-col lg:flex-row gap-8\">\r\n      {/* Left Column */}\r\n      <div className=\"w-full lg:w-2/3 flex flex-col gap-8\">\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\r\n          <Caller\r\n            currentNumber={state.currentNumber}\r\n            calledNumbersHistory={state.calledNumbersHistory}\r\n            isMuted={state.settings.isMuted}\r\n          />\r\n          <Prizes prizeWinners={state.prizeWinners} />\r\n          <PlayerList players={playerList} />\r\n        </div>\r\n        <div>\r\n          <h2 className=\"text-2xl font-headline glow-primary mb-4\">Your Tickets</h2>\r\n          <ScrollArea className=\"h-[400px] w-full pr-4\">\r\n            <div className=\"grid grid-cols-1 gap-6\">\r\n              {state.tickets.map((ticket, index) => (\r\n                <Ticket\r\n                  key={index}\r\n                  ticket={ticket}\r\n                  calledNumbers={state.calledNumbers}\r\n                  winner={state.prizeWinners.fullHouse?.ticketIndex === index}\r\n                />\r\n              ))}\r\n            </div>\r\n          </ScrollArea>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Right Column */}\r\n      <div className=\"w-full lg:w-1/3 flex flex-col gap-8\">\r\n        <Controls state={state} dispatch={dispatch} />\r\n        <div className=\"flex-grow\">\r\n           <h2 className=\"text-2xl font-headline glow-primary mb-4\">Number Board</h2>\r\n          <BingoBoard calledNumbers={state.calledNumbers} />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;AAYO,SAAS,UAAU,EAAC,UAAU,EAA8B;;IACjE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG;IAE5B,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uIAAA,CAAA,SAAM;gCACL,eAAe,MAAM,aAAa;gCAClC,sBAAsB,MAAM,oBAAoB;gCAChD,SAAS,MAAM,QAAQ,CAAC,OAAO;;;;;;0CAEjC,6LAAC,uIAAA,CAAA,SAAM;gCAAC,cAAc,MAAM,YAAY;;;;;;0CACxC,6LAAC,2IAAA,CAAA,aAAU;gCAAC,SAAS;;;;;;;;;;;;kCAEvB,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,6LAAC,6IAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,6LAAC;oCAAI,WAAU;8CACZ,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC1B,6LAAC,uIAAA,CAAA,SAAM;4CAEL,QAAQ;4CACR,eAAe,MAAM,aAAa;4CAClC,QAAQ,MAAM,YAAY,CAAC,SAAS,EAAE,gBAAgB;2CAHjD;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yIAAA,CAAA,WAAQ;wBAAC,OAAO;wBAAO,UAAU;;;;;;kCAClC,6LAAC;wBAAI,WAAU;;0CACZ,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAC1D,6LAAC,2IAAA,CAAA,aAAU;gCAAC,eAAe,MAAM,aAAa;;;;;;;;;;;;;;;;;;;;;;;;AAKxD;GA3CgB;;QACc;;;KADd", "debugId": null}}, {"offset": {"line": 1629, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/components/player-view/BingoGame.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { useBingoEngine } from '@/hooks/use-bingo-engine';\r\nimport { Caller } from '@/components/game/Caller';\r\nimport { Controls } from '@/components/game/Controls';\r\nimport { Prizes } from '@/components/game/Prizes';\r\nimport { Ticket } from '@/components/game/Ticket';\r\nimport { BingoBoard } from '@/components/game/BingoBoard';\r\nimport { ScrollArea } from '@/components/ui/scroll-area';\r\n\r\nexport function BingoGame() {\r\n  const { state, dispatch } = useBingoEngine();\r\n\r\n  return (\r\n    <div className=\"container mx-auto p-4 flex flex-col lg:flex-row gap-8\">\r\n      {/* Left Column */}\r\n      <div className=\"w-full lg:w-2/3 flex flex-col gap-8\">\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\r\n          <Caller\r\n            currentNumber={state.currentNumber}\r\n            calledNumbersHistory={state.calledNumbersHistory}\r\n            isMuted={state.settings.isMuted}\r\n          />\r\n          <Prizes prizeWinners={state.prizeWinners} />\r\n        </div>\r\n        <div>\r\n          <h2 className=\"text-2xl font-headline glow-primary mb-4\">Your Tickets</h2>\r\n          <ScrollArea className=\"h-[400px] w-full pr-4\">\r\n            <div className=\"grid grid-cols-1 gap-6\">\r\n              {state.tickets.map((ticket, index) => (\r\n                <Ticket\r\n                  key={index}\r\n                  ticket={ticket}\r\n                  calledNumbers={state.calledNumbers}\r\n                  winner={state.prizeWinners.fullHouse?.ticketIndex === index}\r\n                />\r\n              ))}\r\n            </div>\r\n          </ScrollArea>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Right Column */}\r\n      <div className=\"w-full lg:w-1/3 flex flex-col gap-8\">\r\n        <Controls state={state} dispatch={dispatch} />\r\n        <div className=\"flex-grow\">\r\n           <h2 className=\"text-2xl font-headline glow-primary mb-4\">Number Board</h2>\r\n          <BingoBoard calledNumbers={state.calledNumbers} />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;AAIA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;AAWO,SAAS;;IACd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG;IAE5B,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uIAAA,CAAA,SAAM;gCACL,eAAe,MAAM,aAAa;gCAClC,sBAAsB,MAAM,oBAAoB;gCAChD,SAAS,MAAM,QAAQ,CAAC,OAAO;;;;;;0CAEjC,6LAAC,uIAAA,CAAA,SAAM;gCAAC,cAAc,MAAM,YAAY;;;;;;;;;;;;kCAE1C,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,6LAAC,6IAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,6LAAC;oCAAI,WAAU;8CACZ,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC1B,6LAAC,uIAAA,CAAA,SAAM;4CAEL,QAAQ;4CACR,eAAe,MAAM,aAAa;4CAClC,QAAQ,MAAM,YAAY,CAAC,SAAS,EAAE,gBAAgB;2CAHjD;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yIAAA,CAAA,WAAQ;wBAAC,OAAO;wBAAO,UAAU;;;;;;kCAClC,6LAAC;wBAAI,WAAU;;0CACZ,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAC1D,6LAAC,2IAAA,CAAA,aAAU;gCAAC,eAAe,MAAM,aAAa;;;;;;;;;;;;;;;;;;;;;;;;AAKxD;GA1CgB;;QACc;;;KADd", "debugId": null}}, {"offset": {"line": 1798, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/lib/socket.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { io, Socket } from \"socket.io-client\";\r\nconst SOCKET_URL = \"http://localhost:3000\"; // Change if deployed\r\n\r\nlet socket: Socket | null = null;\r\n\r\nexport const getSocket = (): Socket => {\r\n  if (!socket) {\r\n    socket = io(SOCKET_URL, {\r\n      transports: [\"websocket\"],\r\n    });\r\n  }\r\n  return socket;\r\n};"], "names": [], "mappings": ";;;AAEA;AAAA;AAFA;;AAGA,MAAM,aAAa,yBAAyB,qBAAqB;AAEjE,IAAI,SAAwB;AAErB,MAAM,YAAY;IACvB,IAAI,CAAC,QAAQ;QACX,SAAS,CAAA,GAAA,kLAAA,CAAA,KAAE,AAAD,EAAE,YAAY;YACtB,YAAY;gBAAC;aAAY;QAC3B;IACF;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1826, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/app/%5Bgameid%5D/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { BingoGame } from '@/components/game/BingoGame'\r\nimport { BingoGame as PlayerView } from '@/components/player-view/BingoGame';\r\nimport React, { useState, useEffect } from 'react';\r\nimport { getSocket } from '@/lib/socket';\r\nimport { useToast } from '@/hooks/use-toast';\r\n\r\nconst Game = ({ params }: { params: any }) => {\r\n  const gameid = params.gameid;\r\n  const isFirst = window.location.href.split(\"?\")[1] === \"isFirst=true\";\r\n  const { toast } = useToast();\r\n  const socket = getSocket();\r\n  const [playerList, setPlayerList] = useState<Array<String>>([]);\r\n\r\n  useEffect(() => {\r\n    socket.on('user_joined', ({ id, isFirst }: {id: string, isFirst: boolean}) => {\r\n      console.log(`User ${id} joined room ${gameid} (first: ${isFirst})`);\r\n      setPlayerList((prev) => [...prev, id]);\r\n    });\r\n\r\n    console.log(gameid);\r\n    socket.emit('join_room', gameid);\r\n  }, [toast]);\r\n\r\n  return (\r\n    <>\r\n      { isFirst ? <BingoGame playerList={playerList} />:  <PlayerView />}\r\n    </>\r\n  )\r\n}\r\n\r\nexport default Game"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQA,MAAM,OAAO,CAAC,EAAE,MAAM,EAAmB;;IACvC,MAAM,SAAS,OAAO,MAAM;IAC5B,MAAM,UAAU,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,KAAK;IACvD,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAE9D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,OAAO,EAAE,CAAC;kCAAe,CAAC,EAAE,EAAE,EAAE,OAAO,EAAkC;oBACvE,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,GAAG,aAAa,EAAE,OAAO,SAAS,EAAE,QAAQ,CAAC,CAAC;oBAClE;0CAAc,CAAC,OAAS;mCAAI;gCAAM;6BAAG;;gBACvC;;YAEA,QAAQ,GAAG,CAAC;YACZ,OAAO,IAAI,CAAC,aAAa;QAC3B;yBAAG;QAAC;KAAM;IAEV,qBACE;kBACI,wBAAU,6LAAC,0IAAA,CAAA,YAAS;YAAC,YAAY;;;;;iCAAiB,6LAAC,oJAAA,CAAA,YAAU;;;;;;AAGrE;GAtBM;;QAGc,+HAAA,CAAA,WAAQ;;;KAHtB;uCAwBS", "debugId": null}}]}