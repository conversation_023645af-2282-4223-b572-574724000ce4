"use client";

import React from 'react';
import type { BingoState, BingoAction } from '@/hooks/use-bingo-engine';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Play, Pause, RefreshCw, Settings, Volume2, VolumeX } from 'lucide-react';
import { getSocket } from '@/lib/socket';

type ControlsProps = {
  state: BingoState;
  dispatch: React.Dispatch<BingoAction>;
};

export function Controls({ state, dispatch }: ControlsProps) {
  const { gameState, settings } = state;
  const socket = getSocket();

  return (
    <Card className="bg-card/50 backdrop-blur-sm">
      <CardHeader>
        <CardTitle className="font-headline text-2xl flex items-center gap-2 glow-primary">
          <Settings className="w-6 h-6" />
          Admin Controls
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-3 gap-2">
          {gameState === 'idle' || gameState === 'ended' ? (
            <Button
              className="w-full"
              onClick={() => dispatch({ type: 'START_GAME', socket })}
            >
              <Play className="mr-2 h-4 w-4" /> Start
            </Button>
          ) : (
            <Button
              className="w-full"
              variant={gameState === 'paused' ? 'default' : 'secondary'}
              onClick={() => dispatch({ type: 'PAUSE_GAME' })}
            >
              {gameState === 'paused' ? <Play className="mr-2 h-4 w-4" /> : <Pause className="mr-2 h-4 w-4" />}
              {gameState === 'paused' ? 'Resume' : 'Pause'}
            </Button>
          )}

          <Button
            className="w-full"
            variant="destructive"
            onClick={() => dispatch({ type: 'RESET_GAME' })}
          >
            <RefreshCw className="mr-2 h-4 w-4" /> Reset
          </Button>
          
          <Button
            className="w-full"
            variant="outline"
            disabled={gameState !== 'running'}
            onClick={() => dispatch({ type: 'CALL_NUMBER', socket })}
          >
            Call Next
          </Button>
        </div>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="num-tickets">Number of Tickets</Label>
            <Input
              id="num-tickets"
              type="number"
              value={settings.numTickets}
              onChange={(e) =>
                dispatch({
                  type: 'UPDATE_SETTINGS',
                  payload: { numTickets: parseInt(e.target.value, 10) || 1 },
                })
              }
              disabled={gameState !== 'idle'}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="speed">Auto-call Speed (seconds)</Label>
            <Slider
              id="speed"
              min={1}
              max={10}
              step={0.5}
              value={[settings.speed / 1000]}
              onValueChange={([val]) =>
                dispatch({
                  type: 'UPDATE_SETTINGS',
                  payload: { speed: val * 1000 },
                })
              }
            />
          </div>
          <div className="flex items-center justify-between">
            <Label htmlFor="auto-call" className="flex items-center">
              Auto-call Numbers
            </Label>
            <Switch
              id="auto-call"
              checked={settings.isAutoPlay}
              onCheckedChange={(checked) =>
                dispatch({
                  type: 'UPDATE_SETTINGS',
                  payload: { isAutoPlay: checked },
                })
              }
            />
          </div>
           <div className="flex items-center justify-between">
            <Label htmlFor="sound-toggle" className="flex items-center gap-2">
               {settings.isMuted ? <VolumeX className="w-5 h-5" /> : <Volume2 className="w-5 h-5" />}
              Game Sounds
            </Label>
            <Switch
              id="sound-toggle"
              checked={!settings.isMuted}
              onCheckedChange={(checked) =>
                dispatch({
                  type: 'UPDATE_SETTINGS',
                  payload: { isMuted: !checked },
                })
              }
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
