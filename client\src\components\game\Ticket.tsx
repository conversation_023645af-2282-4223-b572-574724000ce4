"use client";

import React from 'react';
import type { Ticket as TicketType } from '@/lib/bingo';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';

type TicketProps = {
  ticket: TicketType;
  calledNumbers: Set<number>;
  winner?: boolean;
};

export function Ticket({ ticket, calledNumbers, winner = false }: TicketProps) {
  return (
    <div
      className={cn(
        'p-4 rounded-lg border-2 transition-all duration-500',
        winner
          ? 'bg-primary/20 border-primary shadow-2xl shadow-primary/50'
          : 'bg-card/80 border-border'
      )}
    >
      <div className="grid grid-cols-9 gap-1">
        {ticket.belongsTo}
        {ticket.content.flat().map((number, index) => {
          const isCalled = number !== null && calledNumbers.has(number);
          return (
            <div
              key={index}
              className={cn(
                'aspect-square flex items-center justify-center rounded-md font-bold text-lg transition-all duration-300',
                number === null ? 'bg-transparent' : 'bg-background/50',
                isCalled &&
                  'bg-accent text-accent-foreground scale-110 rotate-12 shadow-lg shadow-accent/50'
              )}
            >
              {number}
            </div>
          );
        })}
      </div>
    </div>
  );
}
