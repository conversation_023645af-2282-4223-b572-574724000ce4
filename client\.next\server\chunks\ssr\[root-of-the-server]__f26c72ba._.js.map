{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/lib/bingo.ts"], "sourcesContent": ["import { v4 } from \"uuid\";\r\n\r\nexport type Ticket = {\r\n  content: (number | null)[][],\r\n  belongsTo: string,\r\n  id: string,\r\n};\r\nexport type Prize = 'earlyFive' | 'topLine' | 'middleLine' | 'bottomLine' | 'fullHouse';\r\nexport type PrizeWinner = { belongsTo: string; ticketIndex: number };\r\n\r\nexport const PRIZES: Record<Prize, string> = {\r\n  earlyFive: 'Early Five',\r\n  topLine: 'Top Line',\r\n  middleLine: 'Middle Line',\r\n  bottomLine: 'Bottom Line',\r\n  fullHouse: 'Full House',\r\n};\r\n\r\nfunction shuffle<T>(array: T[]): T[] {\r\n  let currentIndex = array.length,  randomIndex;\r\n  while (currentIndex !== 0) {\r\n    randomIndex = Math.floor(Math.random() * currentIndex);\r\n    currentIndex--;\r\n    [array[currentIndex], array[randomIndex]] = [array[randomIndex], array[currentIndex]];\r\n  }\r\n  return array;\r\n}\r\n\r\nexport function generateTickets(belongsTo: string): Ticket[] {\r\n  const tickets: Ticket[] = [];\r\n  tickets.push(generateSingleTicket(belongsTo));\r\n  return tickets;\r\n}\r\n\r\nfunction generateSingleTicket(belongsTo: string): Ticket {\r\n  const ticket: Ticket = { content: Array.from({ length: 3 }, () => Array(9).fill(null)), belongsTo, id: v4() };\r\n  const numbers = new Set<number>();\r\n\r\n  // Determine column fills: 6 columns get 1 number, 3 columns get 2 numbers\r\n  const columns = shuffle(Array.from({length: 9}, (_, i) => i));\r\n  const colsWithTwo = columns.slice(0, 3);\r\n  const colsWithOne = columns.slice(3, 9);\r\n  \r\n  const colFills: number[] = Array(9).fill(0);\r\n  colsWithTwo.forEach(c => colFills[c] = 2);\r\n  colsWithOne.forEach(c => colFills[c] = 1);\r\n\r\n  // Place numbers\r\n  for (let col = 0; col < 9; col++) {\r\n    const numbersInCol = colFills[col];\r\n    const min = col * 10 + 1;\r\n    const max = col * 10 + 10;\r\n    const availableNumbers = shuffle(Array.from({ length: max - min }, (_, i) => min + i));\r\n    const rows = shuffle([0, 1, 2]);\r\n\r\n    for (let i = 0; i < numbersInCol; i++) {\r\n        ticket.content[rows[i]][col] = availableNumbers[i];\r\n    }\r\n  }\r\n\r\n  // Ensure each row has 5 numbers\r\n  for (let row = 0; row < 3; row++) {\r\n    let numbersInRow = ticket.content[row].filter(n => n !== null).length;\r\n    while (numbersInRow < 5) {\r\n        let emptyCol = -1;\r\n        // find an empty column in this row that can accept a number\r\n        const potentialCols = shuffle(Array.from({length: 9}, (_, i) => i));\r\n        for(const col of potentialCols) {\r\n            if(ticket.content[row][col] === null && colFills[col] < 3) {\r\n                emptyCol = col;\r\n                break;\r\n            }\r\n        }\r\n\r\n        if(emptyCol !== -1) {\r\n            const min = emptyCol * 10 + 1;\r\n            const max = emptyCol * 10 + 10;\r\n            const availableNumbers = shuffle(Array.from({ length: max - min }, (_, i) => min + i));\r\n\r\n            let numToAdd;\r\n            let attempt = 0;\r\n            do {\r\n                numToAdd = availableNumbers[attempt++];\r\n            } while (isNumberInTicket(ticket, numToAdd) && attempt < availableNumbers.length);\r\n            \r\n            if (numToAdd && !isNumberInTicket(ticket, numToAdd)) {\r\n                ticket.content[row][emptyCol] = numToAdd;\r\n                colFills[emptyCol]++;\r\n                numbersInRow++;\r\n            }\r\n        } else {\r\n            // Failsafe, should not be hit in normal generation\r\n            break;\r\n        }\r\n    }\r\n  }\r\n\r\n  return ticket;\r\n}\r\n\r\nfunction isNumberInTicket(ticket: Ticket, num: number): boolean {\r\n    return ticket.content.some(row => row.includes(num));\r\n}\r\n\r\n\r\nexport function checkPrizes(ticket: Ticket, calledNumbers: Array<number>): Partial<Record<Prize, boolean>> {\r\n  const results: Partial<Record<Prize, boolean>> = {};\r\n  const ticketNumbers = ticket.content.flat().filter((n): n is number => n !== null);\r\n\r\n  const markedCount = ticketNumbers.filter(n => calledNumbers.includes(n)).length;\r\n\r\n  // Early Five\r\n  if (markedCount >= 5) {\r\n    results.earlyFive = true;\r\n  }\r\n\r\n  // Top Line\r\n  const topLineNumbers = ticket.content[0].filter((n): n is number => n !== null);\r\n  if (topLineNumbers.every(n => calledNumbers.includes(n))) {\r\n    results.topLine = true;\r\n  }\r\n\r\n  // Middle Line\r\n  const middleLineNumbers = ticket.content[1].filter((n): n is number => n !== null);\r\n  if (middleLineNumbers.every(n => calledNumbers.includes(n))) {\r\n    results.middleLine = true;\r\n  }\r\n\r\n  // Bottom Line\r\n  const bottomLineNumbers = ticket.content[2].filter((n): n is number => n !== null);\r\n  if (bottomLineNumbers.every(n => calledNumbers.includes(n))) {\r\n    results.bottomLine = true;\r\n  }\r\n  \r\n  // Full House\r\n  if (ticketNumbers.every(n => calledNumbers.includes(n))) {\r\n    results.fullHouse = true;\r\n  }\r\n\r\n  return results;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;AAUO,MAAM,SAAgC;IAC3C,WAAW;IACX,SAAS;IACT,YAAY;IACZ,YAAY;IACZ,WAAW;AACb;AAEA,SAAS,QAAW,KAAU;IAC5B,IAAI,eAAe,MAAM,MAAM,EAAG;IAClC,MAAO,iBAAiB,EAAG;QACzB,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;QACzC;QACA,CAAC,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,YAAY,CAAC,GAAG;YAAC,KAAK,CAAC,YAAY;YAAE,KAAK,CAAC,aAAa;SAAC;IACvF;IACA,OAAO;AACT;AAEO,SAAS,gBAAgB,SAAiB;IAC/C,MAAM,UAAoB,EAAE;IAC5B,QAAQ,IAAI,CAAC,qBAAqB;IAClC,OAAO;AACT;AAEA,SAAS,qBAAqB,SAAiB;IAC7C,MAAM,SAAiB;QAAE,SAAS,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,IAAM,MAAM,GAAG,IAAI,CAAC;QAAQ;QAAW,IAAI,CAAA,GAAA,0KAAA,CAAA,KAAE,AAAD;IAAI;IAC5G,MAAM,UAAU,IAAI;IAEpB,0EAA0E;IAC1E,MAAM,UAAU,QAAQ,MAAM,IAAI,CAAC;QAAC,QAAQ;IAAC,GAAG,CAAC,GAAG,IAAM;IAC1D,MAAM,cAAc,QAAQ,KAAK,CAAC,GAAG;IACrC,MAAM,cAAc,QAAQ,KAAK,CAAC,GAAG;IAErC,MAAM,WAAqB,MAAM,GAAG,IAAI,CAAC;IACzC,YAAY,OAAO,CAAC,CAAA,IAAK,QAAQ,CAAC,EAAE,GAAG;IACvC,YAAY,OAAO,CAAC,CAAA,IAAK,QAAQ,CAAC,EAAE,GAAG;IAEvC,gBAAgB;IAChB,IAAK,IAAI,MAAM,GAAG,MAAM,GAAG,MAAO;QAChC,MAAM,eAAe,QAAQ,CAAC,IAAI;QAClC,MAAM,MAAM,MAAM,KAAK;QACvB,MAAM,MAAM,MAAM,KAAK;QACvB,MAAM,mBAAmB,QAAQ,MAAM,IAAI,CAAC;YAAE,QAAQ,MAAM;QAAI,GAAG,CAAC,GAAG,IAAM,MAAM;QACnF,MAAM,OAAO,QAAQ;YAAC;YAAG;YAAG;SAAE;QAE9B,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,IAAK;YACnC,OAAO,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,GAAG,gBAAgB,CAAC,EAAE;QACtD;IACF;IAEA,gCAAgC;IAChC,IAAK,IAAI,MAAM,GAAG,MAAM,GAAG,MAAO;QAChC,IAAI,eAAe,OAAO,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM,MAAM,MAAM;QACrE,MAAO,eAAe,EAAG;YACrB,IAAI,WAAW,CAAC;YAChB,4DAA4D;YAC5D,MAAM,gBAAgB,QAAQ,MAAM,IAAI,CAAC;gBAAC,QAAQ;YAAC,GAAG,CAAC,GAAG,IAAM;YAChE,KAAI,MAAM,OAAO,cAAe;gBAC5B,IAAG,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,QAAQ,CAAC,IAAI,GAAG,GAAG;oBACvD,WAAW;oBACX;gBACJ;YACJ;YAEA,IAAG,aAAa,CAAC,GAAG;gBAChB,MAAM,MAAM,WAAW,KAAK;gBAC5B,MAAM,MAAM,WAAW,KAAK;gBAC5B,MAAM,mBAAmB,QAAQ,MAAM,IAAI,CAAC;oBAAE,QAAQ,MAAM;gBAAI,GAAG,CAAC,GAAG,IAAM,MAAM;gBAEnF,IAAI;gBACJ,IAAI,UAAU;gBACd,GAAG;oBACC,WAAW,gBAAgB,CAAC,UAAU;gBAC1C,QAAS,iBAAiB,QAAQ,aAAa,UAAU,iBAAiB,MAAM,CAAE;gBAElF,IAAI,YAAY,CAAC,iBAAiB,QAAQ,WAAW;oBACjD,OAAO,OAAO,CAAC,IAAI,CAAC,SAAS,GAAG;oBAChC,QAAQ,CAAC,SAAS;oBAClB;gBACJ;YACJ,OAAO;gBAEH;YACJ;QACJ;IACF;IAEA,OAAO;AACT;AAEA,SAAS,iBAAiB,MAAc,EAAE,GAAW;IACjD,OAAO,OAAO,OAAO,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,QAAQ,CAAC;AACnD;AAGO,SAAS,YAAY,MAAc,EAAE,aAA4B;IACtE,MAAM,UAA2C,CAAC;IAClD,MAAM,gBAAgB,OAAO,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC,IAAmB,MAAM;IAE7E,MAAM,cAAc,cAAc,MAAM,CAAC,CAAA,IAAK,cAAc,QAAQ,CAAC,IAAI,MAAM;IAE/E,aAAa;IACb,IAAI,eAAe,GAAG;QACpB,QAAQ,SAAS,GAAG;IACtB;IAEA,WAAW;IACX,MAAM,iBAAiB,OAAO,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAmB,MAAM;IAC1E,IAAI,eAAe,KAAK,CAAC,CAAA,IAAK,cAAc,QAAQ,CAAC,KAAK;QACxD,QAAQ,OAAO,GAAG;IACpB;IAEA,cAAc;IACd,MAAM,oBAAoB,OAAO,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAmB,MAAM;IAC7E,IAAI,kBAAkB,KAAK,CAAC,CAAA,IAAK,cAAc,QAAQ,CAAC,KAAK;QAC3D,QAAQ,UAAU,GAAG;IACvB;IAEA,cAAc;IACd,MAAM,oBAAoB,OAAO,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAmB,MAAM;IAC7E,IAAI,kBAAkB,KAAK,CAAC,CAAA,IAAK,cAAc,QAAQ,CAAC,KAAK;QAC3D,QAAQ,UAAU,GAAG;IACvB;IAEA,aAAa;IACb,IAAI,cAAc,KAAK,CAAC,CAAA,IAAK,cAAc,QAAQ,CAAC,KAAK;QACvD,QAAQ,SAAS,GAAG;IACtB;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 269, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/lib/socket.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { io, Socket } from \"socket.io-client\";\r\nconst SOCKET_URL = \"http://localhost:3000\"; // Change if deployed\r\n\r\nlet socket: Socket | null = null;\r\n\r\nexport const getSocket = (): Socket => {\r\n  if (!socket) {\r\n    socket = io(SOCKET_URL, {\r\n      transports: [\"websocket\"],\r\n    });\r\n  }\r\n  return socket;\r\n};"], "names": [], "mappings": ";;;AAEA;AAAA;AAFA;;AAGA,MAAM,aAAa,yBAAyB,qBAAqB;AAEjE,IAAI,SAAwB;AAErB,MAAM,YAAY;IACvB,IAAI,CAAC,QAAQ;QACX,SAAS,CAAA,GAAA,wLAAA,CAAA,KAAE,AAAD,EAAE,YAAY;YACtB,YAAY;gBAAC;aAAY;QAC3B;IACF;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 294, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/hooks/use-bingo-engine.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useReducer, useEffect, useCallback } from 'react';\r\nimport { generateTickets, checkPrizes, type Ticket, type Prize, type PrizeWinner } from '@/lib/bingo';\r\nimport { useToast } from './use-toast';\r\nimport { Trophy } from 'lucide-react';\r\nimport { getSocket } from '@/lib/socket';\r\nimport { Socket } from 'socket.io-client';\r\nimport { speak } from '@/lib/speech';\r\n\r\n// State and Action Types\r\nexport type GameState = 'idle' | 'running' | 'paused' | 'ended';\r\n\r\ntype Settings = {\r\n  speed: number;\r\n  isAutoPlay: boolean;\r\n  isMuted: boolean;\r\n  isMaleVoice: boolean;\r\n};\r\n\r\nexport type BingoState = {\r\n  gameState: GameState;\r\n  tickets: Ticket[];\r\n  players: Array<String>;\r\n  allNumbers: number[];\r\n  calledNumbers: Set<number>;\r\n  calledNumbersHistory: number[];\r\n  currentNumber: number | null;\r\n  prizeWinners: Partial<Record<Prize, PrizeWinner>>;\r\n  settings: Settings;\r\n  isAdmin: boolean;\r\n};\r\n\r\nexport type BingoAction =\r\n  | { type: 'START_GAME', socket: Socket }\r\n  | { type: 'PAUSE_GAME' }\r\n  | { type: 'RESET_GAME' }\r\n  | { type: 'CALL_NUMBER', socket: Socket }\r\n  | { type: 'UPDATE_SETTINGS'; payload: Partial<Settings> }\r\n  | { type: 'CLAIM_PRIZE'; payload: { prize: Prize; winner: PrizeWinner } }\r\n  | { type: 'UPDATE_PLAYER'; payload: { players: Array<String> } }\r\n  | { type: 'UPDATE_EVERYTHING'; payload: { state: BingoState }}\r\n  | { type: 'PURCHASE_TICKET'; socket: Socket }\r\n  | { type: 'LOCAL_ADD_TICKET'; payload: { ticket: Ticket, socket: Socket } };\r\n\r\n// Initial State\r\nconst initialStateFactory = (players: Array<String> = [], tickets: Ticket[] = [], settings: Partial<Settings> = {}): BingoState => {\r\n  const defaultSettings: Settings = {\r\n    speed: 3000,\r\n    isAutoPlay: true,\r\n    isMuted: false,\r\n    isMaleVoice: true,\r\n    ...settings,\r\n  };\r\n\r\n  return {\r\n    gameState: 'idle',\r\n    players: players,\r\n    tickets: tickets,\r\n    allNumbers: Array.from({ length: 90 }, (_, i) => i + 1),\r\n    calledNumbers: new Set(),\r\n    calledNumbersHistory: [],\r\n    currentNumber: null,\r\n    prizeWinners: {},\r\n    settings: defaultSettings,\r\n    isAdmin: false,\r\n  };\r\n};\r\n\r\n// Reducer\r\nfunction bingoReducer(state: BingoState, action: BingoAction): BingoState {\r\n  switch (action.type) {\r\n    case 'START_GAME':\r\n      if (state.gameState === 'idle' || state.gameState === 'ended') {\r\n        const newState = initialStateFactory(state.players, state.tickets, state.settings);\r\n        newState.isAdmin = true;\r\n        action.socket.emit('update-game-state', JSON.stringify({ roomId: window && window.location.pathname.split(\"?\")[0].slice(1), state: newState }));\r\n        newState.gameState = 'running';\r\n        return newState;\r\n      }\r\n       if (state.gameState === 'paused') {\r\n        return { ...state, gameState: 'running' };\r\n      }\r\n      return state;\r\n\r\n    case 'PAUSE_GAME':\r\n      if (state.gameState === 'running') {\r\n        return { ...state, gameState: 'paused' };\r\n      }\r\n       if (state.gameState === 'paused') {\r\n        return { ...state, gameState: 'running' };\r\n      }\r\n      return state;\r\n\r\n    case 'RESET_GAME':\r\n      return initialStateFactory(state.players, state.tickets, state.settings);\r\n\r\n    case 'CALL_NUMBER': {\r\n      if (state.gameState !== 'running') return state;\r\n      const remainingNumbers = state.allNumbers.filter(n => !state.calledNumbersHistory.includes(n));\r\n      if (remainingNumbers.length === 0) {\r\n        return { ...state, gameState: 'ended' };\r\n      }\r\n      const randomIndex = Math.floor(Math.random() * remainingNumbers.length);\r\n      const newNumber = remainingNumbers[randomIndex];\r\n      const newCalledNumbers = new Set(state.calledNumbersHistory).add(newNumber);\r\n      action.socket.emit('update-game-state', JSON.stringify({ roomId: window && window.location.pathname.split(\"?\")[0].slice(1), state: {\r\n        ...state,\r\n        calledNumbers: newCalledNumbers,\r\n        currentNumber: newNumber,\r\n        calledNumbersHistory: [newNumber, ...state.calledNumbersHistory],\r\n      }}));\r\n      \r\n      return {\r\n        ...state,\r\n        calledNumbers: newCalledNumbers,\r\n        currentNumber: newNumber,\r\n        calledNumbersHistory: [newNumber, ...state.calledNumbersHistory],\r\n      };\r\n    }\r\n    \r\n    case 'CLAIM_PRIZE':\r\n        if(state.prizeWinners[action.payload.prize]){\r\n            return state; // Already claimed\r\n        }\r\n        return {\r\n            ...state,\r\n            prizeWinners: {\r\n                ...state.prizeWinners,\r\n                [action.payload.prize]: action.payload.winner\r\n            }\r\n        };\r\n\r\n    case 'UPDATE_SETTINGS':\r\n      const newSettings = { ...state.settings, ...action.payload };\r\n      if (state.gameState === 'idle') {\r\n          let generatedTickets: Ticket[] = [];\r\n          state.players.forEach(player => {\r\n            generatedTickets = [...generatedTickets, ...generateTickets(player as string)];\r\n          })\r\n          return {\r\n              ...state,\r\n              settings: newSettings,\r\n              tickets: generatedTickets \r\n          }\r\n      }\r\n      return { ...state, settings: newSettings };\r\n    \r\n    case 'UPDATE_PLAYER':\r\n      const newPlayers = action.payload.players;\r\n      if (state.gameState === 'idle') {\r\n        return {\r\n            ...state,\r\n            players: newPlayers,\r\n        }\r\n      }\r\n      return { ...state, players: newPlayers };\r\n    \r\n    case 'UPDATE_EVERYTHING':\r\n      return action.payload.state;\r\n    \r\n    case 'PURCHASE_TICKET':\r\n      if (state.gameState !== 'idle' || !action.socket) return state;\r\n      const newTicket = generateTickets(action.socket.id!);\r\n      action.socket.emit('add-ticket', JSON.stringify({ roomId: window && window.location.pathname.split(\"?\")[0].slice(1), ticket: newTicket[0] }));\r\n      return {\r\n        ...state,\r\n        tickets: [...state.tickets, newTicket[0]]\r\n      };\r\n\r\n    case 'LOCAL_ADD_TICKET':\r\n      if (state.tickets.find(ticket => ticket.id === action.payload.ticket.id)) return state;\r\n      action.payload.socket.emit('update-game-state', JSON.stringify({ roomId: window && window.location.pathname.split(\"?\")[0].slice(1), state: {\r\n        ...state,\r\n        tickets: [...state.tickets, action.payload.ticket]\r\n      }}));\r\n      return {\r\n        ...state,\r\n        tickets: [...state.tickets, action.payload.ticket]\r\n      };\r\n\r\n    default:\r\n      return state;\r\n  }\r\n}\r\n\r\n// Custom Hook\r\nexport function useBingoEngine() {\r\n  const [state, dispatch] = useReducer(bingoReducer, initialStateFactory());\r\n  const socket = getSocket();\r\n  const { toast } = useToast();\r\n\r\n  // Auto-play interval\r\n  useEffect(() => {\r\n    let interval: NodeJS.Timeout | null = null;\r\n    if (state.gameState === 'running' && state.settings.isAutoPlay && state.isAdmin) {\r\n      interval = setInterval(() => {\r\n        dispatch({ type: 'CALL_NUMBER', socket });\r\n      }, state.settings.speed);\r\n    }\r\n    return () => {\r\n      if (interval) clearInterval(interval);\r\n    };\r\n  }, [state.gameState, state.settings.isAutoPlay, state.settings.speed]);\r\n\r\n  // Prize checking logic\r\n  const checkAllPrizes = useCallback(() => {\r\n    if (state.gameState !== 'running') return;\r\n    \r\n    state.tickets.forEach((ticket, ticketIndex) => {\r\n        const wonPrizes = checkPrizes(ticket, state.calledNumbersHistory);\r\n        (Object.keys(wonPrizes) as Prize[]).forEach(prize => {\r\n            if (wonPrizes[prize] && !state.prizeWinners[prize]) {\r\n                dispatch({ type: 'CLAIM_PRIZE', payload: { prize, winner: { belongsTo: ticket.belongsTo, ticketIndex } } });\r\n                toast({\r\n                    title: \"Prize Won!\",\r\n                    description: `Ticket #${ticketIndex + 1} won ${prize}!`,\r\n                  })\r\n            }\r\n        });\r\n    });\r\n\r\n  }, [state.tickets, state.calledNumbers, state.prizeWinners, state.gameState, toast]);\r\n\r\n  useEffect(() => {\r\n    socket.on(\"ticket-added\", (ticket: Ticket) => {\r\n      console.log(`Received ticket update: ${ticket.belongsTo}`);\r\n      dispatch({ type: 'LOCAL_ADD_TICKET', payload: { ticket, socket } });\r\n    });\r\n  }, [])\r\n\r\n  useEffect(() => {\r\n    checkAllPrizes();\r\n  }, [state.calledNumbers, checkAllPrizes]);\r\n\r\n\r\n  return { state, dispatch };\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AAEA;AANA;;;;;AA6CA,gBAAgB;AAChB,MAAM,sBAAsB,CAAC,UAAyB,EAAE,EAAE,UAAoB,EAAE,EAAE,WAA8B,CAAC,CAAC;IAChH,MAAM,kBAA4B;QAChC,OAAO;QACP,YAAY;QACZ,SAAS;QACT,aAAa;QACb,GAAG,QAAQ;IACb;IAEA,OAAO;QACL,WAAW;QACX,SAAS;QACT,SAAS;QACT,YAAY,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAG,GAAG,CAAC,GAAG,IAAM,IAAI;QACrD,eAAe,IAAI;QACnB,sBAAsB,EAAE;QACxB,eAAe;QACf,cAAc,CAAC;QACf,UAAU;QACV,SAAS;IACX;AACF;AAEA,UAAU;AACV,SAAS,aAAa,KAAiB,EAAE,MAAmB;IAC1D,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,IAAI,MAAM,SAAS,KAAK,UAAU,MAAM,SAAS,KAAK,SAAS;gBAC7D,MAAM,WAAW,oBAAoB,MAAM,OAAO,EAAE,MAAM,OAAO,EAAE,MAAM,QAAQ;gBACjF,SAAS,OAAO,GAAG;gBACnB,OAAO,MAAM,CAAC,IAAI,CAAC,qBAAqB,KAAK,SAAS,CAAC;oBAAE,QAAQ,UAAU,OAAO,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;oBAAI,OAAO;gBAAS;gBAC5I,SAAS,SAAS,GAAG;gBACrB,OAAO;YACT;YACC,IAAI,MAAM,SAAS,KAAK,UAAU;gBACjC,OAAO;oBAAE,GAAG,KAAK;oBAAE,WAAW;gBAAU;YAC1C;YACA,OAAO;QAET,KAAK;YACH,IAAI,MAAM,SAAS,KAAK,WAAW;gBACjC,OAAO;oBAAE,GAAG,KAAK;oBAAE,WAAW;gBAAS;YACzC;YACC,IAAI,MAAM,SAAS,KAAK,UAAU;gBACjC,OAAO;oBAAE,GAAG,KAAK;oBAAE,WAAW;gBAAU;YAC1C;YACA,OAAO;QAET,KAAK;YACH,OAAO,oBAAoB,MAAM,OAAO,EAAE,MAAM,OAAO,EAAE,MAAM,QAAQ;QAEzE,KAAK;YAAe;gBAClB,IAAI,MAAM,SAAS,KAAK,WAAW,OAAO;gBAC1C,MAAM,mBAAmB,MAAM,UAAU,CAAC,MAAM,CAAC,CAAA,IAAK,CAAC,MAAM,oBAAoB,CAAC,QAAQ,CAAC;gBAC3F,IAAI,iBAAiB,MAAM,KAAK,GAAG;oBACjC,OAAO;wBAAE,GAAG,KAAK;wBAAE,WAAW;oBAAQ;gBACxC;gBACA,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,iBAAiB,MAAM;gBACtE,MAAM,YAAY,gBAAgB,CAAC,YAAY;gBAC/C,MAAM,mBAAmB,IAAI,IAAI,MAAM,oBAAoB,EAAE,GAAG,CAAC;gBACjE,OAAO,MAAM,CAAC,IAAI,CAAC,qBAAqB,KAAK,SAAS,CAAC;oBAAE,QAAQ,UAAU,OAAO,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;oBAAI,OAAO;wBACjI,GAAG,KAAK;wBACR,eAAe;wBACf,eAAe;wBACf,sBAAsB;4BAAC;+BAAc,MAAM,oBAAoB;yBAAC;oBAClE;gBAAC;gBAED,OAAO;oBACL,GAAG,KAAK;oBACR,eAAe;oBACf,eAAe;oBACf,sBAAsB;wBAAC;2BAAc,MAAM,oBAAoB;qBAAC;gBAClE;YACF;QAEA,KAAK;YACD,IAAG,MAAM,YAAY,CAAC,OAAO,OAAO,CAAC,KAAK,CAAC,EAAC;gBACxC,OAAO,OAAO,kBAAkB;YACpC;YACA,OAAO;gBACH,GAAG,KAAK;gBACR,cAAc;oBACV,GAAG,MAAM,YAAY;oBACrB,CAAC,OAAO,OAAO,CAAC,KAAK,CAAC,EAAE,OAAO,OAAO,CAAC,MAAM;gBACjD;YACJ;QAEJ,KAAK;YACH,MAAM,cAAc;gBAAE,GAAG,MAAM,QAAQ;gBAAE,GAAG,OAAO,OAAO;YAAC;YAC3D,IAAI,MAAM,SAAS,KAAK,QAAQ;gBAC5B,IAAI,mBAA6B,EAAE;gBACnC,MAAM,OAAO,CAAC,OAAO,CAAC,CAAA;oBACpB,mBAAmB;2BAAI;2BAAqB,CAAA,GAAA,mHAAA,CAAA,kBAAe,AAAD,EAAE;qBAAkB;gBAChF;gBACA,OAAO;oBACH,GAAG,KAAK;oBACR,UAAU;oBACV,SAAS;gBACb;YACJ;YACA,OAAO;gBAAE,GAAG,KAAK;gBAAE,UAAU;YAAY;QAE3C,KAAK;YACH,MAAM,aAAa,OAAO,OAAO,CAAC,OAAO;YACzC,IAAI,MAAM,SAAS,KAAK,QAAQ;gBAC9B,OAAO;oBACH,GAAG,KAAK;oBACR,SAAS;gBACb;YACF;YACA,OAAO;gBAAE,GAAG,KAAK;gBAAE,SAAS;YAAW;QAEzC,KAAK;YACH,OAAO,OAAO,OAAO,CAAC,KAAK;QAE7B,KAAK;YACH,IAAI,MAAM,SAAS,KAAK,UAAU,CAAC,OAAO,MAAM,EAAE,OAAO;YACzD,MAAM,YAAY,CAAA,GAAA,mHAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,MAAM,CAAC,EAAE;YAClD,OAAO,MAAM,CAAC,IAAI,CAAC,cAAc,KAAK,SAAS,CAAC;gBAAE,QAAQ,UAAU,OAAO,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;gBAAI,QAAQ,SAAS,CAAC,EAAE;YAAC;YAC1I,OAAO;gBACL,GAAG,KAAK;gBACR,SAAS;uBAAI,MAAM,OAAO;oBAAE,SAAS,CAAC,EAAE;iBAAC;YAC3C;QAEF,KAAK;YACH,IAAI,MAAM,OAAO,CAAC,IAAI,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK,OAAO,OAAO,CAAC,MAAM,CAAC,EAAE,GAAG,OAAO;YACjF,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,KAAK,SAAS,CAAC;gBAAE,QAAQ,UAAU,OAAO,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;gBAAI,OAAO;oBACzI,GAAG,KAAK;oBACR,SAAS;2BAAI,MAAM,OAAO;wBAAE,OAAO,OAAO,CAAC,MAAM;qBAAC;gBACpD;YAAC;YACD,OAAO;gBACL,GAAG,KAAK;gBACR,SAAS;uBAAI,MAAM,OAAO;oBAAE,OAAO,OAAO,CAAC,MAAM;iBAAC;YACpD;QAEF;YACE,OAAO;IACX;AACF;AAGO,SAAS;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,cAAc;IACnD,MAAM,SAAS,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IAEzB,qBAAqB;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAkC;QACtC,IAAI,MAAM,SAAS,KAAK,aAAa,MAAM,QAAQ,CAAC,UAAU,IAAI,MAAM,OAAO,EAAE;YAC/E,WAAW,YAAY;gBACrB,SAAS;oBAAE,MAAM;oBAAe;gBAAO;YACzC,GAAG,MAAM,QAAQ,CAAC,KAAK;QACzB;QACA,OAAO;YACL,IAAI,UAAU,cAAc;QAC9B;IACF,GAAG;QAAC,MAAM,SAAS;QAAE,MAAM,QAAQ,CAAC,UAAU;QAAE,MAAM,QAAQ,CAAC,KAAK;KAAC;IAErE,uBAAuB;IACvB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,IAAI,MAAM,SAAS,KAAK,WAAW;QAEnC,MAAM,OAAO,CAAC,OAAO,CAAC,CAAC,QAAQ;YAC3B,MAAM,YAAY,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,MAAM,oBAAoB;YAC/D,OAAO,IAAI,CAAC,WAAuB,OAAO,CAAC,CAAA;gBACxC,IAAI,SAAS,CAAC,MAAM,IAAI,CAAC,MAAM,YAAY,CAAC,MAAM,EAAE;oBAChD,SAAS;wBAAE,MAAM;wBAAe,SAAS;4BAAE;4BAAO,QAAQ;gCAAE,WAAW,OAAO,SAAS;gCAAE;4BAAY;wBAAE;oBAAE;oBACzG,MAAM;wBACF,OAAO;wBACP,aAAa,CAAC,QAAQ,EAAE,cAAc,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;oBACzD;gBACN;YACJ;QACJ;IAEF,GAAG;QAAC,MAAM,OAAO;QAAE,MAAM,aAAa;QAAE,MAAM,YAAY;QAAE,MAAM,SAAS;QAAE;KAAM;IAEnF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO,EAAE,CAAC,gBAAgB,CAAC;YACzB,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,OAAO,SAAS,EAAE;YACzD,SAAS;gBAAE,MAAM;gBAAoB,SAAS;oBAAE;oBAAQ;gBAAO;YAAE;QACnE;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC,MAAM,aAAa;QAAE;KAAe;IAGxC,OAAO;QAAE;QAAO;IAAS;AAC3B", "debugId": null}}, {"offset": {"line": 570, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 651, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/lib/speech.ts"], "sourcesContent": ["\"use client\"\r\n\r\nexport function speak(text: string, isMaleVoice: boolean) {\r\n  if (typeof window === 'undefined' || !window.speechSynthesis) {\r\n    return;\r\n  }\r\n\r\n  // Cancel any previous speech\r\n  window.speechSynthesis.cancel();\r\n  \r\n  const utterance = new SpeechSynthesisUtterance(text);\r\n  utterance.lang = 'en-US';\r\n  \r\n  // Optional: find a specific voice\r\n  const voices = window.speechSynthesis.getVoices();\r\n  console.log(voices);\r\n  if (isMaleVoice) {\r\n    utterance.voice = voices.find(v => v.name.includes(\"<PERSON>\")) || voices[0];\r\n  } else {\r\n    utterance.voice = voices.find(v => v.name.includes(\"<PERSON><PERSON>\")) || voices[0];\r\n  }\r\n  \r\n  window.speechSynthesis.speak(utterance);\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AAEO,SAAS,MAAM,IAAY,EAAE,WAAoB;IACtD,wCAA8D;QAC5D;IACF;;IAKA,MAAM;IAGN,kCAAkC;IAClC,MAAM;AASR", "debugId": null}}, {"offset": {"line": 670, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/components/game/Caller.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect } from 'react';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { speak } from '@/lib/speech';\r\n\r\ntype CallerProps = {\r\n  currentNumber: number | null;\r\n  calledNumbersHistory: number[];\r\n  isMuted: boolean;\r\n  isAdmin: boolean;\r\n  isMale: boolean;\r\n};\r\n\r\nexport function Caller({ currentNumber, calledNumbersHistory, isMuted, isAdmin, isMale }: CallerProps) {\r\n  useEffect(() => {\r\n    if (!isMuted && currentNumber !== null && isAdmin) {\r\n      speak(`Number ${currentNumber}`, isMale);\r\n    }\r\n  }, [currentNumber, isMuted, isAdmin]);\r\n\r\n  return (\r\n    <Card className=\"text-center bg-card/50 backdrop-blur-sm h-full\">\r\n      <CardHeader>\r\n        <CardTitle className=\"font-headline text-2xl glow-primary\">Caller</CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"flex flex-col items-center justify-center gap-4\">\r\n        <div className=\"relative w-40 h-40 flex items-center justify-center\">\r\n          <div className=\"absolute inset-0 bg-primary/20 rounded-full animate-pulse blur-xl\"></div>\r\n          <div className=\"relative z-10 flex items-center justify-center w-36 h-36 bg-background rounded-full border-4 border-primary\">\r\n            {currentNumber !== null ? (\r\n              <span key={currentNumber} className=\"font-headline text-7xl caller-animation text-accent\">\r\n                {currentNumber}\r\n              </span>\r\n            ) : (\r\n              <span className=\"text-muted-foreground\">-</span>\r\n            )}\r\n          </div>\r\n        </div>\r\n        <div className=\"flex gap-2 mt-2\">\r\n          <span className=\"text-muted-foreground\">History:</span>\r\n          {calledNumbersHistory.slice(0, 5).map((num, index) => (\r\n            <span\r\n              key={index}\r\n              className=\"flex items-center justify-center w-8 h-8 rounded-full bg-secondary text-secondary-foreground font-bold\"\r\n              style={{ opacity: 1 - index * 0.2 }}\r\n            >\r\n              {num}\r\n            </span>\r\n          ))}\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAcO,SAAS,OAAO,EAAE,aAAa,EAAE,oBAAoB,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAe;IACnG,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,kBAAkB,QAAQ,SAAS;YACjD,CAAA,GAAA,oHAAA,CAAA,QAAK,AAAD,EAAE,CAAC,OAAO,EAAE,eAAe,EAAE;QACnC;IACF,GAAG;QAAC;QAAe;QAAS;KAAQ;IAEpC,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;0BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oBAAC,WAAU;8BAAsC;;;;;;;;;;;0BAE7D,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;0CACZ,kBAAkB,qBACjB,8OAAC;oCAAyB,WAAU;8CACjC;mCADQ;;;;yDAIX,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;;;;;;kCAI9C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAwB;;;;;;4BACvC,qBAAqB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBAC1C,8OAAC;oCAEC,WAAU;oCACV,OAAO;wCAAE,SAAS,IAAI,QAAQ;oCAAI;8CAEjC;mCAJI;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}, {"offset": {"line": 797, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,4VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 857, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n)\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\r\n    VariantProps<typeof labelVariants>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(labelVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,iKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 889, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/components/ui/slider.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SliderPrimitive from \"@radix-ui/react-slider\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Slider = React.forwardRef<\r\n  React.ElementRef<typeof SliderPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <SliderPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex w-full touch-none select-none items-center\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <SliderPrimitive.Track className=\"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary\">\r\n      <SliderPrimitive.Range className=\"absolute h-full bg-primary\" />\r\n    </SliderPrimitive.Track>\r\n    <SliderPrimitive.Thumb className=\"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\" />\r\n  </SliderPrimitive.Root>\r\n))\r\nSlider.displayName = SliderPrimitive.Root.displayName\r\n\r\nexport { Slider }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;0BAET,8OAAC,kKAAA,CAAA,QAAqB;gBAAC,WAAU;0BAC/B,cAAA,8OAAC,kKAAA,CAAA,QAAqB;oBAAC,WAAU;;;;;;;;;;;0BAEnC,8OAAC,kKAAA,CAAA,QAAqB;gBAAC,WAAU;;;;;;;;;;;;AAGrC,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 941, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Switch = React.forwardRef<\r\n  React.ElementRef<typeof SwitchPrimitives.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <SwitchPrimitives.Root\r\n    className={cn(\r\n      \"peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  >\r\n    <SwitchPrimitives.Thumb\r\n      className={cn(\r\n        \"pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0\"\r\n      )}\r\n    />\r\n  </SwitchPrimitives.Root>\r\n))\r\nSwitch.displayName = SwitchPrimitives.Root.displayName\r\n\r\nexport { Switch }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,OAAqB;QACpB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sXACA;QAED,GAAG,KAAK;QACT,KAAK;kBAEL,cAAA,8OAAC,kKAAA,CAAA,QAAsB;YACrB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKR,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 977, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/components/game/Controls.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport type { BingoState, BingoAction } from '@/hooks/use-bingo-engine';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Slider } from '@/components/ui/slider';\r\nimport { Switch } from '@/components/ui/switch';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Play, Pause, RefreshCw, Settings, Volume2, VolumeX, Venus, Mars } from 'lucide-react';\r\nimport { getSocket } from '@/lib/socket';\r\n\r\ntype ControlsProps = {\r\n  state: BingoState;\r\n  dispatch: React.Dispatch<BingoAction>;\r\n};\r\n\r\nexport function Controls({ state, dispatch }: ControlsProps) {\r\n  const { gameState, settings } = state;\r\n  const socket = getSocket();\r\n\r\n  return (\r\n    <Card className=\"bg-card/50 backdrop-blur-sm\">\r\n      <CardHeader>\r\n        <CardTitle className=\"font-headline text-2xl flex items-center gap-2 glow-primary\">\r\n          <Settings className=\"w-6 h-6\" />\r\n          Admin Controls\r\n        </CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-6\">\r\n        <div className=\"grid grid-cols-3 gap-2\">\r\n          {gameState === 'idle' || gameState === 'ended' ? (\r\n            <Button\r\n              className=\"w-full\"\r\n              onClick={() => dispatch({ type: 'START_GAME', socket })}\r\n            >\r\n              <Play className=\"mr-2 h-4 w-4\" /> Start\r\n            </Button>\r\n          ) : (\r\n            <Button\r\n              className=\"w-full\"\r\n              variant={gameState === 'paused' ? 'default' : 'secondary'}\r\n              onClick={() => dispatch({ type: 'PAUSE_GAME' })}\r\n            >\r\n              {gameState === 'paused' ? <Play className=\"mr-2 h-4 w-4\" /> : <Pause className=\"mr-2 h-4 w-4\" />}\r\n              {gameState === 'paused' ? 'Resume' : 'Pause'}\r\n            </Button>\r\n          )}\r\n\r\n          <Button\r\n            className=\"w-full\"\r\n            variant=\"destructive\"\r\n            onClick={() => dispatch({ type: 'RESET_GAME' })}\r\n          >\r\n            <RefreshCw className=\"mr-2 h-4 w-4\" /> Reset\r\n          </Button>\r\n          \r\n          <Button\r\n            className=\"w-full\"\r\n            variant=\"outline\"\r\n            disabled={gameState !== 'running'}\r\n            onClick={() => dispatch({ type: 'CALL_NUMBER', socket })}\r\n          >\r\n            Call Next\r\n          </Button>\r\n        </div>\r\n        <div className=\"space-y-4\">\r\n          <div className=\"space-y-2\">\r\n            <Label htmlFor=\"speed\">Auto-call Speed (seconds)</Label>\r\n            <Slider\r\n              id=\"speed\"\r\n              min={1}\r\n              max={10}\r\n              step={0.5}\r\n              value={[settings.speed / 1000]}\r\n              onValueChange={([val]) =>\r\n                dispatch({\r\n                  type: 'UPDATE_SETTINGS',\r\n                  payload: { speed: val * 1000 },\r\n                })\r\n              }\r\n            />\r\n          </div>\r\n          <div className=\"flex items-center justify-between\">\r\n            <Label htmlFor=\"auto-call\" className=\"flex items-center\">\r\n              Auto-call Numbers\r\n            </Label>\r\n            <Switch\r\n              id=\"auto-call\"\r\n              checked={settings.isAutoPlay}\r\n              onCheckedChange={(checked) =>\r\n                dispatch({\r\n                  type: 'UPDATE_SETTINGS',\r\n                  payload: { isAutoPlay: checked },\r\n                })\r\n              }\r\n            />\r\n          </div>\r\n           <div className=\"flex items-center justify-between\">\r\n            <Label htmlFor=\"sound-toggle\" className=\"flex items-center gap-2\">\r\n               {settings.isMuted ? <VolumeX className=\"w-5 h-5\" /> : <Volume2 className=\"w-5 h-5\" />}\r\n              Game Sounds\r\n            </Label>\r\n            <Switch\r\n              id=\"sound-toggle\"\r\n              checked={!settings.isMuted}\r\n              onCheckedChange={(checked) =>\r\n                dispatch({\r\n                  type: 'UPDATE_SETTINGS',\r\n                  payload: { isMuted: !checked },\r\n                })\r\n              }\r\n            />\r\n          </div>\r\n           <div className=\"flex items-center justify-between\">\r\n            <Label htmlFor=\"sound-toggle\" className=\"flex items-center gap-2\">\r\n               {settings.isMaleVoice ? <Venus className=\"w-5 h-5\" /> : <Mars className=\"w-5 h-5\" />}\r\n              Male/Female Voice\r\n            </Label>\r\n            <Switch\r\n              id=\"sound-toggle\"\r\n              checked={settings.isMaleVoice}\r\n              onCheckedChange={(checked) =>\r\n                dispatch({\r\n                  type: 'UPDATE_SETTINGS',\r\n                  payload: { isMaleVoice: checked },\r\n                })\r\n              }\r\n            />\r\n          </div>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAIA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAXA;;;;;;;;;AAkBO,SAAS,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAiB;IACzD,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG;IAChC,MAAM,SAAS,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD;IAEvB,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;0BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAAY;;;;;;;;;;;;0BAIpC,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBAAI,WAAU;;4BACZ,cAAc,UAAU,cAAc,wBACrC,8OAAC,kIAAA,CAAA,SAAM;gCACL,WAAU;gCACV,SAAS,IAAM,SAAS;wCAAE,MAAM;wCAAc;oCAAO;;kDAErD,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;qDAGnC,8OAAC,kIAAA,CAAA,SAAM;gCACL,WAAU;gCACV,SAAS,cAAc,WAAW,YAAY;gCAC9C,SAAS,IAAM,SAAS;wCAAE,MAAM;oCAAa;;oCAE5C,cAAc,yBAAW,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;6DAAoB,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAC9E,cAAc,WAAW,WAAW;;;;;;;0CAIzC,8OAAC,kIAAA,CAAA,SAAM;gCACL,WAAU;gCACV,SAAQ;gCACR,SAAS,IAAM,SAAS;wCAAE,MAAM;oCAAa;;kDAE7C,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGxC,8OAAC,kIAAA,CAAA,SAAM;gCACL,WAAU;gCACV,SAAQ;gCACR,UAAU,cAAc;gCACxB,SAAS,IAAM,SAAS;wCAAE,MAAM;wCAAe;oCAAO;0CACvD;;;;;;;;;;;;kCAIH,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAQ;;;;;;kDACvB,8OAAC,kIAAA,CAAA,SAAM;wCACL,IAAG;wCACH,KAAK;wCACL,KAAK;wCACL,MAAM;wCACN,OAAO;4CAAC,SAAS,KAAK,GAAG;yCAAK;wCAC9B,eAAe,CAAC,CAAC,IAAI,GACnB,SAAS;gDACP,MAAM;gDACN,SAAS;oDAAE,OAAO,MAAM;gDAAK;4CAC/B;;;;;;;;;;;;0CAIN,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;kDAAoB;;;;;;kDAGzD,8OAAC,kIAAA,CAAA,SAAM;wCACL,IAAG;wCACH,SAAS,SAAS,UAAU;wCAC5B,iBAAiB,CAAC,UAChB,SAAS;gDACP,MAAM;gDACN,SAAS;oDAAE,YAAY;gDAAQ;4CACjC;;;;;;;;;;;;0CAIL,8OAAC;gCAAI,WAAU;;kDACd,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAe,WAAU;;4CACpC,SAAS,OAAO,iBAAG,8OAAC,4MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;qEAAe,8OAAC,4MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAa;;;;;;;kDAGzF,8OAAC,kIAAA,CAAA,SAAM;wCACL,IAAG;wCACH,SAAS,CAAC,SAAS,OAAO;wCAC1B,iBAAiB,CAAC,UAChB,SAAS;gDACP,MAAM;gDACN,SAAS;oDAAE,SAAS,CAAC;gDAAQ;4CAC/B;;;;;;;;;;;;0CAIL,8OAAC;gCAAI,WAAU;;kDACd,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAe,WAAU;;4CACpC,SAAS,WAAW,iBAAG,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;qEAAe,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAa;;;;;;;kDAGxF,8OAAC,kIAAA,CAAA,SAAM;wCACL,IAAG;wCACH,SAAS,SAAS,WAAW;wCAC7B,iBAAiB,CAAC,UAChB,SAAS;gDACP,MAAM;gDACN,SAAS;oDAAE,aAAa;gDAAQ;4CAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB", "debugId": null}}, {"offset": {"line": 1317, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Separator = React.forwardRef<\r\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\r\n>(\r\n  (\r\n    { className, orientation = \"horizontal\", decorative = true, ...props },\r\n    ref\r\n  ) => (\r\n    <SeparatorPrimitive.Root\r\n      ref={ref}\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"shrink-0 bg-border\",\r\n        orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n)\r\nSeparator.displayName = SeparatorPrimitive.Root.displayName\r\n\r\nexport { Separator }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI/B,CACE,EAAE,SAAS,EAAE,cAAc,YAAY,EAAE,aAAa,IAAI,EAAE,GAAG,OAAO,EACtE,oBAEA,8OAAC,qKAAA,CAAA,OAAuB;QACtB,KAAK;QACL,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,eAAe,mBAAmB,kBAClD;QAED,GAAG,KAAK;;;;;;AAIf,UAAU,WAAW,GAAG,qKAAA,CAAA,OAAuB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1348, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/components/game/Prizes.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Separator } from '@/components/ui/separator';\r\nimport { Trophy, CheckCircle2 } from 'lucide-react';\r\nimport type { Prize, PrizeWinner } from '@/lib/bingo';\r\nimport { PRIZES } from '@/lib/bingo';\r\nimport { cn } from '@/lib/utils';\r\n\r\n\r\ntype PrizesProps = {\r\n  prizeWinners: Partial<Record<Prize, PrizeWinner>>;\r\n};\r\n\r\nexport function Prizes({ prizeWinners }: PrizesProps) {\r\n  return (\r\n    <Card className=\"bg-card/50 backdrop-blur-sm h-full\">\r\n      <CardHeader>\r\n        <CardTitle className=\"font-headline text-2xl flex items-center gap-2 glow-primary\">\r\n          <Trophy className=\"w-6 h-6\" />\r\n          Prizes\r\n        </CardTitle>\r\n      </CardHeader>\r\n      <CardContent>\r\n        <ul className=\"space-y-3\">\r\n          {(Object.keys(PRIZES) as Prize[]).map((prizeKey, index) => {\r\n            const winner = prizeWinners[prizeKey];\r\n            const isWon = !!winner;\r\n            return (\r\n              <React.Fragment key={prizeKey}>\r\n                <li\r\n                  className={cn(\r\n                    'flex justify-between items-center transition-all duration-300',\r\n                    isWon ? 'text-accent' : 'text-foreground'\r\n                  )}\r\n                >\r\n                  <span className=\"font-bold text-lg\">{PRIZES[prizeKey]}</span>\r\n                  {isWon ? (\r\n                    <div className=\"flex items-center gap-2 text-sm\">\r\n                      <span>Ticket #{winner.ticketIndex + 1}</span>\r\n                      <span>{winner.belongsTo}</span>\r\n                    </div>\r\n                  ) : (\r\n                    <span className=\"text-sm text-muted-foreground\">Pending</span>\r\n                  )}\r\n                </li>\r\n                {index < Object.keys(PRIZES).length - 1 && <Separator />}\r\n              </React.Fragment>\r\n            );\r\n          })}\r\n        </ul>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AARA;;;;;;;;AAeO,SAAS,OAAO,EAAE,YAAY,EAAe;IAClD,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;0BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBAAY;;;;;;;;;;;;0BAIlC,8OAAC,gIAAA,CAAA,cAAW;0BACV,cAAA,8OAAC;oBAAG,WAAU;8BACX,AAAC,OAAO,IAAI,CAAC,mHAAA,CAAA,SAAM,EAAc,GAAG,CAAC,CAAC,UAAU;wBAC/C,MAAM,SAAS,YAAY,CAAC,SAAS;wBACrC,MAAM,QAAQ,CAAC,CAAC;wBAChB,qBACE,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;;8CACb,8OAAC;oCACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA,QAAQ,gBAAgB;;sDAG1B,8OAAC;4CAAK,WAAU;sDAAqB,mHAAA,CAAA,SAAM,CAAC,SAAS;;;;;;wCACpD,sBACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;wDAAK;wDAAS,OAAO,WAAW,GAAG;;;;;;;8DACpC,8OAAC;8DAAM,OAAO,SAAS;;;;;;;;;;;iEAGzB,8OAAC;4CAAK,WAAU;sDAAgC;;;;;;;;;;;;gCAGnD,QAAQ,OAAO,IAAI,CAAC,mHAAA,CAAA,SAAM,EAAE,MAAM,GAAG,mBAAK,8OAAC,qIAAA,CAAA,YAAS;;;;;;2BAjBlC;;;;;oBAoBzB;;;;;;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 1486, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/components/game/Ticket.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport type { Ticket as TicketType } from '@/lib/bingo';\r\nimport { cn } from '@/lib/utils';\r\nimport { motion } from 'framer-motion';\r\n\r\ntype TicketProps = {\r\n  ticket: TicketType;\r\n  calledNumbers: Array<number>;\r\n  winner?: boolean;\r\n};\r\n\r\nexport function Ticket({ ticket, calledNumbers, winner = false }: TicketProps) {\r\n  return (\r\n    <div\r\n      className={cn(\r\n        'p-4 rounded-lg border-2 transition-all duration-500',\r\n        winner\r\n          ? 'bg-primary/20 border-primary shadow-2xl shadow-primary/50'\r\n          : 'bg-card/80 border-border'\r\n      )}\r\n    >\r\n      <div className=\"grid grid-cols-9 gap-1\">\r\n        {ticket.content.flat().map((number, index) => {\r\n          const isCalled = number !== null && calledNumbers.includes(number);\r\n          return (\r\n            <div\r\n              key={index}\r\n              className={cn(\r\n                'aspect-square flex items-center justify-center rounded-md font-bold text-lg transition-all duration-300',\r\n                number === null ? 'bg-transparent' : 'bg-background/50',\r\n                isCalled &&\r\n                  'bg-accent text-accent-foreground scale-110 rotate-12 shadow-lg shadow-accent/50'\r\n              )}\r\n            >\r\n              {number}\r\n            </div>\r\n          );\r\n        })}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAIA;AAJA;;;AAaO,SAAS,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,SAAS,KAAK,EAAe;IAC3E,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uDACA,SACI,8DACA;kBAGN,cAAA,8OAAC;YAAI,WAAU;sBACZ,OAAO,OAAO,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,QAAQ;gBAClC,MAAM,WAAW,WAAW,QAAQ,cAAc,QAAQ,CAAC;gBAC3D,qBACE,8OAAC;oBAEC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2GACA,WAAW,OAAO,mBAAmB,oBACrC,YACE;8BAGH;mBARI;;;;;YAWX;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 1527, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/components/game/BingoBoard.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { Card, CardContent } from '@/components/ui/card';\r\nimport { cn } from '@/lib/utils';\r\n\r\ntype BingoBoardProps = {\r\n  calledNumbers: Array<number>;\r\n};\r\n\r\nexport function BingoBoard({ calledNumbers }: BingoBoardProps) {\r\n  const numbers = Array.from({ length: 90 }, (_, i) => i + 1);\r\n\r\n  return (\r\n    <Card className=\"bg-card/50 backdrop-blur-sm\">\r\n      <CardContent className=\"p-4\">\r\n        <div className=\"grid grid-cols-10 md:grid-cols-15 lg:grid-cols-18 xl:grid-cols-20 gap-2\">\r\n          {numbers.map((number) => {\r\n            const isCalled = calledNumbers.includes(number);\r\n            return (\r\n              <div\r\n                key={number}\r\n                className={cn(\r\n                  'flex items-center justify-center aspect-square rounded-full text-sm md:text-base font-bold transition-all duration-300',\r\n                  isCalled\r\n                    ? 'bg-primary text-primary-foreground scale-110 shadow-lg shadow-primary/50'\r\n                    : 'bg-secondary text-secondary-foreground'\r\n                )}\r\n              >\r\n                {number}\r\n              </div>\r\n            );\r\n          })}\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAUO,SAAS,WAAW,EAAE,aAAa,EAAmB;IAC3D,MAAM,UAAU,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAG,GAAG,CAAC,GAAG,IAAM,IAAI;IAEzD,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;kBACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,8OAAC;gBAAI,WAAU;0BACZ,QAAQ,GAAG,CAAC,CAAC;oBACZ,MAAM,WAAW,cAAc,QAAQ,CAAC;oBACxC,qBACE,8OAAC;wBAEC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0HACA,WACI,6EACA;kCAGL;uBARI;;;;;gBAWX;;;;;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 1580, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst ScrollArea = React.forwardRef<\r\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\r\n>(({ className, children, ...props }, ref) => (\r\n  <ScrollAreaPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\"relative overflow-hidden\", className)}\r\n    {...props}\r\n  >\r\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\r\n      {children}\r\n    </ScrollAreaPrimitive.Viewport>\r\n    <ScrollBar />\r\n    <ScrollAreaPrimitive.Corner />\r\n  </ScrollAreaPrimitive.Root>\r\n))\r\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\r\n\r\nconst ScrollBar = React.forwardRef<\r\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\r\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\r\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\r\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\r\n    ref={ref}\r\n    orientation={orientation}\r\n    className={cn(\r\n      \"flex touch-none select-none transition-colors\",\r\n      orientation === \"vertical\" &&\r\n        \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\r\n      orientation === \"horizontal\" &&\r\n        \"h-2.5 flex-col border-t border-t-transparent p-[1px]\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\r\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\r\n))\r\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\r\n\r\nexport { ScrollArea, ScrollBar }\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,0KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;0BAET,8OAAC,0KAAA,CAAA,WAA4B;gBAAC,WAAU;0BACrC;;;;;;0BAEH,8OAAC;;;;;0BACD,8OAAC,0KAAA,CAAA,SAA0B;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,0KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO,EAAE,oBACpD,8OAAC,0KAAA,CAAA,sBAAuC;QACtC,KAAK;QACL,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cACd,sDACF,gBAAgB,gBACd,wDACF;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,0KAAA,CAAA,kBAAmC;YAAC,WAAU;;;;;;;;;;;AAGnD,UAAU,WAAW,GAAG,0KAAA,CAAA,sBAAuC,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1648, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/components/game/PlayerList.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Separator } from '@/components/ui/separator';\r\nimport { PersonStanding } from 'lucide-react';\r\nimport { cn } from '@/lib/utils';\r\n\r\nexport function PlayerList({ players }: { players: Array<String> }) {\r\n  return (\r\n    <Card className=\"bg-card/50 backdrop-blur-sm h-full\">\r\n      <CardHeader>\r\n        <CardTitle className=\"font-headline text-2xl flex items-center gap-2 glow-primary\">\r\n          <PersonStanding className='w-6 h-6' />\r\n          Players\r\n        </CardTitle>\r\n      </CardHeader>\r\n      <CardContent>\r\n        <ul className=\"space-y-3\">\r\n          {players.map((player, index) => (\r\n            <li className={cn('transition-all duration-300')} key={index}>\r\n              <span className='font-bold text-lg'>\r\n                {player}\r\n              </span>\r\n              {index < players.length - 1 && <Separator className='mt-2' />}\r\n            </li>\r\n          ))}\r\n        </ul>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AANA;;;;;;AAQO,SAAS,WAAW,EAAE,OAAO,EAA8B;IAChE,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;0BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,8OAAC,0NAAA,CAAA,iBAAc;4BAAC,WAAU;;;;;;wBAAY;;;;;;;;;;;;0BAI1C,8OAAC,gIAAA,CAAA,cAAW;0BACV,cAAA,8OAAC;oBAAG,WAAU;8BACX,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC;4BAAG,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;;8CAChB,8OAAC;oCAAK,WAAU;8CACb;;;;;;gCAEF,QAAQ,QAAQ,MAAM,GAAG,mBAAK,8OAAC,qIAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;2BAJC;;;;;;;;;;;;;;;;;;;;;AAWnE", "debugId": null}}, {"offset": {"line": 1739, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/components/game/BingoGame.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect } from 'react';\r\nimport { useBingoEngine, BingoState } from '@/hooks/use-bingo-engine';\r\nimport { Caller } from '@/components/game/Caller';\r\nimport { Controls } from '@/components/game/Controls';\r\nimport { Prizes } from '@/components/game/Prizes';\r\nimport { Ticket } from '@/components/game/Ticket';\r\nimport { BingoBoard } from '@/components/game/BingoBoard';\r\nimport { ScrollArea } from '@/components/ui/scroll-area';\r\nimport { PlayerList } from './PlayerList';\r\nimport { getSocket } from '@/lib/socket';\r\n\r\nexport function BingoGame({playerList}: {playerList: Array<String>}) {\r\n  const { state, dispatch } = useBingoEngine();\r\n  const socket = getSocket();\r\n\r\n  useEffect(() => {\r\n    socket.on(\"update-game-state\", (data: BingoState) => {\r\n      console.log('Received game state update:', data);\r\n      dispatch({ type: 'UPDATE_EVERYTHING', payload: { state: data } });\r\n    });\r\n\r\n    dispatch({ type: 'UPDATE_PLAYER', payload: { players: playerList } });\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"container mx-auto p-4 flex flex-col lg:flex-row gap-8\">\r\n      {/* Left Column */}\r\n      <div className=\"w-full lg:w-2/3 flex flex-col gap-8\">\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\r\n          <Caller\r\n            currentNumber={state.currentNumber}\r\n            calledNumbersHistory={state.calledNumbersHistory}\r\n            isMuted={state.settings.isMuted}\r\n            isAdmin={true}\r\n            isMale={state.settings.isMaleVoice}\r\n          />\r\n          <Prizes prizeWinners={state.prizeWinners} />\r\n          <PlayerList players={playerList} />\r\n        </div>\r\n        <div>\r\n          <h2 className=\"text-2xl font-headline glow-primary mb-4\">Your Tickets</h2>\r\n          <ScrollArea className=\"h-[400px] w-full pr-4\">\r\n            <div className=\"grid grid-cols-1 gap-6\">\r\n              {state.tickets.map((ticket, index) => (\r\n                <>\r\n                  <p>{ticket.belongsTo}</p>\r\n                  <Ticket\r\n                    key={index}\r\n                    ticket={ticket}\r\n                    calledNumbers={state.calledNumbersHistory}\r\n                    winner={state.prizeWinners.fullHouse?.ticketIndex === index}\r\n                  />\r\n                </>\r\n              ))}\r\n            </div>\r\n          </ScrollArea>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Right Column */}\r\n      <div className=\"w-full lg:w-1/3 flex flex-col gap-8\">\r\n        <Controls state={state} dispatch={dispatch} />\r\n        <div className=\"flex-grow\">\r\n           <h2 className=\"text-2xl font-headline glow-primary mb-4\">Number Board</h2>\r\n          <BingoBoard calledNumbers={state.calledNumbersHistory} />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;;AAaO,SAAS,UAAU,EAAC,UAAU,EAA8B;IACjE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,iBAAc,AAAD;IACzC,MAAM,SAAS,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO,EAAE,CAAC,qBAAqB,CAAC;YAC9B,QAAQ,GAAG,CAAC,+BAA+B;YAC3C,SAAS;gBAAE,MAAM;gBAAqB,SAAS;oBAAE,OAAO;gBAAK;YAAE;QACjE;QAEA,SAAS;YAAE,MAAM;YAAiB,SAAS;gBAAE,SAAS;YAAW;QAAE;IACrE,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oIAAA,CAAA,SAAM;gCACL,eAAe,MAAM,aAAa;gCAClC,sBAAsB,MAAM,oBAAoB;gCAChD,SAAS,MAAM,QAAQ,CAAC,OAAO;gCAC/B,SAAS;gCACT,QAAQ,MAAM,QAAQ,CAAC,WAAW;;;;;;0CAEpC,8OAAC,oIAAA,CAAA,SAAM;gCAAC,cAAc,MAAM,YAAY;;;;;;0CACxC,8OAAC,wIAAA,CAAA,aAAU;gCAAC,SAAS;;;;;;;;;;;;kCAEvB,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,8OAAC,0IAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC;oCAAI,WAAU;8CACZ,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC1B;;8DACE,8OAAC;8DAAG,OAAO,SAAS;;;;;;8DACpB,8OAAC,oIAAA,CAAA,SAAM;oDAEL,QAAQ;oDACR,eAAe,MAAM,oBAAoB;oDACzC,QAAQ,MAAM,YAAY,CAAC,SAAS,EAAE,gBAAgB;mDAHjD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAanB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sIAAA,CAAA,WAAQ;wBAAC,OAAO;wBAAO,UAAU;;;;;;kCAClC,8OAAC;wBAAI,WAAU;;0CACZ,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAC1D,8OAAC,wIAAA,CAAA,aAAU;gCAAC,eAAe,MAAM,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;AAK/D", "debugId": null}}, {"offset": {"line": 1935, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/components/game/PurchaseTicket.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect } from 'react';\r\nimport { useBingoEngine } from '@/hooks/use-bingo-engine';\r\nimport { getSocket } from '@/lib/socket';\r\nimport { Button } from '../ui/button';\r\nimport { Ticket } from 'lucide-react';\r\n\r\nconst PurchaseTicket = () => {\r\n\tconst { state, dispatch } = useBingoEngine();\r\n\tconst socket = getSocket();\r\n\r\n  return (\r\n    <div>\r\n          <Button\r\n            className=\"w-full\"\r\n            variant=\"default\"\r\n\t\t\t\t\t\tdisabled={state.gameState !== 'idle'}\r\n            onClick={() => {\r\n              console.log(\"add ticket\")\r\n              dispatch({ type: 'PURCHASE_TICKET', socket })\r\n            }}\r\n          >\r\n\t\t\t\t\t\t<Ticket className=\"mr-2 h-4 w-4\" />\r\n            Purchase Ticket\r\n          </Button>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default PurchaseTicket"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AANA;;;;;;AAQA,MAAM,iBAAiB;IACtB,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,iBAAc,AAAD;IACzC,MAAM,SAAS,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD;IAEtB,qBACE,8OAAC;kBACK,cAAA,8OAAC,kIAAA,CAAA,SAAM;YACL,WAAU;YACV,SAAQ;YACd,UAAU,MAAM,SAAS,KAAK;YACxB,SAAS;gBACP,QAAQ,GAAG,CAAC;gBACZ,SAAS;oBAAE,MAAM;oBAAmB;gBAAO;YAC7C;;8BAEN,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;gBAAiB;;;;;;;;;;;;AAKzC;uCAEe", "debugId": null}}, {"offset": {"line": 1992, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/components/player-view/BingoGame.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { BingoState, useBingoEngine } from '@/hooks/use-bingo-engine';\r\nimport { Caller } from '@/components/game/Caller';\r\nimport { Prizes } from '@/components/game/Prizes';\r\nimport { Ticket } from '@/components/game/Ticket';\r\nimport { BingoBoard } from '@/components/game/BingoBoard';\r\nimport { ScrollArea } from '@/components/ui/scroll-area';\r\nimport { getSocket } from '@/lib/socket';\r\nimport { speak } from '@/lib/speech';\r\nimport PurchaseTicket from '../game/PurchaseTicket';\r\n\r\nexport function BingoGame() {\r\n  const { state, dispatch } = useBingoEngine();\r\n  const socket = getSocket();\r\n\r\n  useEffect(() => {\r\n    // Only run on client side\r\n    if (typeof window === 'undefined') return;\r\n\r\n    const handleGameStateUpdate = (data: BingoState) => {\r\n      console.log('Received game state update:', data);\r\n      if (data.currentNumber !== null)\r\n        speak(`Number ${data.currentNumber}`, state.settings.isMaleVoice);\r\n\t\t\tdispatch({ type: 'UPDATE_EVERYTHING', payload: { state: data } });\r\n    };\r\n\r\n    socket.on(\"update-game-state\", handleGameStateUpdate);\r\n\r\n    // Cleanup function\r\n    return () => {\r\n      socket.off(\"update-game-state\", handleGameStateUpdate);\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"container mx-auto p-4 flex flex-col lg:flex-row gap-8\">\r\n      {/* Left Column */}\r\n      <div className=\"w-full lg:w-2/3 flex flex-col gap-8\">\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\r\n          <Caller\r\n            currentNumber={state.currentNumber}\r\n            calledNumbersHistory={state.calledNumbersHistory}\r\n            isMuted={state.settings.isMuted}\r\n            isAdmin={false}\r\n            isMale={state.settings.isMaleVoice}\r\n          />\r\n          <Prizes prizeWinners={state.prizeWinners} />\r\n        </div>\r\n        <div>\r\n          <h2 className=\"text-2xl font-headline glow-primary mb-4\">Your Tickets</h2>\r\n          <ScrollArea className=\"h-[400px] w-full pr-4\">\r\n            {state.gameState === 'idle' && <PurchaseTicket />}\r\n            <div className=\"grid grid-cols-1 gap-6\">\r\n              {state.tickets.map((ticket, index) => (\r\n                ticket.belongsTo === socket.id && \r\n                <Ticket\r\n                  key={index}\r\n                  ticket={ticket}\r\n                  calledNumbers={state.calledNumbersHistory}\r\n                  winner={state.prizeWinners.fullHouse?.ticketIndex === index}\r\n                />\r\n              ))}\r\n            </div>\r\n          </ScrollArea>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Right Column */}\r\n      <div className=\"w-full lg:w-1/3 flex flex-col gap-8\">\r\n\r\n        <div className=\"flex-grow\">\r\n           <h2 className=\"text-2xl font-headline glow-primary mb-4\">Number Board</h2>\r\n          <BingoBoard calledNumbers={state.calledNumbersHistory} />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;;AAaO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,iBAAc,AAAD;IACzC,MAAM,SAAS,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,0BAA0B;QAC1B,wCAAmC;;QAEnC,MAAM;IAaR,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oIAAA,CAAA,SAAM;gCACL,eAAe,MAAM,aAAa;gCAClC,sBAAsB,MAAM,oBAAoB;gCAChD,SAAS,MAAM,QAAQ,CAAC,OAAO;gCAC/B,SAAS;gCACT,QAAQ,MAAM,QAAQ,CAAC,WAAW;;;;;;0CAEpC,8OAAC,oIAAA,CAAA,SAAM;gCAAC,cAAc,MAAM,YAAY;;;;;;;;;;;;kCAE1C,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,8OAAC,0IAAA,CAAA,aAAU;gCAAC,WAAU;;oCACnB,MAAM,SAAS,KAAK,wBAAU,8OAAC,4IAAA,CAAA,UAAc;;;;;kDAC9C,8OAAC;wCAAI,WAAU;kDACZ,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,QAC1B,OAAO,SAAS,KAAK,OAAO,EAAE,kBAC9B,8OAAC,oIAAA,CAAA,SAAM;gDAEL,QAAQ;gDACR,eAAe,MAAM,oBAAoB;gDACzC,QAAQ,MAAM,YAAY,CAAC,SAAS,EAAE,gBAAgB;+CAHjD;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYjB,8OAAC;gBAAI,WAAU;0BAEb,cAAA,8OAAC;oBAAI,WAAU;;sCACZ,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;sCAC1D,8OAAC,wIAAA,CAAA,aAAU;4BAAC,eAAe,MAAM,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;AAK/D", "debugId": null}}, {"offset": {"line": 2180, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/src/app/%5Bgameid%5D/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { BingoGame } from '@/components/game/BingoGame'\r\nimport { BingoGame as PlayerView } from '@/components/player-view/BingoGame';\r\nimport React, { useState, useEffect } from 'react';\r\nimport { useSearchParams } from 'next/navigation';\r\nimport { getSocket } from '@/lib/socket';\r\nimport { useToast } from '@/hooks/use-toast';\r\n\r\nconst Game = () => {\r\n  const searchParams = useSearchParams();\r\n  const { toast } = useToast();\r\n  const socket = getSocket();\r\n  const [playerList, setPlayerList] = useState<Array<String>>([]);\r\n\r\n  useEffect(() => {\r\n    socket.on('user_joined', ({ id, isFirst }: {id: string, isFirst: boolean}) => {\r\n      console.log(`User ${id} joined room ${window && window.location.pathname.split(\"?\")[0].slice(1)} (first: ${isFirst})`);\r\n      setPlayerList((prev) => Array.from(new Set([...prev, id])));\r\n    });\r\n\r\n    if (window !== undefined) {\r\n      console.log(window && window.location.pathname.split(\"?\")[0].slice(1));\r\n      socket.emit('join_room', window.location.pathname.split(\"?\")[0].slice(1));\r\n    }\r\n  }, [toast]);\r\n\r\n  return (\r\n    <>\r\n      {searchParams.get(\"isFirst\") ? <BingoGame playerList={playerList} />:  <PlayerView />}\r\n    </>\r\n  )\r\n}\r\n\r\nexport default Game"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASA,MAAM,OAAO;IACX,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,SAAS,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAE9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO,EAAE,CAAC,eAAe,CAAC,EAAE,EAAE,EAAE,OAAO,EAAkC;YACvE,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,GAAG,aAAa,EAAE,UAAU,OAAO,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,SAAS,EAAE,QAAQ,CAAC,CAAC;YACrH,cAAc,CAAC,OAAS,MAAM,IAAI,CAAC,IAAI,IAAI;uBAAI;oBAAM;iBAAG;QAC1D;QAEA,IAAI,WAAW,WAAW;YACxB,QAAQ,GAAG,CAAC,UAAU,OAAO,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;YACnE,OAAO,IAAI,CAAC,aAAa,OAAO,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;QACxE;IACF,GAAG;QAAC;KAAM;IAEV,qBACE;kBACG,aAAa,GAAG,CAAC,2BAAa,8OAAC,uIAAA,CAAA,YAAS;YAAC,YAAY;;;;;iCAAiB,8OAAC,iJAAA,CAAA,YAAU;;;;;;AAGxF;uCAEe", "debugId": null}}]}