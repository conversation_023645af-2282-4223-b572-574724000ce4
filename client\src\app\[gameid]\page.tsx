"use client";

import { BingoGame } from '@/components/game/BingoGame'
import { BingoGame as PlayerView } from '@/components/player-view/BingoGame';
import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { getSocket } from '@/lib/socket';
import { useToast } from '@/hooks/use-toast';

const Game = () => {
  const searchParams = useSearchParams();
  const { toast } = useToast();
  const socket = getSocket();
  const [playerList, setPlayerList] = useState<Array<String>>([]);

  useEffect(() => {
    socket.on('user_joined', ({ id, isFirst }: {id: string, isFirst: boolean}) => {
      console.log(`User ${id} joined room ${window && window.location.pathname.split("?")[0].slice(1)} (first: ${isFirst})`);
      setPlayerList((prev) => Array.from(new Set([...prev, id])));
    });

    if (window !== undefined) {
      console.log(window && window.location.pathname.split("?")[0].slice(1));
      socket.emit('join_room', window.location.pathname.split("?")[0].slice(1));
    }
  }, [toast]);

  return (
    <>
      {searchParams.get("isFirst") ? <BingoGame playerList={playerList} />:  <PlayerView />}
    </>
  )
}

export default Game