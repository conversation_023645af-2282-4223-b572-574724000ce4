"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { v4 } from 'uuid';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Users,
  Plus,
  LogIn,
  Gamepad2,
  Crown,
  AlertCircle,
  Copy,
  Check
} from 'lucide-react';
import { getSocket } from '@/lib/socket';
import { useToast } from '@/hooks/use-toast';

const HomePage = () => {
  const router = useRouter();
  const [rooms, setRooms] = useState<Array<String>>([]);
  const [joinRoomId, setJoinRoomId] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const [isJoining, setIsJoining] = useState(false);
  const [error, setError] = useState('');
  const [copiedRoomId, setCopiedRoomId] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') return;

    const socket = getSocket();

    // Socket event listeners
    const handleRoomCreated = (room_list: Array<string>) => {
      setRooms(room_list);
    };

    const handleRoomError = (message: string) => {
      setError(message);
      setIsCreating(false);
      setIsJoining(false);
    };

    const handleRoomsList = (roomsList: Array<string>) => {
      setRooms(roomsList);
    };

    socket.on('room_created', handleRoomCreated);
    socket.on('room_error', handleRoomError);
    socket.on('rooms_list', handleRoomsList);

    // Request initial rooms list
    socket.emit('get_rooms');

    return () => {
      socket.off('room_created', handleRoomCreated);
      socket.off('room_error', handleRoomError);
      socket.off('rooms_list', handleRoomsList);
    };
  }, [toast]);

  const createRoom = () => {
    // Generate room ID and navigate
    const roomId = v4();

    // Navigate using Next.js router
    router.push(`/${roomId}?isFirst=true`);

    setIsCreating(false);
  };

  const joinRoom = (roomId: string) => {
    console.log("Joining room: " + roomId);

    if (!roomId.trim()) {
      setError('Please enter a room ID');
      return;
    }

    setIsJoining(true);
    setError('');

    // Navigate using Next.js router
    router.push(`/${roomId}`);

    setJoinRoomId('');
    setIsJoining(false);
  };

  const copyRoomId = async (roomId: string) => {
    // Only run on client side
    if (typeof window === 'undefined') return;

    try {
      await navigator.clipboard.writeText(roomId);
      setCopiedRoomId(roomId);
      toast({
        title: "Copied!",
        description: "Room ID copied to clipboard.",
      });
      setTimeout(() => setCopiedRoomId(null), 2000);
    } catch (err) {
      toast({
        title: "Copy Failed",
        description: "Failed to copy room ID to clipboard.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="container mx-auto p-4 max-w-6xl">
      <div className="text-center mb-8">
        <h1 className="text-8xl font-headline font-bold glow-primary mb-4">
          BINGO<span className="text-accent glow-accent">VERSE</span>
        </h1>
        <p className="text-lg text-muted-foreground">
          Create or join a room to start playing multiplayer bingo!
        </p>
      </div>

      {error && (
        <Alert className="mb-6 border-destructive/50 bg-destructive/10">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="text-destructive">
            {error}
          </AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* Create Room */}
        <Card className="bg-card/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="font-headline text-2xl flex items-center gap-2 glow-primary">
              <Plus className="w-6 h-6" />
              Create New Room
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button
              onClick={createRoom}
              disabled={isCreating}
              className="w-full"
            >
              <Plus className="mr-2 h-4 w-4" />
              {isCreating ? 'Creating...' : 'Create Room'}
            </Button>
          </CardContent>
        </Card>

        {/* Join Room */}
        <Card className="bg-card/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="font-headline text-2xl flex items-center gap-2 glow-primary">
              <LogIn className="w-6 h-6" />
              Join Existing Room
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="room-id">Room ID</Label>
              <Input
                id="room-id"
                placeholder="Enter room ID..."
                value={joinRoomId}
                onChange={(e) => setJoinRoomId(e.target.value)}
              />
            </div>
            <Button
              onClick={() => joinRoom(joinRoomId)}
              disabled={isJoining || !joinRoomId.trim()}
              className="w-full"
            >
              <LogIn className="mr-2 h-4 w-4" />
              {isJoining ? 'Joining...' : 'Join Room'}
            </Button>
          </CardContent>
        </Card>
      </div>

      <Separator className="my-8" />

      {/* Available Rooms */}
      <Card className="bg-card/50 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="font-headline text-2xl flex items-center gap-2 glow-primary">
            <Gamepad2 className="w-6 h-6" />
            Available Rooms
          </CardTitle>
        </CardHeader>
        <CardContent>
          {rooms.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Users className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>No rooms available. Create one to get started!</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {rooms.map((room) => (
                <Card className="bg-background/50 border-border/50">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1 min-w-0">
                        <h3 className="font-semibold text-lg truncate">{room}</h3>
                      </div>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => copyRoomId(room as string)}
                        className="ml-2 flex-shrink-0"
                      >
                        {copiedRoomId === room ? (
                          <Check className="h-4 w-4 text-green-500" />
                        ) : (
                          <Copy className="h-4 w-4" />
                        )}
                      </Button>
                    </div>

                    <Button
                      onClick={() => joinRoom(room as string)}
                      className="w-full"
                      size="sm"
                    >
                            <LogIn className="mr-2 h-4 w-4" />
                            Join Room
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Start Guide */}
      <Card className="bg-card/50 backdrop-blur-sm mt-8">
        <CardHeader>
          <CardTitle className="font-headline text-xl flex items-center gap-2 glow-primary">
            <Crown className="w-5 h-5" />
            How to Play
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="bg-primary/20 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                <span className="text-primary font-bold">1</span>
              </div>
              <h3 className="font-semibold mb-2">Create or Join</h3>
              <p className="text-sm text-muted-foreground">
                Create a new room or join an existing one using the room ID
              </p>
            </div>
            <div className="text-center">
              <div className="bg-primary/20 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                <span className="text-primary font-bold">2</span>
              </div>
              <h3 className="font-semibold mb-2">Get Your Tickets</h3>
              <p className="text-sm text-muted-foreground">
                Purchase bingo tickets and wait for other players to join
              </p>
            </div>
            <div className="text-center">
              <div className="bg-primary/20 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                <span className="text-primary font-bold">3</span>
              </div>
              <h3 className="font-semibold mb-2">Play & Win</h3>
              <p className="text-sm text-muted-foreground">
                Mark your numbers as they're called and be the first to win!
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default HomePage;