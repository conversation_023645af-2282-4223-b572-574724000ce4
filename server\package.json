{"name": "server", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "ts-node-dev --respawn index.ts", "build": "tsc", "start": "node dist/index.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"express": "^5.1.0", "socket.io": "^4.8.1"}, "devDependencies": {"@types/express": "^5.0.3", "@types/node": "^24.0.10", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}