import express from "express";
import http from "http";
import { Server, Socket } from "socket.io";

const app = express();
const server = http.createServer(app);
const io = new Server(server, {
  cors: {
    origin: "*",
  },
});

// Express route
app.get("/", (_req, res) => {
  res.send("Socket.IO Room Server is running (TS)!");
});

// Type-safe event structure
interface PingData {
  roomId: string;
  message: string;
}

type GameState = 'idle' | 'running' | 'paused' | 'ended';
type Prize = 'earlyFive' | 'topLine' | 'middleLine' | 'bottomLine' | 'fullHouse';
type PrizeWinner = { ticketIndex: number };
type Ticket = {
  content: (number | null)[][],
  belongsTo: string;
};
type Settings = {
  numTickets: number;
  speed: number;
  isAutoPlay: boolean;
  isMuted: boolean;
};
type BingoState = {
  gameState: GameState;
  tickets: Ticket[];
  players: Array<String>;
  allNumbers: number[];
  calledNumbers: Set<number>;
  calledNumbersHistory: number[];
  currentNumber: number | null;
  prizeWinners: Partial<Record<Prize, PrizeWinner>>;
  settings: Settings;
  isAdmin: boolean;
};

var room_list: Array<string> = []

const ticketCooldowns = new Map<string, number>();
const TICKET_COOLDOWN_MS = 5000; // 5 seconds cooldown

io.on("connection", (socket: Socket) => {
  console.log(`Client connected: ${socket.id}`);

  socket.on("join_room", (roomId: string) => {
	const room = io.sockets.adapter.rooms.get(roomId);
	const isFirst = !room || room.size === 0;
    socket.join(roomId);
    console.log(`Client ${socket.id} joined room ${roomId} (first: ${isFirst})`);
    if (!room_list.includes(roomId)) {
      room_list.push(roomId);
      socket.emit("room_created", room_list);
    }
    socket.to(roomId).emit("user_joined", {id: socket.id, isFirst: isFirst});
  });

  socket.on("get_rooms", () => {
	socket.emit("rooms_list", room_list);
  })

  socket.on("leave_room", (roomId: string) => {
    socket.leave(roomId);
    console.log(`Client ${socket.id} left room ${roomId}`);
    socket.to(roomId).emit("user_left", socket.id);
  });

  socket.on("update-game-state", (data: string) => {
    let parsed = JSON.parse(data);
    socket.to(parsed.roomId).emit("update-game-state", { ...parsed.state, isAdmin: false });
  });

  socket.on("add-ticket", (data: string) => {
    let parsed = JSON.parse(data);
    const currentTime = Date.now();

    if (ticketCooldowns.has(parsed.ticket.belongsTo)) {
      const lastPurchaseTime = ticketCooldowns.get(parsed.ticket.belongsTo)!;
      if (currentTime - lastPurchaseTime < TICKET_COOLDOWN_MS) {
        return;
      }
    }
    ticketCooldowns.set(parsed.ticket.belongsTo, currentTime);

    console.log(`Received ticket update: ${parsed.ticket.belongsTo}`);
    socket.to(parsed.roomId).emit("ticket-added", parsed.ticket);
  });

  socket.on("disconnect", () => {
    console.log(`Client disconnected: ${socket.id}`);
  });
});

// Start server
const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
  console.log(`✅ Server running at http://localhost:${PORT}`);
});
