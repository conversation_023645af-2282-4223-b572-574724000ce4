import express from "express";
import http from "http";
import { Server, Socket } from "socket.io";

const app = express();
const server = http.createServer(app);
const io = new Server(server, {
  cors: {
    origin: "*",
  },
});

// Express route
app.get("/", (_req, res) => {
  res.send("Socket.IO Room Server is running (TS)!");
});

// Type-safe event structure
interface PingData {
  roomId: string;
  message: string;
}

var room_list: Array<string> = []

io.on("connection", (socket: Socket) => {
  console.log(`Client connected: ${socket.id}`);

  socket.on("join_room", (roomId: string) => {
	const room = io.sockets.adapter.rooms.get(roomId);
	const isFirst = !room || room.size === 0;
    socket.join(roomId);
    console.log(`Client ${socket.id} joined room ${roomId} (first: ${isFirst})`);
    if (!room_list.includes(roomId)) {
      room_list.push(roomId);
      socket.emit("room_created", room_list);
    }
    socket.to(roomId).emit("user_joined", {id: socket.id, isFirst: isFirst});
  });

  socket.on("send_ping", (data: PingData) => {
    console.log(`Ping from ${socket.id} to room ${data.roomId}: ${data.message}`);
    socket.to(data.roomId).emit("receive_ping", {
      from: socket.id,
      message: data.message || "ping",
    });
  });

  socket.on("get_rooms", () => {
	socket.emit("rooms_list", room_list);
  })

  socket.on("leave_room", (roomId: string) => {
    socket.leave(roomId);
    console.log(`Client ${socket.id} left room ${roomId}`);
    socket.to(roomId).emit("user_left", socket.id);
  });

  socket.on("update-game-state", (data: { roomId: string; state: any }) => {
    socket.to(data.roomId).emit("update-game-state", data.state);
  });

  socket.on("disconnect", () => {
    console.log(`Client disconnected: ${socket.id}`);
  });
});

// Start server
const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
  console.log(`✅ Server running at http://localhost:${PORT}`);
});
