import { v4 } from "uuid";

export type Ticket = {
  content: (number | null)[][],
  belongsTo: string,
  id: string,
};
export type Prize = 'earlyFive' | 'topLine' | 'middleLine' | 'bottomLine' | 'fullHouse';
export type PrizeWinner = { belongsTo: string; ticketIndex: number };

export const PRIZES: Record<Prize, string> = {
  earlyFive: 'Early Five',
  topLine: 'Top Line',
  middleLine: 'Middle Line',
  bottomLine: 'Bottom Line',
  fullHouse: 'Full House',
};

function shuffle<T>(array: T[]): T[] {
  let currentIndex = array.length,  randomIndex;
  while (currentIndex !== 0) {
    randomIndex = Math.floor(Math.random() * currentIndex);
    currentIndex--;
    [array[currentIndex], array[randomIndex]] = [array[randomIndex], array[currentIndex]];
  }
  return array;
}

export function generateTickets(belongsTo: string): Ticket[] {
  const tickets: Ticket[] = [];
  tickets.push(generateSingleTicket(belongsTo));
  return tickets;
}

function generateSingleTicket(belongsTo: string): Ticket {
  const ticket: Ticket = { content: Array.from({ length: 3 }, () => Array(9).fill(null)), belongsTo, id: v4() };
  const numbers = new Set<number>();

  // Determine column fills: 6 columns get 1 number, 3 columns get 2 numbers
  const columns = shuffle(Array.from({length: 9}, (_, i) => i));
  const colsWithTwo = columns.slice(0, 3);
  const colsWithOne = columns.slice(3, 9);
  
  const colFills: number[] = Array(9).fill(0);
  colsWithTwo.forEach(c => colFills[c] = 2);
  colsWithOne.forEach(c => colFills[c] = 1);

  // Place numbers
  for (let col = 0; col < 9; col++) {
    const numbersInCol = colFills[col];
    const min = col * 10 + 1;
    const max = col * 10 + 10;
    const availableNumbers = shuffle(Array.from({ length: max - min }, (_, i) => min + i));
    const rows = shuffle([0, 1, 2]);

    for (let i = 0; i < numbersInCol; i++) {
        ticket.content[rows[i]][col] = availableNumbers[i];
    }
  }

  // Ensure each row has 5 numbers
  for (let row = 0; row < 3; row++) {
    let numbersInRow = ticket.content[row].filter(n => n !== null).length;
    while (numbersInRow < 5) {
        let emptyCol = -1;
        // find an empty column in this row that can accept a number
        const potentialCols = shuffle(Array.from({length: 9}, (_, i) => i));
        for(const col of potentialCols) {
            if(ticket.content[row][col] === null && colFills[col] < 3) {
                emptyCol = col;
                break;
            }
        }

        if(emptyCol !== -1) {
            const min = emptyCol * 10 + 1;
            const max = emptyCol * 10 + 10;
            const availableNumbers = shuffle(Array.from({ length: max - min }, (_, i) => min + i));

            let numToAdd;
            let attempt = 0;
            do {
                numToAdd = availableNumbers[attempt++];
            } while (isNumberInTicket(ticket, numToAdd) && attempt < availableNumbers.length);
            
            if (numToAdd && !isNumberInTicket(ticket, numToAdd)) {
                ticket.content[row][emptyCol] = numToAdd;
                colFills[emptyCol]++;
                numbersInRow++;
            }
        } else {
            // Failsafe, should not be hit in normal generation
            break;
        }
    }
  }

  return ticket;
}

function isNumberInTicket(ticket: Ticket, num: number): boolean {
    return ticket.content.some(row => row.includes(num));
}


export function checkPrizes(ticket: Ticket, calledNumbers: Array<number>): Partial<Record<Prize, boolean>> {
  const results: Partial<Record<Prize, boolean>> = {};
  const ticketNumbers = ticket.content.flat().filter((n): n is number => n !== null);

  const markedCount = ticketNumbers.filter(n => calledNumbers.includes(n)).length;

  // Early Five
  if (markedCount >= 5) {
    results.earlyFive = true;
  }

  // Top Line
  const topLineNumbers = ticket.content[0].filter((n): n is number => n !== null);
  if (topLineNumbers.every(n => calledNumbers.includes(n))) {
    results.topLine = true;
  }

  // Middle Line
  const middleLineNumbers = ticket.content[1].filter((n): n is number => n !== null);
  if (middleLineNumbers.every(n => calledNumbers.includes(n))) {
    results.middleLine = true;
  }

  // Bottom Line
  const bottomLineNumbers = ticket.content[2].filter((n): n is number => n !== null);
  if (bottomLineNumbers.every(n => calledNumbers.includes(n))) {
    results.bottomLine = true;
  }
  
  // Full House
  if (ticketNumbers.every(n => calledNumbers.includes(n))) {
    results.fullHouse = true;
  }

  return results;
}
