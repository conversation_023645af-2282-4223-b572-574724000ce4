@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
 
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
 
    --primary: 274 87% 50%;
    --primary-foreground: 0 0% 98%;
 
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 215.4 16.3% 46.9%;
 
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
 
    --accent: 183 96% 63%;
    --accent-foreground: 222.2 47.4% 11.2%;
 
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 274 87% 50%;
 
    --radius: 0.5rem;
  }
 
  .dark {
    --background: 222 47% 11%;
    --foreground: 0 0% 98%;

    --card: 222 47% 13%;
    --card-foreground: 0 0% 98%;
 
    --popover: 222 47% 11%;
    --popover-foreground: 0 0% 98%;
 
    --primary: 274 87% 56%;
    --primary-foreground: 0 0% 98%;
 
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 0 0% 98%;
 
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215.4 16.3% 56.9%;
 
    --accent: 183 96% 63%;
    --accent-foreground: 222.2 47.4% 1.2%;
 
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
 
    --border: 217.2 32.6% 22.5%;
    --input: 217.2 32.6% 22.5%;
    --ring: 183 96% 63%;
  }
}
 
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .glow-primary {
    --glow-color: hsl(var(--primary));
    text-shadow: 0 0 2px var(--glow-color), 0 0 5px var(--glow-color), 0 0 10px var(--glow-color);
  }
  .glow-accent {
    --glow-color: hsl(var(--accent));
    text-shadow: 0 0 2px var(--glow-color), 0 0 5px var(--glow-color), 0 0 10px var(--glow-color);
  }

  .caller-animation {
    animation: caller-reveal 0.5s cubic-bezier(0.25, 1, 0.5, 1) forwards, caller-glow 2s ease-in-out infinite alternate;
  }
  
  @keyframes caller-reveal {
    from {
      transform: translateY(-50%) scale(0.5);
      opacity: 0;
    }
    to {
      transform: translateY(0) scale(1);
      opacity: 1;
    }
  }

  @keyframes caller-glow {
    from {
      text-shadow: 0 0 10px hsl(var(--accent) / 0.8), 0 0 20px hsl(var(--accent) / 0.6), 0 0 40px hsl(var(--primary) / 0.6);
      filter: drop-shadow(0 0 10px hsl(var(--accent)));
    }
    to {
      text-shadow: 0 0 20px hsl(var(--accent) / 0.8), 0 0 40px hsl(var(--accent) / 0.6), 0 0 60px hsl(var(--primary) / 0.6);
      filter: drop-shadow(0 0 15px hsl(var(--accent)));
    }
  }

  .ticket-perspective {
    transform-style: preserve-3d;
    perspective: 1000px;
  }

  .ticket-3d {
    transition: transform 0.5s cubic-bezier(0.25, 1, 0.5, 1);
    transform: rotateY(var(--rotate-y, 0)) rotateX(var(--rotate-x, 0));
  }
}
