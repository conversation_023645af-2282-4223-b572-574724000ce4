"use client";

import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { PersonStanding } from 'lucide-react';
import { cn } from '@/lib/utils';

export function PlayerList({ players }: { players: Array<String> }) {
  return (
    <Card className="bg-card/50 backdrop-blur-sm h-full">
      <CardHeader>
        <CardTitle className="font-headline text-2xl flex items-center gap-2 glow-primary">
          <PersonStanding className='w-6 h-6' />
          Players
        </CardTitle>
      </CardHeader>
      <CardContent>
        <ul className="space-y-3">
          {players.map((player, index) => (
            <li className={cn('transition-all duration-300')} key={index}>
              <span className='font-bold text-lg'>
                {player}
              </span>
              {index < players.length - 1 && <Separator className='mt-2' />}
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  );
}
