Project Objective:
Build a modern, animated, multiplayer bingo Game, featuring smooth gameplay, automated prize detection, admin controls, sound.

Core Features:

Gameplay:
- [x] Multiplayer Bingo game with:
  - [x] modern visual aesthetics
  - [x] Realistic 3x9 tickets:
    - [x] 3 rows, 9 columns per ticket
    - [x] 15 randomized numbers per ticket (standard Bingo rules)
    - [x] Accurate distribution (5 numbers per row)
    - [x] Visually elevated or glowing ticket layout
  - [x] Players can own one or multiple tickets (configurable per game)
  - [x] Real-time number matching on player tickets

Number Calling:
- [x] Randomized number calling from 1 to 90 (no repeats)
- [x] High-quality animated number reveal:
  - [x] Rolling glowing ball, rotating coin, or futuristic hologram effect
  - [x] Smooth visual entry for each called number
  - [x] Highlight called number on the number board (1-90)
  - [x] Animate matching numbers directly on player tickets
- Voice-based number announcement:
  - [x] Example: "Number 45 - Forty Five"
  - [x] Configurable voice options (male/female)
  - [ ] Optional sound toggle for players

Prize Detection:
- [x] Automated detection for standard prizes:
  - [x] Early Five (first 5 numbers marked)
  - [x] Top Line (complete first row)
  - [x] Middle Line (complete second row)
  - [x] Bottom Line (complete third row)
  - [x] Full House (all 15 numbers)
- [x] System highlights winning ticket(s)
- [x] Visual and sound effects on win detection
- [x] Prevent multiple claims for the same prize

Visuals & UI:
- [x] game environment:
  - [x] Floating number grid or futuristic control panel for 1-90 numbers
  - [x] Elevated, glowing, or visually polished 3D tickets
  - [x] Smooth animations, transitions, and effects
- [x] Modern, vibrant, colorful UI
- [x] Light and dark mode toggle
- [ ] Optional background music with mute option
- [x] Visual celebration effects for prize winners

Admin Controls:
- [x] Hidden/restricted admin panel with:
  - [x] Start, Pause, Resume, or End game controls
  - [x] Manual or auto number calling (configurable delay)
  - [x] Reset entire game state
  - [x] View prize claims in real time
  - [x] Sound/animation toggles
- [x] Designed for easy future Web3 admin upgrades

- [x] Modern, clean aesthetics inspired by:
  - [x] Futuristic hologram interfaces
  - [x] Polished casino/Bingo environments
  - [x] Vibrant color schemes, smooth UI/UX
Visual Inspiration:
- [x] Avoid outdated or flat designs
- [x] Smooth transitions, glowing elements, ticket layouts