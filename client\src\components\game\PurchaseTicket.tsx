"use client";

import React, { useEffect } from 'react';
import { useBingoEngine } from '@/hooks/use-bingo-engine';
import { getSocket } from '@/lib/socket';
import { Button } from '../ui/button';
import { Ticket } from 'lucide-react';

const PurchaseTicket = () => {
	const { state, dispatch } = useBingoEngine();
	const socket = getSocket();

  return (
    <div>
          <Button
            className="w-full"
            variant="default"
						disabled={state.gameState !== 'idle'}
            onClick={() => {
              console.log("add ticket")
              dispatch({ type: 'PURCHASE_TICKET', socket })
            }}
          >
						<Ticket className="mr-2 h-4 w-4" />
            Purchase Ticket
          </Button>
    </div>
  )
}

export default PurchaseTicket