"use client";

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';

type BingoBoardProps = {
  calledNumbers: Array<number>;
};

export function BingoBoard({ calledNumbers }: BingoBoardProps) {
  const numbers = Array.from({ length: 90 }, (_, i) => i + 1);

  return (
    <Card className="bg-card/50 backdrop-blur-sm">
      <CardContent className="p-4">
        <div className="grid grid-cols-10 md:grid-cols-15 lg:grid-cols-18 xl:grid-cols-20 gap-2">
          {numbers.map((number) => {
            const isCalled = calledNumbers.includes(number);
            return (
              <div
                key={number}
                className={cn(
                  'flex items-center justify-center aspect-square rounded-full text-sm md:text-base font-bold transition-all duration-300',
                  isCalled
                    ? 'bg-primary text-primary-foreground scale-110 shadow-lg shadow-primary/50'
                    : 'bg-secondary text-secondary-foreground'
                )}
              >
                {number}
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
