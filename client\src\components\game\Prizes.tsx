"use client";

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Trophy, CheckCircle2 } from 'lucide-react';
import type { Prize, PrizeWinner } from '@/lib/bingo';
import { PRIZES } from '@/lib/bingo';
import { cn } from '@/lib/utils';


type PrizesProps = {
  prizeWinners: Partial<Record<Prize, PrizeWinner>>;
};

export function Prizes({ prizeWinners }: PrizesProps) {
  return (
    <Card className="bg-card/50 backdrop-blur-sm h-full">
      <CardHeader>
        <CardTitle className="font-headline text-2xl flex items-center gap-2 glow-primary">
          <Trophy className="w-6 h-6" />
          Prizes
        </CardTitle>
      </CardHeader>
      <CardContent>
        <ul className="space-y-3">
          {(Object.keys(PRIZES) as Prize[]).map((prizeKey, index) => {
            const winner = prizeWinners[prizeKey];
            const isWon = !!winner;
            return (
              <React.Fragment key={prizeKey}>
                <li
                  className={cn(
                    'flex justify-between items-center transition-all duration-300',
                    isWon ? 'text-accent' : 'text-foreground'
                  )}
                >
                  <span className="font-bold text-lg">{PRIZES[prizeKey]}</span>
                  {isWon ? (
                    <div className="flex items-center gap-2 text-sm">
                      <span>Ticket #{winner.ticketIndex + 1}</span>
                      <span>{winner.belongsTo}</span>
                    </div>
                  ) : (
                    <span className="text-sm text-muted-foreground">Pending</span>
                  )}
                </li>
                {index < Object.keys(PRIZES).length - 1 && <Separator />}
              </React.Fragment>
            );
          })}
        </ul>
      </CardContent>
    </Card>
  );
}
