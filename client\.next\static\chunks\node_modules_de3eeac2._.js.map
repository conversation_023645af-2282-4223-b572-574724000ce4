{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/engine.io-parser/build/esm/commons.js"], "sourcesContent": ["const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach((key) => {\n    PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\nconst ERROR_PACKET = { type: \"error\", data: \"parser error\" };\nexport { PACKET_TYPES, PACKET_TYPES_REVERSE, ERROR_PACKET };\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,eAAe,OAAO,MAAM,CAAC,OAAO,uBAAuB;AACjE,YAAY,CAAC,OAAO,GAAG;AACvB,YAAY,CAAC,QAAQ,GAAG;AACxB,YAAY,CAAC,OAAO,GAAG;AACvB,YAAY,CAAC,OAAO,GAAG;AACvB,YAAY,CAAC,UAAU,GAAG;AAC1B,YAAY,CAAC,UAAU,GAAG;AAC1B,YAAY,CAAC,OAAO,GAAG;AACvB,MAAM,uBAAuB,OAAO,MAAM,CAAC;AAC3C,OAAO,IAAI,CAAC,cAAc,OAAO,CAAC,CAAC;IAC/B,oBAAoB,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG;AAC9C;AACA,MAAM,eAAe;IAAE,MAAM;IAAS,MAAM;AAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/engine.io-parser/build/esm/encodePacket.browser.js"], "sourcesContent": ["import { PACKET_TYPES } from \"./commons.js\";\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = (obj) => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj && obj.buffer instanceof ArrayBuffer;\n};\nconst encodePacket = ({ type, data }, supportsBinary, callback) => {\n    if (withNativeBlob && data instanceof Blob) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(data, callback);\n        }\n    }\n    else if (withNativeArrayBuffer &&\n        (data instanceof ArrayBuffer || isView(data))) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(new Blob([data]), callback);\n        }\n    }\n    // plain string\n    return callback(PACKET_TYPES[type] + (data || \"\"));\n};\nconst encodeBlobAsBase64 = (data, callback) => {\n    const fileReader = new FileReader();\n    fileReader.onload = function () {\n        const content = fileReader.result.split(\",\")[1];\n        callback(\"b\" + (content || \"\"));\n    };\n    return fileReader.readAsDataURL(data);\n};\nfunction toArray(data) {\n    if (data instanceof Uint8Array) {\n        return data;\n    }\n    else if (data instanceof ArrayBuffer) {\n        return new Uint8Array(data);\n    }\n    else {\n        return new Uint8Array(data.buffer, data.byteOffset, data.byteLength);\n    }\n}\nlet TEXT_ENCODER;\nexport function encodePacketToBinary(packet, callback) {\n    if (withNativeBlob && packet.data instanceof Blob) {\n        return packet.data.arrayBuffer().then(toArray).then(callback);\n    }\n    else if (withNativeArrayBuffer &&\n        (packet.data instanceof ArrayBuffer || isView(packet.data))) {\n        return callback(toArray(packet.data));\n    }\n    encodePacket(packet, false, (encoded) => {\n        if (!TEXT_ENCODER) {\n            TEXT_ENCODER = new TextEncoder();\n        }\n        callback(TEXT_ENCODER.encode(encoded));\n    });\n}\nexport { encodePacket };\n"], "names": [], "mappings": ";;;;AAAA;;AACA,MAAM,iBAAiB,OAAO,SAAS,cAClC,OAAO,SAAS,eACb,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU;AACjD,MAAM,wBAAwB,OAAO,gBAAgB;AACrD,mDAAmD;AACnD,MAAM,SAAS,CAAC;IACZ,OAAO,OAAO,YAAY,MAAM,KAAK,aAC/B,YAAY,MAAM,CAAC,OACnB,OAAO,IAAI,MAAM,YAAY;AACvC;AACA,MAAM,eAAe,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,gBAAgB;IAClD,IAAI,kBAAkB,gBAAgB,MAAM;QACxC,IAAI,gBAAgB;YAChB,OAAO,SAAS;QACpB,OACK;YACD,OAAO,mBAAmB,MAAM;QACpC;IACJ,OACK,IAAI,yBACL,CAAC,gBAAgB,eAAe,OAAO,KAAK,GAAG;QAC/C,IAAI,gBAAgB;YAChB,OAAO,SAAS;QACpB,OACK;YACD,OAAO,mBAAmB,IAAI,KAAK;gBAAC;aAAK,GAAG;QAChD;IACJ;IACA,eAAe;IACf,OAAO,SAAS,oKAAA,CAAA,eAAY,CAAC,KAAK,GAAG,CAAC,QAAQ,EAAE;AACpD;AACA,MAAM,qBAAqB,CAAC,MAAM;IAC9B,MAAM,aAAa,IAAI;IACvB,WAAW,MAAM,GAAG;QAChB,MAAM,UAAU,WAAW,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QAC/C,SAAS,MAAM,CAAC,WAAW,EAAE;IACjC;IACA,OAAO,WAAW,aAAa,CAAC;AACpC;AACA,SAAS,QAAQ,IAAI;IACjB,IAAI,gBAAgB,YAAY;QAC5B,OAAO;IACX,OACK,IAAI,gBAAgB,aAAa;QAClC,OAAO,IAAI,WAAW;IAC1B,OACK;QACD,OAAO,IAAI,WAAW,KAAK,MAAM,EAAE,KAAK,UAAU,EAAE,KAAK,UAAU;IACvE;AACJ;AACA,IAAI;AACG,SAAS,qBAAqB,MAAM,EAAE,QAAQ;IACjD,IAAI,kBAAkB,OAAO,IAAI,YAAY,MAAM;QAC/C,OAAO,OAAO,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,SAAS,IAAI,CAAC;IACxD,OACK,IAAI,yBACL,CAAC,OAAO,IAAI,YAAY,eAAe,OAAO,OAAO,IAAI,CAAC,GAAG;QAC7D,OAAO,SAAS,QAAQ,OAAO,IAAI;IACvC;IACA,aAAa,QAAQ,OAAO,CAAC;QACzB,IAAI,CAAC,cAAc;YACf,eAAe,IAAI;QACvB;QACA,SAAS,aAAa,MAAM,CAAC;IACjC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/engine.io-parser/build/esm/contrib/base64-arraybuffer.js"], "sourcesContent": ["// imported from https://github.com/socketio/base64-arraybuffer\nconst chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n// Use a lookup table to find the index.\nconst lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (let i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\nexport const encode = (arraybuffer) => {\n    let bytes = new Uint8Array(arraybuffer), i, len = bytes.length, base64 = '';\n    for (i = 0; i < len; i += 3) {\n        base64 += chars[bytes[i] >> 2];\n        base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n        base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n        base64 += chars[bytes[i + 2] & 63];\n    }\n    if (len % 3 === 2) {\n        base64 = base64.substring(0, base64.length - 1) + '=';\n    }\n    else if (len % 3 === 1) {\n        base64 = base64.substring(0, base64.length - 2) + '==';\n    }\n    return base64;\n};\nexport const decode = (base64) => {\n    let bufferLength = base64.length * 0.75, len = base64.length, i, p = 0, encoded1, encoded2, encoded3, encoded4;\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n    const arraybuffer = new ArrayBuffer(bufferLength), bytes = new Uint8Array(arraybuffer);\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n        bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n        bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n        bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n    return arraybuffer;\n};\n"], "names": [], "mappings": "AAAA,+DAA+D;;;;;AAC/D,MAAM,QAAQ;AACd,wCAAwC;AACxC,MAAM,SAAS,OAAO,eAAe,cAAc,EAAE,GAAG,IAAI,WAAW;AACvE,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;IACnC,MAAM,CAAC,MAAM,UAAU,CAAC,GAAG,GAAG;AAClC;AACO,MAAM,SAAS,CAAC;IACnB,IAAI,QAAQ,IAAI,WAAW,cAAc,GAAG,MAAM,MAAM,MAAM,EAAE,SAAS;IACzE,IAAK,IAAI,GAAG,IAAI,KAAK,KAAK,EAAG;QACzB,UAAU,KAAK,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE;QAC9B,UAAU,KAAK,CAAC,AAAC,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,KAAK,IAAM,KAAK,CAAC,IAAI,EAAE,IAAI,EAAG;QAC5D,UAAU,KAAK,CAAC,AAAC,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,IAAM,KAAK,CAAC,IAAI,EAAE,IAAI,EAAG;QACjE,UAAU,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,GAAG;IACtC;IACA,IAAI,MAAM,MAAM,GAAG;QACf,SAAS,OAAO,SAAS,CAAC,GAAG,OAAO,MAAM,GAAG,KAAK;IACtD,OACK,IAAI,MAAM,MAAM,GAAG;QACpB,SAAS,OAAO,SAAS,CAAC,GAAG,OAAO,MAAM,GAAG,KAAK;IACtD;IACA,OAAO;AACX;AACO,MAAM,SAAS,CAAC;IACnB,IAAI,eAAe,OAAO,MAAM,GAAG,MAAM,MAAM,OAAO,MAAM,EAAE,GAAG,IAAI,GAAG,UAAU,UAAU,UAAU;IACtG,IAAI,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,KAAK,KAAK;QACnC;QACA,IAAI,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,KAAK,KAAK;YACnC;QACJ;IACJ;IACA,MAAM,cAAc,IAAI,YAAY,eAAe,QAAQ,IAAI,WAAW;IAC1E,IAAK,IAAI,GAAG,IAAI,KAAK,KAAK,EAAG;QACzB,WAAW,MAAM,CAAC,OAAO,UAAU,CAAC,GAAG;QACvC,WAAW,MAAM,CAAC,OAAO,UAAU,CAAC,IAAI,GAAG;QAC3C,WAAW,MAAM,CAAC,OAAO,UAAU,CAAC,IAAI,GAAG;QAC3C,WAAW,MAAM,CAAC,OAAO,UAAU,CAAC,IAAI,GAAG;QAC3C,KAAK,CAAC,IAAI,GAAG,AAAC,YAAY,IAAM,YAAY;QAC5C,KAAK,CAAC,IAAI,GAAG,AAAC,CAAC,WAAW,EAAE,KAAK,IAAM,YAAY;QACnD,KAAK,CAAC,IAAI,GAAG,AAAC,CAAC,WAAW,CAAC,KAAK,IAAM,WAAW;IACrD;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/engine.io-parser/build/esm/decodePacket.browser.js"], "sourcesContent": ["import { ERROR_PACKET, PACKET_TYPES_REVERSE, } from \"./commons.js\";\nimport { decode } from \"./contrib/base64-arraybuffer.js\";\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nexport const decodePacket = (encodedPacket, binaryType) => {\n    if (typeof encodedPacket !== \"string\") {\n        return {\n            type: \"message\",\n            data: mapBinary(encodedPacket, binaryType),\n        };\n    }\n    const type = encodedPacket.charAt(0);\n    if (type === \"b\") {\n        return {\n            type: \"message\",\n            data: decodeBase64Packet(encodedPacket.substring(1), binaryType),\n        };\n    }\n    const packetType = PACKET_TYPES_REVERSE[type];\n    if (!packetType) {\n        return ERROR_PACKET;\n    }\n    return encodedPacket.length > 1\n        ? {\n            type: PACKET_TYPES_REVERSE[type],\n            data: encodedPacket.substring(1),\n        }\n        : {\n            type: PACKET_TYPES_REVERSE[type],\n        };\n};\nconst decodeBase64Packet = (data, binaryType) => {\n    if (withNativeArrayBuffer) {\n        const decoded = decode(data);\n        return mapBinary(decoded, binaryType);\n    }\n    else {\n        return { base64: true, data }; // fallback for old browsers\n    }\n};\nconst mapBinary = (data, binaryType) => {\n    switch (binaryType) {\n        case \"blob\":\n            if (data instanceof Blob) {\n                // from WebSocket + binaryType \"blob\"\n                return data;\n            }\n            else {\n                // from HTTP long-polling or WebTransport\n                return new Blob([data]);\n            }\n        case \"arraybuffer\":\n        default:\n            if (data instanceof ArrayBuffer) {\n                // from HTTP long-polling (base64) or WebSocket + binaryType \"arraybuffer\"\n                return data;\n            }\n            else {\n                // from WebTransport (Uint8Array)\n                return data.buffer;\n            }\n    }\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACA,MAAM,wBAAwB,OAAO,gBAAgB;AAC9C,MAAM,eAAe,CAAC,eAAe;IACxC,IAAI,OAAO,kBAAkB,UAAU;QACnC,OAAO;YACH,MAAM;YACN,MAAM,UAAU,eAAe;QACnC;IACJ;IACA,MAAM,OAAO,cAAc,MAAM,CAAC;IAClC,IAAI,SAAS,KAAK;QACd,OAAO;YACH,MAAM;YACN,MAAM,mBAAmB,cAAc,SAAS,CAAC,IAAI;QACzD;IACJ;IACA,MAAM,aAAa,oKAAA,CAAA,uBAAoB,CAAC,KAAK;IAC7C,IAAI,CAAC,YAAY;QACb,OAAO,oKAAA,CAAA,eAAY;IACvB;IACA,OAAO,cAAc,MAAM,GAAG,IACxB;QACE,MAAM,oKAAA,CAAA,uBAAoB,CAAC,KAAK;QAChC,MAAM,cAAc,SAAS,CAAC;IAClC,IACE;QACE,MAAM,oKAAA,CAAA,uBAAoB,CAAC,KAAK;IACpC;AACR;AACA,MAAM,qBAAqB,CAAC,MAAM;IAC9B,IAAI,uBAAuB;QACvB,MAAM,UAAU,CAAA,GAAA,6LAAA,CAAA,SAAM,AAAD,EAAE;QACvB,OAAO,UAAU,SAAS;IAC9B,OACK;QACD,OAAO;YAAE,QAAQ;YAAM;QAAK,GAAG,4BAA4B;IAC/D;AACJ;AACA,MAAM,YAAY,CAAC,MAAM;IACrB,OAAQ;QACJ,KAAK;YACD,IAAI,gBAAgB,MAAM;gBACtB,qCAAqC;gBACrC,OAAO;YACX,OACK;gBACD,yCAAyC;gBACzC,OAAO,IAAI,KAAK;oBAAC;iBAAK;YAC1B;QACJ,KAAK;QACL;YACI,IAAI,gBAAgB,aAAa;gBAC7B,0EAA0E;gBAC1E,OAAO;YACX,OACK;gBACD,iCAAiC;gBACjC,OAAO,KAAK,MAAM;YACtB;IACR;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 229, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/engine.io-parser/build/esm/index.js"], "sourcesContent": ["import { encodePacket, encodePacketToBinary } from \"./encodePacket.js\";\nimport { decodePacket } from \"./decodePacket.js\";\nimport { ERROR_PACKET, } from \"./commons.js\";\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\nconst encodePayload = (packets, callback) => {\n    // some packets may be added to the array while encoding, so the initial length must be saved\n    const length = packets.length;\n    const encodedPackets = new Array(length);\n    let count = 0;\n    packets.forEach((packet, i) => {\n        // force base64 encoding for binary packets\n        encodePacket(packet, false, (encodedPacket) => {\n            encodedPackets[i] = encodedPacket;\n            if (++count === length) {\n                callback(encodedPackets.join(SEPARATOR));\n            }\n        });\n    });\n};\nconst decodePayload = (encodedPayload, binaryType) => {\n    const encodedPackets = encodedPayload.split(SEPARATOR);\n    const packets = [];\n    for (let i = 0; i < encodedPackets.length; i++) {\n        const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n        packets.push(decodedPacket);\n        if (decodedPacket.type === \"error\") {\n            break;\n        }\n    }\n    return packets;\n};\nexport function createPacketEncoderStream() {\n    return new TransformStream({\n        transform(packet, controller) {\n            encodePacketToBinary(packet, (encodedPacket) => {\n                const payloadLength = encodedPacket.length;\n                let header;\n                // inspired by the WebSocket format: https://developer.mozilla.org/en-US/docs/Web/API/WebSockets_API/Writing_WebSocket_servers#decoding_payload_length\n                if (payloadLength < 126) {\n                    header = new Uint8Array(1);\n                    new DataView(header.buffer).setUint8(0, payloadLength);\n                }\n                else if (payloadLength < 65536) {\n                    header = new Uint8Array(3);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 126);\n                    view.setUint16(1, payloadLength);\n                }\n                else {\n                    header = new Uint8Array(9);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 127);\n                    view.setBigUint64(1, BigInt(payloadLength));\n                }\n                // first bit indicates whether the payload is plain text (0) or binary (1)\n                if (packet.data && typeof packet.data !== \"string\") {\n                    header[0] |= 0x80;\n                }\n                controller.enqueue(header);\n                controller.enqueue(encodedPacket);\n            });\n        },\n    });\n}\nlet TEXT_DECODER;\nfunction totalLength(chunks) {\n    return chunks.reduce((acc, chunk) => acc + chunk.length, 0);\n}\nfunction concatChunks(chunks, size) {\n    if (chunks[0].length === size) {\n        return chunks.shift();\n    }\n    const buffer = new Uint8Array(size);\n    let j = 0;\n    for (let i = 0; i < size; i++) {\n        buffer[i] = chunks[0][j++];\n        if (j === chunks[0].length) {\n            chunks.shift();\n            j = 0;\n        }\n    }\n    if (chunks.length && j < chunks[0].length) {\n        chunks[0] = chunks[0].slice(j);\n    }\n    return buffer;\n}\nexport function createPacketDecoderStream(maxPayload, binaryType) {\n    if (!TEXT_DECODER) {\n        TEXT_DECODER = new TextDecoder();\n    }\n    const chunks = [];\n    let state = 0 /* State.READ_HEADER */;\n    let expectedLength = -1;\n    let isBinary = false;\n    return new TransformStream({\n        transform(chunk, controller) {\n            chunks.push(chunk);\n            while (true) {\n                if (state === 0 /* State.READ_HEADER */) {\n                    if (totalLength(chunks) < 1) {\n                        break;\n                    }\n                    const header = concatChunks(chunks, 1);\n                    isBinary = (header[0] & 0x80) === 0x80;\n                    expectedLength = header[0] & 0x7f;\n                    if (expectedLength < 126) {\n                        state = 3 /* State.READ_PAYLOAD */;\n                    }\n                    else if (expectedLength === 126) {\n                        state = 1 /* State.READ_EXTENDED_LENGTH_16 */;\n                    }\n                    else {\n                        state = 2 /* State.READ_EXTENDED_LENGTH_64 */;\n                    }\n                }\n                else if (state === 1 /* State.READ_EXTENDED_LENGTH_16 */) {\n                    if (totalLength(chunks) < 2) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 2);\n                    expectedLength = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length).getUint16(0);\n                    state = 3 /* State.READ_PAYLOAD */;\n                }\n                else if (state === 2 /* State.READ_EXTENDED_LENGTH_64 */) {\n                    if (totalLength(chunks) < 8) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 8);\n                    const view = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length);\n                    const n = view.getUint32(0);\n                    if (n > Math.pow(2, 53 - 32) - 1) {\n                        // the maximum safe integer in JavaScript is 2^53 - 1\n                        controller.enqueue(ERROR_PACKET);\n                        break;\n                    }\n                    expectedLength = n * Math.pow(2, 32) + view.getUint32(4);\n                    state = 3 /* State.READ_PAYLOAD */;\n                }\n                else {\n                    if (totalLength(chunks) < expectedLength) {\n                        break;\n                    }\n                    const data = concatChunks(chunks, expectedLength);\n                    controller.enqueue(decodePacket(isBinary ? data : TEXT_DECODER.decode(data), binaryType));\n                    state = 0 /* State.READ_HEADER */;\n                }\n                if (expectedLength === 0 || expectedLength > maxPayload) {\n                    controller.enqueue(ERROR_PACKET);\n                    break;\n                }\n            }\n        },\n    });\n}\nexport const protocol = 4;\nexport { encodePacket, encodePayload, decodePacket, decodePayload, };\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;AACA,MAAM,YAAY,OAAO,YAAY,CAAC,KAAK,mEAAmE;AAC9G,MAAM,gBAAgB,CAAC,SAAS;IAC5B,6FAA6F;IAC7F,MAAM,SAAS,QAAQ,MAAM;IAC7B,MAAM,iBAAiB,IAAI,MAAM;IACjC,IAAI,QAAQ;IACZ,QAAQ,OAAO,CAAC,CAAC,QAAQ;QACrB,2CAA2C;QAC3C,CAAA,GAAA,oLAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,OAAO,CAAC;YACzB,cAAc,CAAC,EAAE,GAAG;YACpB,IAAI,EAAE,UAAU,QAAQ;gBACpB,SAAS,eAAe,IAAI,CAAC;YACjC;QACJ;IACJ;AACJ;AACA,MAAM,gBAAgB,CAAC,gBAAgB;IACnC,MAAM,iBAAiB,eAAe,KAAK,CAAC;IAC5C,MAAM,UAAU,EAAE;IAClB,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;QAC5C,MAAM,gBAAgB,CAAA,GAAA,oLAAA,CAAA,eAAY,AAAD,EAAE,cAAc,CAAC,EAAE,EAAE;QACtD,QAAQ,IAAI,CAAC;QACb,IAAI,cAAc,IAAI,KAAK,SAAS;YAChC;QACJ;IACJ;IACA,OAAO;AACX;AACO,SAAS;IACZ,OAAO,IAAI,gBAAgB;QACvB,WAAU,MAAM,EAAE,UAAU;YACxB,CAAA,GAAA,oLAAA,CAAA,uBAAoB,AAAD,EAAE,QAAQ,CAAC;gBAC1B,MAAM,gBAAgB,cAAc,MAAM;gBAC1C,IAAI;gBACJ,sJAAsJ;gBACtJ,IAAI,gBAAgB,KAAK;oBACrB,SAAS,IAAI,WAAW;oBACxB,IAAI,SAAS,OAAO,MAAM,EAAE,QAAQ,CAAC,GAAG;gBAC5C,OACK,IAAI,gBAAgB,OAAO;oBAC5B,SAAS,IAAI,WAAW;oBACxB,MAAM,OAAO,IAAI,SAAS,OAAO,MAAM;oBACvC,KAAK,QAAQ,CAAC,GAAG;oBACjB,KAAK,SAAS,CAAC,GAAG;gBACtB,OACK;oBACD,SAAS,IAAI,WAAW;oBACxB,MAAM,OAAO,IAAI,SAAS,OAAO,MAAM;oBACvC,KAAK,QAAQ,CAAC,GAAG;oBACjB,KAAK,YAAY,CAAC,GAAG,OAAO;gBAChC;gBACA,0EAA0E;gBAC1E,IAAI,OAAO,IAAI,IAAI,OAAO,OAAO,IAAI,KAAK,UAAU;oBAChD,MAAM,CAAC,EAAE,IAAI;gBACjB;gBACA,WAAW,OAAO,CAAC;gBACnB,WAAW,OAAO,CAAC;YACvB;QACJ;IACJ;AACJ;AACA,IAAI;AACJ,SAAS,YAAY,MAAM;IACvB,OAAO,OAAO,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,MAAM,EAAE;AAC7D;AACA,SAAS,aAAa,MAAM,EAAE,IAAI;IAC9B,IAAI,MAAM,CAAC,EAAE,CAAC,MAAM,KAAK,MAAM;QAC3B,OAAO,OAAO,KAAK;IACvB;IACA,MAAM,SAAS,IAAI,WAAW;IAC9B,IAAI,IAAI;IACR,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;QAC3B,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC,IAAI;QAC1B,IAAI,MAAM,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE;YACxB,OAAO,KAAK;YACZ,IAAI;QACR;IACJ;IACA,IAAI,OAAO,MAAM,IAAI,IAAI,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE;QACvC,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC;IAChC;IACA,OAAO;AACX;AACO,SAAS,0BAA0B,UAAU,EAAE,UAAU;IAC5D,IAAI,CAAC,cAAc;QACf,eAAe,IAAI;IACvB;IACA,MAAM,SAAS,EAAE;IACjB,IAAI,QAAQ,EAAE,qBAAqB;IACnC,IAAI,iBAAiB,CAAC;IACtB,IAAI,WAAW;IACf,OAAO,IAAI,gBAAgB;QACvB,WAAU,KAAK,EAAE,UAAU;YACvB,OAAO,IAAI,CAAC;YACZ,MAAO,KAAM;gBACT,IAAI,UAAU,EAAE,qBAAqB,KAAI;oBACrC,IAAI,YAAY,UAAU,GAAG;wBACzB;oBACJ;oBACA,MAAM,SAAS,aAAa,QAAQ;oBACpC,WAAW,CAAC,MAAM,CAAC,EAAE,GAAG,IAAI,MAAM;oBAClC,iBAAiB,MAAM,CAAC,EAAE,GAAG;oBAC7B,IAAI,iBAAiB,KAAK;wBACtB,QAAQ,EAAE,sBAAsB;oBACpC,OACK,IAAI,mBAAmB,KAAK;wBAC7B,QAAQ,EAAE,iCAAiC;oBAC/C,OACK;wBACD,QAAQ,EAAE,iCAAiC;oBAC/C;gBACJ,OACK,IAAI,UAAU,EAAE,iCAAiC,KAAI;oBACtD,IAAI,YAAY,UAAU,GAAG;wBACzB;oBACJ;oBACA,MAAM,cAAc,aAAa,QAAQ;oBACzC,iBAAiB,IAAI,SAAS,YAAY,MAAM,EAAE,YAAY,UAAU,EAAE,YAAY,MAAM,EAAE,SAAS,CAAC;oBACxG,QAAQ,EAAE,sBAAsB;gBACpC,OACK,IAAI,UAAU,EAAE,iCAAiC,KAAI;oBACtD,IAAI,YAAY,UAAU,GAAG;wBACzB;oBACJ;oBACA,MAAM,cAAc,aAAa,QAAQ;oBACzC,MAAM,OAAO,IAAI,SAAS,YAAY,MAAM,EAAE,YAAY,UAAU,EAAE,YAAY,MAAM;oBACxF,MAAM,IAAI,KAAK,SAAS,CAAC;oBACzB,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,KAAK,MAAM,GAAG;wBAC9B,qDAAqD;wBACrD,WAAW,OAAO,CAAC,oKAAA,CAAA,eAAY;wBAC/B;oBACJ;oBACA,iBAAiB,IAAI,KAAK,GAAG,CAAC,GAAG,MAAM,KAAK,SAAS,CAAC;oBACtD,QAAQ,EAAE,sBAAsB;gBACpC,OACK;oBACD,IAAI,YAAY,UAAU,gBAAgB;wBACtC;oBACJ;oBACA,MAAM,OAAO,aAAa,QAAQ;oBAClC,WAAW,OAAO,CAAC,CAAA,GAAA,oLAAA,CAAA,eAAY,AAAD,EAAE,WAAW,OAAO,aAAa,MAAM,CAAC,OAAO;oBAC7E,QAAQ,EAAE,qBAAqB;gBACnC;gBACA,IAAI,mBAAmB,KAAK,iBAAiB,YAAY;oBACrD,WAAW,OAAO,CAAC,oKAAA,CAAA,eAAY;oBAC/B;gBACJ;YACJ;QACJ;IACJ;AACJ;AACO,MAAM,WAAW", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 405, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/%40socket.io/component-emitter/lib/esm/index.js"], "sourcesContent": ["/**\n * Initialize a new `Emitter`.\n *\n * @api public\n */\n\nexport function Emitter(obj) {\n  if (obj) return mixin(obj);\n}\n\n/**\n * Mixin the emitter properties.\n *\n * @param {Object} obj\n * @return {Object}\n * @api private\n */\n\nfunction mixin(obj) {\n  for (var key in Emitter.prototype) {\n    obj[key] = Emitter.prototype[key];\n  }\n  return obj;\n}\n\n/**\n * Listen on the given `event` with `fn`.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.on =\nEmitter.prototype.addEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\n    .push(fn);\n  return this;\n};\n\n/**\n * Adds an `event` listener that will be invoked a single\n * time then automatically removed.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.once = function(event, fn){\n  function on() {\n    this.off(event, on);\n    fn.apply(this, arguments);\n  }\n\n  on.fn = fn;\n  this.on(event, on);\n  return this;\n};\n\n/**\n * Remove the given callback for `event` or all\n * registered callbacks.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.off =\nEmitter.prototype.removeListener =\nEmitter.prototype.removeAllListeners =\nEmitter.prototype.removeEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n\n  // all\n  if (0 == arguments.length) {\n    this._callbacks = {};\n    return this;\n  }\n\n  // specific event\n  var callbacks = this._callbacks['$' + event];\n  if (!callbacks) return this;\n\n  // remove all handlers\n  if (1 == arguments.length) {\n    delete this._callbacks['$' + event];\n    return this;\n  }\n\n  // remove specific handler\n  var cb;\n  for (var i = 0; i < callbacks.length; i++) {\n    cb = callbacks[i];\n    if (cb === fn || cb.fn === fn) {\n      callbacks.splice(i, 1);\n      break;\n    }\n  }\n\n  // Remove event specific arrays for event types that no\n  // one is subscribed for to avoid memory leak.\n  if (callbacks.length === 0) {\n    delete this._callbacks['$' + event];\n  }\n\n  return this;\n};\n\n/**\n * Emit `event` with the given args.\n *\n * @param {String} event\n * @param {Mixed} ...\n * @return {Emitter}\n */\n\nEmitter.prototype.emit = function(event){\n  this._callbacks = this._callbacks || {};\n\n  var args = new Array(arguments.length - 1)\n    , callbacks = this._callbacks['$' + event];\n\n  for (var i = 1; i < arguments.length; i++) {\n    args[i - 1] = arguments[i];\n  }\n\n  if (callbacks) {\n    callbacks = callbacks.slice(0);\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\n      callbacks[i].apply(this, args);\n    }\n  }\n\n  return this;\n};\n\n// alias used for reserved events (protected method)\nEmitter.prototype.emitReserved = Emitter.prototype.emit;\n\n/**\n * Return array of callbacks for `event`.\n *\n * @param {String} event\n * @return {Array}\n * @api public\n */\n\nEmitter.prototype.listeners = function(event){\n  this._callbacks = this._callbacks || {};\n  return this._callbacks['$' + event] || [];\n};\n\n/**\n * Check if this emitter has `event` handlers.\n *\n * @param {String} event\n * @return {Boolean}\n * @api public\n */\n\nEmitter.prototype.hasListeners = function(event){\n  return !! this.listeners(event).length;\n};\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AAEM,SAAS,QAAQ,GAAG;IACzB,IAAI,KAAK,OAAO,MAAM;AACxB;AAEA;;;;;;CAMC,GAED,SAAS,MAAM,GAAG;IAChB,IAAK,IAAI,OAAO,QAAQ,SAAS,CAAE;QACjC,GAAG,CAAC,IAAI,GAAG,QAAQ,SAAS,CAAC,IAAI;IACnC;IACA,OAAO;AACT;AAEA;;;;;;;CAOC,GAED,QAAQ,SAAS,CAAC,EAAE,GACpB,QAAQ,SAAS,CAAC,gBAAgB,GAAG,SAAS,KAAK,EAAE,EAAE;IACrD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC;IACtC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,MAAM,IAAI,EAAE,EAC/D,IAAI,CAAC;IACR,OAAO,IAAI;AACb;AAEA;;;;;;;;CAQC,GAED,QAAQ,SAAS,CAAC,IAAI,GAAG,SAAS,KAAK,EAAE,EAAE;IACzC,SAAS;QACP,IAAI,CAAC,GAAG,CAAC,OAAO;QAChB,GAAG,KAAK,CAAC,IAAI,EAAE;IACjB;IAEA,GAAG,EAAE,GAAG;IACR,IAAI,CAAC,EAAE,CAAC,OAAO;IACf,OAAO,IAAI;AACb;AAEA;;;;;;;;CAQC,GAED,QAAQ,SAAS,CAAC,GAAG,GACrB,QAAQ,SAAS,CAAC,cAAc,GAChC,QAAQ,SAAS,CAAC,kBAAkB,GACpC,QAAQ,SAAS,CAAC,mBAAmB,GAAG,SAAS,KAAK,EAAE,EAAE;IACxD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC;IAEtC,MAAM;IACN,IAAI,KAAK,UAAU,MAAM,EAAE;QACzB,IAAI,CAAC,UAAU,GAAG,CAAC;QACnB,OAAO,IAAI;IACb;IAEA,iBAAiB;IACjB,IAAI,YAAY,IAAI,CAAC,UAAU,CAAC,MAAM,MAAM;IAC5C,IAAI,CAAC,WAAW,OAAO,IAAI;IAE3B,sBAAsB;IACtB,IAAI,KAAK,UAAU,MAAM,EAAE;QACzB,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,MAAM;QACnC,OAAO,IAAI;IACb;IAEA,0BAA0B;IAC1B,IAAI;IACJ,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QACzC,KAAK,SAAS,CAAC,EAAE;QACjB,IAAI,OAAO,MAAM,GAAG,EAAE,KAAK,IAAI;YAC7B,UAAU,MAAM,CAAC,GAAG;YACpB;QACF;IACF;IAEA,uDAAuD;IACvD,8CAA8C;IAC9C,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,MAAM;IACrC;IAEA,OAAO,IAAI;AACb;AAEA;;;;;;CAMC,GAED,QAAQ,SAAS,CAAC,IAAI,GAAG,SAAS,KAAK;IACrC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC;IAEtC,IAAI,OAAO,IAAI,MAAM,UAAU,MAAM,GAAG,IACpC,YAAY,IAAI,CAAC,UAAU,CAAC,MAAM,MAAM;IAE5C,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QACzC,IAAI,CAAC,IAAI,EAAE,GAAG,SAAS,CAAC,EAAE;IAC5B;IAEA,IAAI,WAAW;QACb,YAAY,UAAU,KAAK,CAAC;QAC5B,IAAK,IAAI,IAAI,GAAG,MAAM,UAAU,MAAM,EAAE,IAAI,KAAK,EAAE,EAAG;YACpD,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE;QAC3B;IACF;IAEA,OAAO,IAAI;AACb;AAEA,oDAAoD;AACpD,QAAQ,SAAS,CAAC,YAAY,GAAG,QAAQ,SAAS,CAAC,IAAI;AAEvD;;;;;;CAMC,GAED,QAAQ,SAAS,CAAC,SAAS,GAAG,SAAS,KAAK;IAC1C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC;IACtC,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,MAAM,IAAI,EAAE;AAC3C;AAEA;;;;;;CAMC,GAED,QAAQ,SAAS,CAAC,YAAY,GAAG,SAAS,KAAK;IAC7C,OAAO,CAAC,CAAE,IAAI,CAAC,SAAS,CAAC,OAAO,MAAM;AACxC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 542, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/engine.io-client/build/esm/globals.js"], "sourcesContent": ["export const nextTick = (() => {\n    const isPromiseAvailable = typeof Promise === \"function\" && typeof Promise.resolve === \"function\";\n    if (isPromiseAvailable) {\n        return (cb) => Promise.resolve().then(cb);\n    }\n    else {\n        return (cb, setTimeoutFn) => setTimeoutFn(cb, 0);\n    }\n})();\nexport const globalThisShim = (() => {\n    if (typeof self !== \"undefined\") {\n        return self;\n    }\n    else if (typeof window !== \"undefined\") {\n        return window;\n    }\n    else {\n        return Function(\"return this\")();\n    }\n})();\nexport const defaultBinaryType = \"arraybuffer\";\nexport function createCookieJar() { }\n"], "names": [], "mappings": ";;;;;;AAAO,MAAM,WAAW,CAAC;IACrB,MAAM,qBAAqB,OAAO,YAAY,cAAc,OAAO,QAAQ,OAAO,KAAK;IACvF,IAAI,oBAAoB;QACpB,OAAO,CAAC,KAAO,QAAQ,OAAO,GAAG,IAAI,CAAC;IAC1C,OACK;QACD,OAAO,CAAC,IAAI,eAAiB,aAAa,IAAI;IAClD;AACJ,CAAC;AACM,MAAM,iBAAiB,CAAC;IAC3B,IAAI,OAAO,SAAS,aAAa;QAC7B,OAAO;IACX,OACK,IAAI,OAAO,WAAW,aAAa;QACpC,OAAO;IACX,OACK;QACD,OAAO,SAAS;IACpB;AACJ,CAAC;AACM,MAAM,oBAAoB;AAC1B,SAAS,mBAAoB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 573, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/engine.io-client/build/esm/util.js"], "sourcesContent": ["import { globalThisShim as globalThis } from \"./globals.node.js\";\nexport function pick(obj, ...attr) {\n    return attr.reduce((acc, k) => {\n        if (obj.hasOwnProperty(k)) {\n            acc[k] = obj[k];\n        }\n        return acc;\n    }, {});\n}\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = globalThis.setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = globalThis.clearTimeout;\nexport function installTimerFunctions(obj, opts) {\n    if (opts.useNativeTimers) {\n        obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n        obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n    }\n    else {\n        obj.setTimeoutFn = globalThis.setTimeout.bind(globalThis);\n        obj.clearTimeoutFn = globalThis.clearTimeout.bind(globalThis);\n    }\n}\n// base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\nconst BASE64_OVERHEAD = 1.33;\n// we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\nexport function byteLength(obj) {\n    if (typeof obj === \"string\") {\n        return utf8Length(obj);\n    }\n    // arraybuffer or blob\n    return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\nfunction utf8Length(str) {\n    let c = 0, length = 0;\n    for (let i = 0, l = str.length; i < l; i++) {\n        c = str.charCodeAt(i);\n        if (c < 0x80) {\n            length += 1;\n        }\n        else if (c < 0x800) {\n            length += 2;\n        }\n        else if (c < 0xd800 || c >= 0xe000) {\n            length += 3;\n        }\n        else {\n            i++;\n            length += 4;\n        }\n    }\n    return length;\n}\n/**\n * Generates a random 8-characters string.\n */\nexport function randomString() {\n    return (Date.now().toString(36).substring(3) +\n        Math.random().toString(36).substring(2, 5));\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,SAAS,KAAK,GAAG,EAAE,GAAG,IAAI;IAC7B,OAAO,KAAK,MAAM,CAAC,CAAC,KAAK;QACrB,IAAI,IAAI,cAAc,CAAC,IAAI;YACvB,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE;QACnB;QACA,OAAO;IACX,GAAG,CAAC;AACR;AACA,qFAAqF;AACrF,MAAM,qBAAqB,oKAAA,CAAA,iBAAU,CAAC,UAAU;AAChD,MAAM,uBAAuB,oKAAA,CAAA,iBAAU,CAAC,YAAY;AAC7C,SAAS,sBAAsB,GAAG,EAAE,IAAI;IAC3C,IAAI,KAAK,eAAe,EAAE;QACtB,IAAI,YAAY,GAAG,mBAAmB,IAAI,CAAC,oKAAA,CAAA,iBAAU;QACrD,IAAI,cAAc,GAAG,qBAAqB,IAAI,CAAC,oKAAA,CAAA,iBAAU;IAC7D,OACK;QACD,IAAI,YAAY,GAAG,oKAAA,CAAA,iBAAU,CAAC,UAAU,CAAC,IAAI,CAAC,oKAAA,CAAA,iBAAU;QACxD,IAAI,cAAc,GAAG,oKAAA,CAAA,iBAAU,CAAC,YAAY,CAAC,IAAI,CAAC,oKAAA,CAAA,iBAAU;IAChE;AACJ;AACA,qFAAqF;AACrF,MAAM,kBAAkB;AAEjB,SAAS,WAAW,GAAG;IAC1B,IAAI,OAAO,QAAQ,UAAU;QACzB,OAAO,WAAW;IACtB;IACA,sBAAsB;IACtB,OAAO,KAAK,IAAI,CAAC,CAAC,IAAI,UAAU,IAAI,IAAI,IAAI,IAAI;AACpD;AACA,SAAS,WAAW,GAAG;IACnB,IAAI,IAAI,GAAG,SAAS;IACpB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAI,GAAG,IAAK;QACxC,IAAI,IAAI,UAAU,CAAC;QACnB,IAAI,IAAI,MAAM;YACV,UAAU;QACd,OACK,IAAI,IAAI,OAAO;YAChB,UAAU;QACd,OACK,IAAI,IAAI,UAAU,KAAK,QAAQ;YAChC,UAAU;QACd,OACK;YACD;YACA,UAAU;QACd;IACJ;IACA,OAAO;AACX;AAIO,SAAS;IACZ,OAAQ,KAAK,GAAG,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,KACtC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 636, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/engine.io-client/build/esm/contrib/parseqs.js"], "sourcesContent": ["// imported from https://github.com/galkn/querystring\n/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\nexport function encode(obj) {\n    let str = '';\n    for (let i in obj) {\n        if (obj.hasOwnProperty(i)) {\n            if (str.length)\n                str += '&';\n            str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n        }\n    }\n    return str;\n}\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\nexport function decode(qs) {\n    let qry = {};\n    let pairs = qs.split('&');\n    for (let i = 0, l = pairs.length; i < l; i++) {\n        let pair = pairs[i].split('=');\n        qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n    }\n    return qry;\n}\n"], "names": [], "mappings": "AAAA,qDAAqD;AACrD;;;;;;CAMC;;;;AACM,SAAS,OAAO,GAAG;IACtB,IAAI,MAAM;IACV,IAAK,IAAI,KAAK,IAAK;QACf,IAAI,IAAI,cAAc,CAAC,IAAI;YACvB,IAAI,IAAI,MAAM,EACV,OAAO;YACX,OAAO,mBAAmB,KAAK,MAAM,mBAAmB,GAAG,CAAC,EAAE;QAClE;IACJ;IACA,OAAO;AACX;AAOO,SAAS,OAAO,EAAE;IACrB,IAAI,MAAM,CAAC;IACX,IAAI,QAAQ,GAAG,KAAK,CAAC;IACrB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAI,GAAG,IAAK;QAC1C,IAAI,OAAO,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC;QAC1B,GAAG,CAAC,mBAAmB,IAAI,CAAC,EAAE,EAAE,GAAG,mBAAmB,IAAI,CAAC,EAAE;IACjE;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 672, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/engine.io-client/build/esm/transport.js"], "sourcesContent": ["import { decodePacket } from \"engine.io-parser\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions } from \"./util.js\";\nimport { encode } from \"./contrib/parseqs.js\";\nexport class TransportError extends Error {\n    constructor(reason, description, context) {\n        super(reason);\n        this.description = description;\n        this.context = context;\n        this.type = \"TransportError\";\n    }\n}\nexport class Transport extends Emitter {\n    /**\n     * Transport abstract constructor.\n     *\n     * @param {Object} opts - options\n     * @protected\n     */\n    constructor(opts) {\n        super();\n        this.writable = false;\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.query = opts.query;\n        this.socket = opts.socket;\n        this.supportsBinary = !opts.forceBase64;\n    }\n    /**\n     * Emits an error.\n     *\n     * @param {String} reason\n     * @param description\n     * @param context - the error context\n     * @return {Transport} for chaining\n     * @protected\n     */\n    onError(reason, description, context) {\n        super.emitReserved(\"error\", new TransportError(reason, description, context));\n        return this;\n    }\n    /**\n     * Opens the transport.\n     */\n    open() {\n        this.readyState = \"opening\";\n        this.doOpen();\n        return this;\n    }\n    /**\n     * Closes the transport.\n     */\n    close() {\n        if (this.readyState === \"opening\" || this.readyState === \"open\") {\n            this.doClose();\n            this.onClose();\n        }\n        return this;\n    }\n    /**\n     * Sends multiple packets.\n     *\n     * @param {Array} packets\n     */\n    send(packets) {\n        if (this.readyState === \"open\") {\n            this.write(packets);\n        }\n        else {\n            // this might happen if the transport was silently closed in the beforeunload event handler\n        }\n    }\n    /**\n     * Called upon open\n     *\n     * @protected\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        this.writable = true;\n        super.emitReserved(\"open\");\n    }\n    /**\n     * Called with data.\n     *\n     * @param {String} data\n     * @protected\n     */\n    onData(data) {\n        const packet = decodePacket(data, this.socket.binaryType);\n        this.onPacket(packet);\n    }\n    /**\n     * Called with a decoded packet.\n     *\n     * @protected\n     */\n    onPacket(packet) {\n        super.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon close.\n     *\n     * @protected\n     */\n    onClose(details) {\n        this.readyState = \"closed\";\n        super.emitReserved(\"close\", details);\n    }\n    /**\n     * Pauses the transport, in order not to lose packets during an upgrade.\n     *\n     * @param onPause\n     */\n    pause(onPause) { }\n    createUri(schema, query = {}) {\n        return (schema +\n            \"://\" +\n            this._hostname() +\n            this._port() +\n            this.opts.path +\n            this._query(query));\n    }\n    _hostname() {\n        const hostname = this.opts.hostname;\n        return hostname.indexOf(\":\") === -1 ? hostname : \"[\" + hostname + \"]\";\n    }\n    _port() {\n        if (this.opts.port &&\n            ((this.opts.secure && Number(this.opts.port !== 443)) ||\n                (!this.opts.secure && Number(this.opts.port) !== 80))) {\n            return \":\" + this.opts.port;\n        }\n        else {\n            return \"\";\n        }\n    }\n    _query(query) {\n        const encodedQuery = encode(query);\n        return encodedQuery.length ? \"?\" + encodedQuery : \"\";\n    }\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;AACA;;;;;AACO,MAAM,uBAAuB;IAChC,YAAY,MAAM,EAAE,WAAW,EAAE,OAAO,CAAE;QACtC,KAAK,CAAC;QACN,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,IAAI,GAAG;IAChB;AACJ;AACO,MAAM,kBAAkB,gLAAA,CAAA,UAAO;IAClC;;;;;KAKC,GACD,YAAY,IAAI,CAAE;QACd,KAAK;QACL,IAAI,CAAC,QAAQ,GAAG;QAChB,CAAA,GAAA,iKAAA,CAAA,wBAAqB,AAAD,EAAE,IAAI,EAAE;QAC5B,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG,KAAK,KAAK;QACvB,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM;QACzB,IAAI,CAAC,cAAc,GAAG,CAAC,KAAK,WAAW;IAC3C;IACA;;;;;;;;KAQC,GACD,QAAQ,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE;QAClC,KAAK,CAAC,aAAa,SAAS,IAAI,eAAe,QAAQ,aAAa;QACpE,OAAO,IAAI;IACf;IACA;;KAEC,GACD,OAAO;QACH,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,MAAM;QACX,OAAO,IAAI;IACf;IACA;;KAEC,GACD,QAAQ;QACJ,IAAI,IAAI,CAAC,UAAU,KAAK,aAAa,IAAI,CAAC,UAAU,KAAK,QAAQ;YAC7D,IAAI,CAAC,OAAO;YACZ,IAAI,CAAC,OAAO;QAChB;QACA,OAAO,IAAI;IACf;IACA;;;;KAIC,GACD,KAAK,OAAO,EAAE;QACV,IAAI,IAAI,CAAC,UAAU,KAAK,QAAQ;YAC5B,IAAI,CAAC,KAAK,CAAC;QACf,OACK;QACD,2FAA2F;QAC/F;IACJ;IACA;;;;KAIC,GACD,SAAS;QACL,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,QAAQ,GAAG;QAChB,KAAK,CAAC,aAAa;IACvB;IACA;;;;;KAKC,GACD,OAAO,IAAI,EAAE;QACT,MAAM,SAAS,CAAA,GAAA,oLAAA,CAAA,eAAY,AAAD,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU;QACxD,IAAI,CAAC,QAAQ,CAAC;IAClB;IACA;;;;KAIC,GACD,SAAS,MAAM,EAAE;QACb,KAAK,CAAC,aAAa,UAAU;IACjC;IACA;;;;KAIC,GACD,QAAQ,OAAO,EAAE;QACb,IAAI,CAAC,UAAU,GAAG;QAClB,KAAK,CAAC,aAAa,SAAS;IAChC;IACA;;;;KAIC,GACD,MAAM,OAAO,EAAE,CAAE;IACjB,UAAU,MAAM,EAAE,QAAQ,CAAC,CAAC,EAAE;QAC1B,OAAQ,SACJ,QACA,IAAI,CAAC,SAAS,KACd,IAAI,CAAC,KAAK,KACV,IAAI,CAAC,IAAI,CAAC,IAAI,GACd,IAAI,CAAC,MAAM,CAAC;IACpB;IACA,YAAY;QACR,MAAM,WAAW,IAAI,CAAC,IAAI,CAAC,QAAQ;QACnC,OAAO,SAAS,OAAO,CAAC,SAAS,CAAC,IAAI,WAAW,MAAM,WAAW;IACtE;IACA,QAAQ;QACJ,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,IACd,CAAC,AAAC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,QAC3C,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,MAAM,EAAG,GAAG;YAC3D,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI;QAC/B,OACK;YACD,OAAO;QACX;IACJ;IACA,OAAO,KAAK,EAAE;QACV,MAAM,eAAe,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE;QAC5B,OAAO,aAAa,MAAM,GAAG,MAAM,eAAe;IACtD;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 810, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/engine.io-client/build/esm/transports/polling.js"], "sourcesContent": ["import { Transport } from \"../transport.js\";\nimport { randomString } from \"../util.js\";\nimport { encodePayload, decodePayload } from \"engine.io-parser\";\nexport class Polling extends Transport {\n    constructor() {\n        super(...arguments);\n        this._polling = false;\n    }\n    get name() {\n        return \"polling\";\n    }\n    /**\n     * Opens the socket (triggers polling). We write a PING message to determine\n     * when the transport is open.\n     *\n     * @protected\n     */\n    doOpen() {\n        this._poll();\n    }\n    /**\n     * Pauses polling.\n     *\n     * @param {Function} onPause - callback upon buffers are flushed and transport is paused\n     * @package\n     */\n    pause(onPause) {\n        this.readyState = \"pausing\";\n        const pause = () => {\n            this.readyState = \"paused\";\n            onPause();\n        };\n        if (this._polling || !this.writable) {\n            let total = 0;\n            if (this._polling) {\n                total++;\n                this.once(\"pollComplete\", function () {\n                    --total || pause();\n                });\n            }\n            if (!this.writable) {\n                total++;\n                this.once(\"drain\", function () {\n                    --total || pause();\n                });\n            }\n        }\n        else {\n            pause();\n        }\n    }\n    /**\n     * Starts polling cycle.\n     *\n     * @private\n     */\n    _poll() {\n        this._polling = true;\n        this.doPoll();\n        this.emitReserved(\"poll\");\n    }\n    /**\n     * Overloads onData to detect payloads.\n     *\n     * @protected\n     */\n    onData(data) {\n        const callback = (packet) => {\n            // if its the first message we consider the transport open\n            if (\"opening\" === this.readyState && packet.type === \"open\") {\n                this.onOpen();\n            }\n            // if its a close packet, we close the ongoing requests\n            if (\"close\" === packet.type) {\n                this.onClose({ description: \"transport closed by the server\" });\n                return false;\n            }\n            // otherwise bypass onData and handle the message\n            this.onPacket(packet);\n        };\n        // decode payload\n        decodePayload(data, this.socket.binaryType).forEach(callback);\n        // if an event did not trigger closing\n        if (\"closed\" !== this.readyState) {\n            // if we got data we're not polling\n            this._polling = false;\n            this.emitReserved(\"pollComplete\");\n            if (\"open\" === this.readyState) {\n                this._poll();\n            }\n            else {\n            }\n        }\n    }\n    /**\n     * For polling, send a close packet.\n     *\n     * @protected\n     */\n    doClose() {\n        const close = () => {\n            this.write([{ type: \"close\" }]);\n        };\n        if (\"open\" === this.readyState) {\n            close();\n        }\n        else {\n            // in case we're trying to close while\n            // handshaking is in progress (GH-164)\n            this.once(\"open\", close);\n        }\n    }\n    /**\n     * Writes a packets payload.\n     *\n     * @param {Array} packets - data packets\n     * @protected\n     */\n    write(packets) {\n        this.writable = false;\n        encodePayload(packets, (data) => {\n            this.doWrite(data, () => {\n                this.writable = true;\n                this.emitReserved(\"drain\");\n            });\n        });\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"https\" : \"http\";\n        const query = this.query || {};\n        // cache busting is forced\n        if (false !== this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = randomString();\n        }\n        if (!this.supportsBinary && !query.sid) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AAAA;;;;AACO,MAAM,gBAAgB,sKAAA,CAAA,YAAS;IAClC,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA,IAAI,OAAO;QACP,OAAO;IACX;IACA;;;;;KAKC,GACD,SAAS;QACL,IAAI,CAAC,KAAK;IACd;IACA;;;;;KAKC,GACD,MAAM,OAAO,EAAE;QACX,IAAI,CAAC,UAAU,GAAG;QAClB,MAAM,QAAQ;YACV,IAAI,CAAC,UAAU,GAAG;YAClB;QACJ;QACA,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YACjC,IAAI,QAAQ;YACZ,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACf;gBACA,IAAI,CAAC,IAAI,CAAC,gBAAgB;oBACtB,EAAE,SAAS;gBACf;YACJ;YACA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAChB;gBACA,IAAI,CAAC,IAAI,CAAC,SAAS;oBACf,EAAE,SAAS;gBACf;YACJ;QACJ,OACK;YACD;QACJ;IACJ;IACA;;;;KAIC,GACD,QAAQ;QACJ,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,MAAM;QACX,IAAI,CAAC,YAAY,CAAC;IACtB;IACA;;;;KAIC,GACD,OAAO,IAAI,EAAE;QACT,MAAM,WAAW,CAAC;YACd,0DAA0D;YAC1D,IAAI,cAAc,IAAI,CAAC,UAAU,IAAI,OAAO,IAAI,KAAK,QAAQ;gBACzD,IAAI,CAAC,MAAM;YACf;YACA,uDAAuD;YACvD,IAAI,YAAY,OAAO,IAAI,EAAE;gBACzB,IAAI,CAAC,OAAO,CAAC;oBAAE,aAAa;gBAAiC;gBAC7D,OAAO;YACX;YACA,iDAAiD;YACjD,IAAI,CAAC,QAAQ,CAAC;QAClB;QACA,iBAAiB;QACjB,CAAA,GAAA,kLAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,OAAO,CAAC;QACpD,sCAAsC;QACtC,IAAI,aAAa,IAAI,CAAC,UAAU,EAAE;YAC9B,mCAAmC;YACnC,IAAI,CAAC,QAAQ,GAAG;YAChB,IAAI,CAAC,YAAY,CAAC;YAClB,IAAI,WAAW,IAAI,CAAC,UAAU,EAAE;gBAC5B,IAAI,CAAC,KAAK;YACd,OACK,CACL;QACJ;IACJ;IACA;;;;KAIC,GACD,UAAU;QACN,MAAM,QAAQ;YACV,IAAI,CAAC,KAAK,CAAC;gBAAC;oBAAE,MAAM;gBAAQ;aAAE;QAClC;QACA,IAAI,WAAW,IAAI,CAAC,UAAU,EAAE;YAC5B;QACJ,OACK;YACD,sCAAsC;YACtC,sCAAsC;YACtC,IAAI,CAAC,IAAI,CAAC,QAAQ;QACtB;IACJ;IACA;;;;;KAKC,GACD,MAAM,OAAO,EAAE;QACX,IAAI,CAAC,QAAQ,GAAG;QAChB,CAAA,GAAA,kLAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,CAAC;YACpB,IAAI,CAAC,OAAO,CAAC,MAAM;gBACf,IAAI,CAAC,QAAQ,GAAG;gBAChB,IAAI,CAAC,YAAY,CAAC;YACtB;QACJ;IACJ;IACA;;;;KAIC,GACD,MAAM;QACF,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,UAAU;QAC5C,MAAM,QAAQ,IAAI,CAAC,KAAK,IAAI,CAAC;QAC7B,0BAA0B;QAC1B,IAAI,UAAU,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACvC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD;QACjD;QACA,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,MAAM,GAAG,EAAE;YACpC,MAAM,GAAG,GAAG;QAChB;QACA,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ;IAClC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 963, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/engine.io-client/build/esm/contrib/has-cors.js"], "sourcesContent": ["// imported from https://github.com/component/has-cors\nlet value = false;\ntry {\n    value = typeof XMLHttpRequest !== 'undefined' &&\n        'withCredentials' in new XMLHttpRequest();\n}\ncatch (err) {\n    // if XMLHttp support is disabled in IE then it will throw\n    // when trying to create\n}\nexport const hasCORS = value;\n"], "names": [], "mappings": "AAAA,sDAAsD;;;;AACtD,IAAI,QAAQ;AACZ,IAAI;IACA,QAAQ,OAAO,mBAAmB,eAC9B,qBAAqB,IAAI;AACjC,EACA,OAAO,KAAK;AACR,0DAA0D;AAC1D,wBAAwB;AAC5B;AACO,MAAM,UAAU", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 981, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/engine.io-client/build/esm/transports/polling-xhr.js"], "sourcesContent": ["import { Polling } from \"./polling.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions, pick } from \"../util.js\";\nimport { globalThisShim as globalThis } from \"../globals.node.js\";\nimport { hasCORS } from \"../contrib/has-cors.js\";\nfunction empty() { }\nexport class BaseXHR extends Polling {\n    /**\n     * XHR Polling constructor.\n     *\n     * @param {Object} opts\n     * @package\n     */\n    constructor(opts) {\n        super(opts);\n        if (typeof location !== \"undefined\") {\n            const isSSL = \"https:\" === location.protocol;\n            let port = location.port;\n            // some user agents have empty `location.port`\n            if (!port) {\n                port = isSSL ? \"443\" : \"80\";\n            }\n            this.xd =\n                (typeof location !== \"undefined\" &&\n                    opts.hostname !== location.hostname) ||\n                    port !== opts.port;\n        }\n    }\n    /**\n     * Sends data.\n     *\n     * @param {String} data to send.\n     * @param {Function} called upon flush.\n     * @private\n     */\n    doWrite(data, fn) {\n        const req = this.request({\n            method: \"POST\",\n            data: data,\n        });\n        req.on(\"success\", fn);\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr post error\", xhrStatus, context);\n        });\n    }\n    /**\n     * Starts a poll cycle.\n     *\n     * @private\n     */\n    doPoll() {\n        const req = this.request();\n        req.on(\"data\", this.onData.bind(this));\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr poll error\", xhrStatus, context);\n        });\n        this.pollXhr = req;\n    }\n}\nexport class Request extends Emitter {\n    /**\n     * Request constructor\n     *\n     * @param {Object} options\n     * @package\n     */\n    constructor(createRequest, uri, opts) {\n        super();\n        this.createRequest = createRequest;\n        installTimerFunctions(this, opts);\n        this._opts = opts;\n        this._method = opts.method || \"GET\";\n        this._uri = uri;\n        this._data = undefined !== opts.data ? opts.data : null;\n        this._create();\n    }\n    /**\n     * Creates the XHR object and sends the request.\n     *\n     * @private\n     */\n    _create() {\n        var _a;\n        const opts = pick(this._opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n        opts.xdomain = !!this._opts.xd;\n        const xhr = (this._xhr = this.createRequest(opts));\n        try {\n            xhr.open(this._method, this._uri, true);\n            try {\n                if (this._opts.extraHeaders) {\n                    // @ts-ignore\n                    xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n                    for (let i in this._opts.extraHeaders) {\n                        if (this._opts.extraHeaders.hasOwnProperty(i)) {\n                            xhr.setRequestHeader(i, this._opts.extraHeaders[i]);\n                        }\n                    }\n                }\n            }\n            catch (e) { }\n            if (\"POST\" === this._method) {\n                try {\n                    xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n                }\n                catch (e) { }\n            }\n            try {\n                xhr.setRequestHeader(\"Accept\", \"*/*\");\n            }\n            catch (e) { }\n            (_a = this._opts.cookieJar) === null || _a === void 0 ? void 0 : _a.addCookies(xhr);\n            // ie6 check\n            if (\"withCredentials\" in xhr) {\n                xhr.withCredentials = this._opts.withCredentials;\n            }\n            if (this._opts.requestTimeout) {\n                xhr.timeout = this._opts.requestTimeout;\n            }\n            xhr.onreadystatechange = () => {\n                var _a;\n                if (xhr.readyState === 3) {\n                    (_a = this._opts.cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies(\n                    // @ts-ignore\n                    xhr.getResponseHeader(\"set-cookie\"));\n                }\n                if (4 !== xhr.readyState)\n                    return;\n                if (200 === xhr.status || 1223 === xhr.status) {\n                    this._onLoad();\n                }\n                else {\n                    // make sure the `error` event handler that's user-set\n                    // does not throw in the same tick and gets caught here\n                    this.setTimeoutFn(() => {\n                        this._onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n                    }, 0);\n                }\n            };\n            xhr.send(this._data);\n        }\n        catch (e) {\n            // Need to defer since .create() is called directly from the constructor\n            // and thus the 'error' event can only be only bound *after* this exception\n            // occurs.  Therefore, also, we cannot throw here at all.\n            this.setTimeoutFn(() => {\n                this._onError(e);\n            }, 0);\n            return;\n        }\n        if (typeof document !== \"undefined\") {\n            this._index = Request.requestsCount++;\n            Request.requests[this._index] = this;\n        }\n    }\n    /**\n     * Called upon error.\n     *\n     * @private\n     */\n    _onError(err) {\n        this.emitReserved(\"error\", err, this._xhr);\n        this._cleanup(true);\n    }\n    /**\n     * Cleans up house.\n     *\n     * @private\n     */\n    _cleanup(fromError) {\n        if (\"undefined\" === typeof this._xhr || null === this._xhr) {\n            return;\n        }\n        this._xhr.onreadystatechange = empty;\n        if (fromError) {\n            try {\n                this._xhr.abort();\n            }\n            catch (e) { }\n        }\n        if (typeof document !== \"undefined\") {\n            delete Request.requests[this._index];\n        }\n        this._xhr = null;\n    }\n    /**\n     * Called upon load.\n     *\n     * @private\n     */\n    _onLoad() {\n        const data = this._xhr.responseText;\n        if (data !== null) {\n            this.emitReserved(\"data\", data);\n            this.emitReserved(\"success\");\n            this._cleanup();\n        }\n    }\n    /**\n     * Aborts the request.\n     *\n     * @package\n     */\n    abort() {\n        this._cleanup();\n    }\n}\nRequest.requestsCount = 0;\nRequest.requests = {};\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\nif (typeof document !== \"undefined\") {\n    // @ts-ignore\n    if (typeof attachEvent === \"function\") {\n        // @ts-ignore\n        attachEvent(\"onunload\", unloadHandler);\n    }\n    else if (typeof addEventListener === \"function\") {\n        const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n        addEventListener(terminationEvent, unloadHandler, false);\n    }\n}\nfunction unloadHandler() {\n    for (let i in Request.requests) {\n        if (Request.requests.hasOwnProperty(i)) {\n            Request.requests[i].abort();\n        }\n    }\n}\nconst hasXHR2 = (function () {\n    const xhr = newRequest({\n        xdomain: false,\n    });\n    return xhr && xhr.responseType !== null;\n})();\n/**\n * HTTP long-polling based on the built-in `XMLHttpRequest` object.\n *\n * Usage: browser\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest\n */\nexport class XHR extends BaseXHR {\n    constructor(opts) {\n        super(opts);\n        const forceBase64 = opts && opts.forceBase64;\n        this.supportsBinary = hasXHR2 && !forceBase64;\n    }\n    request(opts = {}) {\n        Object.assign(opts, { xd: this.xd }, this.opts);\n        return new Request(newRequest, this.uri(), opts);\n    }\n}\nfunction newRequest(opts) {\n    const xdomain = opts.xdomain;\n    // XMLHttpRequest can be disabled on IE\n    try {\n        if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n            return new XMLHttpRequest();\n        }\n    }\n    catch (e) { }\n    if (!xdomain) {\n        try {\n            return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n        }\n        catch (e) { }\n    }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AACA,SAAS,SAAU;AACZ,MAAM,gBAAgB,kLAAA,CAAA,UAAO;IAChC;;;;;KAKC,GACD,YAAY,IAAI,CAAE;QACd,KAAK,CAAC;QACN,IAAI,OAAO,aAAa,aAAa;YACjC,MAAM,QAAQ,aAAa,SAAS,QAAQ;YAC5C,IAAI,OAAO,SAAS,IAAI;YACxB,8CAA8C;YAC9C,IAAI,CAAC,MAAM;gBACP,OAAO,QAAQ,QAAQ;YAC3B;YACA,IAAI,CAAC,EAAE,GACH,AAAC,OAAO,aAAa,eACjB,KAAK,QAAQ,KAAK,SAAS,QAAQ,IACnC,SAAS,KAAK,IAAI;QAC9B;IACJ;IACA;;;;;;KAMC,GACD,QAAQ,IAAI,EAAE,EAAE,EAAE;QACd,MAAM,MAAM,IAAI,CAAC,OAAO,CAAC;YACrB,QAAQ;YACR,MAAM;QACV;QACA,IAAI,EAAE,CAAC,WAAW;QAClB,IAAI,EAAE,CAAC,SAAS,CAAC,WAAW;YACxB,IAAI,CAAC,OAAO,CAAC,kBAAkB,WAAW;QAC9C;IACJ;IACA;;;;KAIC,GACD,SAAS;QACL,MAAM,MAAM,IAAI,CAAC,OAAO;QACxB,IAAI,EAAE,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI;QACpC,IAAI,EAAE,CAAC,SAAS,CAAC,WAAW;YACxB,IAAI,CAAC,OAAO,CAAC,kBAAkB,WAAW;QAC9C;QACA,IAAI,CAAC,OAAO,GAAG;IACnB;AACJ;AACO,MAAM,gBAAgB,gLAAA,CAAA,UAAO;IAChC;;;;;KAKC,GACD,YAAY,aAAa,EAAE,GAAG,EAAE,IAAI,CAAE;QAClC,KAAK;QACL,IAAI,CAAC,aAAa,GAAG;QACrB,CAAA,GAAA,iKAAA,CAAA,wBAAqB,AAAD,EAAE,IAAI,EAAE;QAC5B,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,OAAO,GAAG,KAAK,MAAM,IAAI;QAC9B,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG,cAAc,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG;QACnD,IAAI,CAAC,OAAO;IAChB;IACA;;;;KAIC,GACD,UAAU;QACN,IAAI;QACJ,MAAM,OAAO,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,KAAK,EAAE,SAAS,OAAO,OAAO,cAAc,QAAQ,MAAM,WAAW,sBAAsB;QAClH,KAAK,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;QAC9B,MAAM,MAAO,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC;QAC5C,IAAI;YACA,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,EAAE;YAClC,IAAI;gBACA,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE;oBACzB,aAAa;oBACb,IAAI,qBAAqB,IAAI,IAAI,qBAAqB,CAAC;oBACvD,IAAK,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,YAAY,CAAE;wBACnC,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI;4BAC3C,IAAI,gBAAgB,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;wBACtD;oBACJ;gBACJ;YACJ,EACA,OAAO,GAAG,CAAE;YACZ,IAAI,WAAW,IAAI,CAAC,OAAO,EAAE;gBACzB,IAAI;oBACA,IAAI,gBAAgB,CAAC,gBAAgB;gBACzC,EACA,OAAO,GAAG,CAAE;YAChB;YACA,IAAI;gBACA,IAAI,gBAAgB,CAAC,UAAU;YACnC,EACA,OAAO,GAAG,CAAE;YACZ,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,SAAS,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,UAAU,CAAC;YAC/E,YAAY;YACZ,IAAI,qBAAqB,KAAK;gBAC1B,IAAI,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe;YACpD;YACA,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE;gBAC3B,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc;YAC3C;YACA,IAAI,kBAAkB,GAAG;gBACrB,IAAI;gBACJ,IAAI,IAAI,UAAU,KAAK,GAAG;oBACtB,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,SAAS,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,YAAY,CAChF,aAAa;oBACb,IAAI,iBAAiB,CAAC;gBAC1B;gBACA,IAAI,MAAM,IAAI,UAAU,EACpB;gBACJ,IAAI,QAAQ,IAAI,MAAM,IAAI,SAAS,IAAI,MAAM,EAAE;oBAC3C,IAAI,CAAC,OAAO;gBAChB,OACK;oBACD,sDAAsD;oBACtD,uDAAuD;oBACvD,IAAI,CAAC,YAAY,CAAC;wBACd,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,MAAM,KAAK,WAAW,IAAI,MAAM,GAAG;oBAChE,GAAG;gBACP;YACJ;YACA,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK;QACvB,EACA,OAAO,GAAG;YACN,wEAAwE;YACxE,2EAA2E;YAC3E,yDAAyD;YACzD,IAAI,CAAC,YAAY,CAAC;gBACd,IAAI,CAAC,QAAQ,CAAC;YAClB,GAAG;YACH;QACJ;QACA,IAAI,OAAO,aAAa,aAAa;YACjC,IAAI,CAAC,MAAM,GAAG,QAAQ,aAAa;YACnC,QAAQ,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI;QACxC;IACJ;IACA;;;;KAIC,GACD,SAAS,GAAG,EAAE;QACV,IAAI,CAAC,YAAY,CAAC,SAAS,KAAK,IAAI,CAAC,IAAI;QACzC,IAAI,CAAC,QAAQ,CAAC;IAClB;IACA;;;;KAIC,GACD,SAAS,SAAS,EAAE;QAChB,IAAI,gBAAgB,OAAO,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;YACxD;QACJ;QACA,IAAI,CAAC,IAAI,CAAC,kBAAkB,GAAG;QAC/B,IAAI,WAAW;YACX,IAAI;gBACA,IAAI,CAAC,IAAI,CAAC,KAAK;YACnB,EACA,OAAO,GAAG,CAAE;QAChB;QACA,IAAI,OAAO,aAAa,aAAa;YACjC,OAAO,QAAQ,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;QACxC;QACA,IAAI,CAAC,IAAI,GAAG;IAChB;IACA;;;;KAIC,GACD,UAAU;QACN,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY;QACnC,IAAI,SAAS,MAAM;YACf,IAAI,CAAC,YAAY,CAAC,QAAQ;YAC1B,IAAI,CAAC,YAAY,CAAC;YAClB,IAAI,CAAC,QAAQ;QACjB;IACJ;IACA;;;;KAIC,GACD,QAAQ;QACJ,IAAI,CAAC,QAAQ;IACjB;AACJ;AACA,QAAQ,aAAa,GAAG;AACxB,QAAQ,QAAQ,GAAG,CAAC;AACpB;;;;CAIC,GACD,IAAI,OAAO,aAAa,aAAa;IACjC,aAAa;IACb,IAAI,OAAO,gBAAgB,YAAY;QACnC,aAAa;QACb,YAAY,YAAY;IAC5B,OACK,IAAI,OAAO,qBAAqB,YAAY;QAC7C,MAAM,mBAAmB,gBAAgB,oKAAA,CAAA,iBAAU,GAAG,aAAa;QACnE,iBAAiB,kBAAkB,eAAe;IACtD;AACJ;AACA,SAAS;IACL,IAAK,IAAI,KAAK,QAAQ,QAAQ,CAAE;QAC5B,IAAI,QAAQ,QAAQ,CAAC,cAAc,CAAC,IAAI;YACpC,QAAQ,QAAQ,CAAC,EAAE,CAAC,KAAK;QAC7B;IACJ;AACJ;AACA,MAAM,UAAU,AAAC;IACb,MAAM,MAAM,WAAW;QACnB,SAAS;IACb;IACA,OAAO,OAAO,IAAI,YAAY,KAAK;AACvC;AAQO,MAAM,YAAY;IACrB,YAAY,IAAI,CAAE;QACd,KAAK,CAAC;QACN,MAAM,cAAc,QAAQ,KAAK,WAAW;QAC5C,IAAI,CAAC,cAAc,GAAG,WAAW,CAAC;IACtC;IACA,QAAQ,OAAO,CAAC,CAAC,EAAE;QACf,OAAO,MAAM,CAAC,MAAM;YAAE,IAAI,IAAI,CAAC,EAAE;QAAC,GAAG,IAAI,CAAC,IAAI;QAC9C,OAAO,IAAI,QAAQ,YAAY,IAAI,CAAC,GAAG,IAAI;IAC/C;AACJ;AACA,SAAS,WAAW,IAAI;IACpB,MAAM,UAAU,KAAK,OAAO;IAC5B,uCAAuC;IACvC,IAAI;QACA,IAAI,gBAAgB,OAAO,kBAAkB,CAAC,CAAC,WAAW,mLAAA,CAAA,UAAO,GAAG;YAChE,OAAO,IAAI;QACf;IACJ,EACA,OAAO,GAAG,CAAE;IACZ,IAAI,CAAC,SAAS;QACV,IAAI;YACA,OAAO,IAAI,oKAAA,CAAA,iBAAU,CAAC;gBAAC;aAAS,CAAC,MAAM,CAAC,UAAU,IAAI,CAAC,KAAK,CAAC;QACjE,EACA,OAAO,GAAG,CAAE;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1241, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/engine.io-client/build/esm/transports/websocket.js"], "sourcesContent": ["import { Transport } from \"../transport.js\";\nimport { pick, randomString } from \"../util.js\";\nimport { encodePacket } from \"engine.io-parser\";\nimport { globalThisShim as globalThis, nextTick } from \"../globals.node.js\";\n// detect ReactNative environment\nconst isReactNative = typeof navigator !== \"undefined\" &&\n    typeof navigator.product === \"string\" &&\n    navigator.product.toLowerCase() === \"reactnative\";\nexport class BaseWS extends Transport {\n    get name() {\n        return \"websocket\";\n    }\n    doOpen() {\n        const uri = this.uri();\n        const protocols = this.opts.protocols;\n        // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n        const opts = isReactNative\n            ? {}\n            : pick(this.opts, \"agent\", \"perMessageDeflate\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"localAddress\", \"protocolVersion\", \"origin\", \"maxPayload\", \"family\", \"checkServerIdentity\");\n        if (this.opts.extraHeaders) {\n            opts.headers = this.opts.extraHeaders;\n        }\n        try {\n            this.ws = this.createSocket(uri, protocols, opts);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this.ws.binaryType = this.socket.binaryType;\n        this.addEventListeners();\n    }\n    /**\n     * Adds event listeners to the socket\n     *\n     * @private\n     */\n    addEventListeners() {\n        this.ws.onopen = () => {\n            if (this.opts.autoUnref) {\n                this.ws._socket.unref();\n            }\n            this.onOpen();\n        };\n        this.ws.onclose = (closeEvent) => this.onClose({\n            description: \"websocket connection closed\",\n            context: closeEvent,\n        });\n        this.ws.onmessage = (ev) => this.onData(ev.data);\n        this.ws.onerror = (e) => this.onError(\"websocket error\", e);\n    }\n    write(packets) {\n        this.writable = false;\n        // encodePacket efficient as it uses WS framing\n        // no need for encodePayload\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            encodePacket(packet, this.supportsBinary, (data) => {\n                // Sometimes the websocket has already been closed but the browser didn't\n                // have a chance of informing us about it yet, in that case send will\n                // throw an error\n                try {\n                    this.doWrite(packet, data);\n                }\n                catch (e) {\n                }\n                if (lastPacket) {\n                    // fake drain\n                    // defer to next tick to allow Socket to clear writeBuffer\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        if (typeof this.ws !== \"undefined\") {\n            this.ws.onerror = () => { };\n            this.ws.close();\n            this.ws = null;\n        }\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"wss\" : \"ws\";\n        const query = this.query || {};\n        // append timestamp to URI\n        if (this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = randomString();\n        }\n        // communicate binary support capabilities\n        if (!this.supportsBinary) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n}\nconst WebSocketCtor = globalThis.WebSocket || globalThis.MozWebSocket;\n/**\n * WebSocket transport based on the built-in `WebSocket` object.\n *\n * Usage: browser, Node.js (since v21), Deno, Bun\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket\n * @see https://caniuse.com/mdn-api_websocket\n * @see https://nodejs.org/api/globals.html#websocket\n */\nexport class WS extends BaseWS {\n    createSocket(uri, protocols, opts) {\n        return !isReactNative\n            ? protocols\n                ? new WebSocketCtor(uri, protocols)\n                : new WebSocketCtor(uri)\n            : new WebSocketCtor(uri, protocols, opts);\n    }\n    doWrite(_packet, data) {\n        this.ws.send(data);\n    }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AACA;;;;;AACA,iCAAiC;AACjC,MAAM,gBAAgB,OAAO,cAAc,eACvC,OAAO,UAAU,OAAO,KAAK,YAC7B,UAAU,OAAO,CAAC,WAAW,OAAO;AACjC,MAAM,eAAe,sKAAA,CAAA,YAAS;IACjC,IAAI,OAAO;QACP,OAAO;IACX;IACA,SAAS;QACL,MAAM,MAAM,IAAI,CAAC,GAAG;QACpB,MAAM,YAAY,IAAI,CAAC,IAAI,CAAC,SAAS;QACrC,uGAAuG;QACvG,MAAM,OAAO,gBACP,CAAC,IACD,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,IAAI,EAAE,SAAS,qBAAqB,OAAO,OAAO,cAAc,QAAQ,MAAM,WAAW,sBAAsB,gBAAgB,mBAAmB,UAAU,cAAc,UAAU;QACpM,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACxB,KAAK,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY;QACzC;QACA,IAAI;YACA,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,WAAW;QAChD,EACA,OAAO,KAAK;YACR,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS;QACtC;QACA,IAAI,CAAC,EAAE,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU;QAC3C,IAAI,CAAC,iBAAiB;IAC1B;IACA;;;;KAIC,GACD,oBAAoB;QAChB,IAAI,CAAC,EAAE,CAAC,MAAM,GAAG;YACb,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACrB,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK;YACzB;YACA,IAAI,CAAC,MAAM;QACf;QACA,IAAI,CAAC,EAAE,CAAC,OAAO,GAAG,CAAC,aAAe,IAAI,CAAC,OAAO,CAAC;gBAC3C,aAAa;gBACb,SAAS;YACb;QACA,IAAI,CAAC,EAAE,CAAC,SAAS,GAAG,CAAC,KAAO,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI;QAC/C,IAAI,CAAC,EAAE,CAAC,OAAO,GAAG,CAAC,IAAM,IAAI,CAAC,OAAO,CAAC,mBAAmB;IAC7D;IACA,MAAM,OAAO,EAAE;QACX,IAAI,CAAC,QAAQ,GAAG;QAChB,+CAA+C;QAC/C,4BAA4B;QAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACrC,MAAM,SAAS,OAAO,CAAC,EAAE;YACzB,MAAM,aAAa,MAAM,QAAQ,MAAM,GAAG;YAC1C,CAAA,GAAA,oLAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,IAAI,CAAC,cAAc,EAAE,CAAC;gBACvC,yEAAyE;gBACzE,qEAAqE;gBACrE,iBAAiB;gBACjB,IAAI;oBACA,IAAI,CAAC,OAAO,CAAC,QAAQ;gBACzB,EACA,OAAO,GAAG,CACV;gBACA,IAAI,YAAY;oBACZ,aAAa;oBACb,0DAA0D;oBAC1D,CAAA,GAAA,oKAAA,CAAA,WAAQ,AAAD,EAAE;wBACL,IAAI,CAAC,QAAQ,GAAG;wBAChB,IAAI,CAAC,YAAY,CAAC;oBACtB,GAAG,IAAI,CAAC,YAAY;gBACxB;YACJ;QACJ;IACJ;IACA,UAAU;QACN,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,aAAa;YAChC,IAAI,CAAC,EAAE,CAAC,OAAO,GAAG,KAAQ;YAC1B,IAAI,CAAC,EAAE,CAAC,KAAK;YACb,IAAI,CAAC,EAAE,GAAG;QACd;IACJ;IACA;;;;KAIC,GACD,MAAM;QACF,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,QAAQ;QAC1C,MAAM,QAAQ,IAAI,CAAC,KAAK,IAAI,CAAC;QAC7B,0BAA0B;QAC1B,IAAI,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC7B,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD;QACjD;QACA,0CAA0C;QAC1C,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,MAAM,GAAG,GAAG;QAChB;QACA,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ;IAClC;AACJ;AACA,MAAM,gBAAgB,oKAAA,CAAA,iBAAU,CAAC,SAAS,IAAI,oKAAA,CAAA,iBAAU,CAAC,YAAY;AAU9D,MAAM,WAAW;IACpB,aAAa,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE;QAC/B,OAAO,CAAC,gBACF,YACI,IAAI,cAAc,KAAK,aACvB,IAAI,cAAc,OACtB,IAAI,cAAc,KAAK,WAAW;IAC5C;IACA,QAAQ,OAAO,EAAE,IAAI,EAAE;QACnB,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;IACjB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1359, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/engine.io-client/build/esm/transports/webtransport.js"], "sourcesContent": ["import { Transport } from \"../transport.js\";\nimport { nextTick } from \"../globals.node.js\";\nimport { createPacketDecoderStream, createPacketEncoderStream, } from \"engine.io-parser\";\n/**\n * WebTransport transport based on the built-in `WebTransport` object.\n *\n * Usage: browser, Node.js (with the `@fails-components/webtransport` package)\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/WebTransport\n * @see https://caniuse.com/webtransport\n */\nexport class WT extends Transport {\n    get name() {\n        return \"webtransport\";\n    }\n    doOpen() {\n        try {\n            // @ts-ignore\n            this._transport = new WebTransport(this.createUri(\"https\"), this.opts.transportOptions[this.name]);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this._transport.closed\n            .then(() => {\n            this.onClose();\n        })\n            .catch((err) => {\n            this.onError(\"webtransport error\", err);\n        });\n        // note: we could have used async/await, but that would require some additional polyfills\n        this._transport.ready.then(() => {\n            this._transport.createBidirectionalStream().then((stream) => {\n                const decoderStream = createPacketDecoderStream(Number.MAX_SAFE_INTEGER, this.socket.binaryType);\n                const reader = stream.readable.pipeThrough(decoderStream).getReader();\n                const encoderStream = createPacketEncoderStream();\n                encoderStream.readable.pipeTo(stream.writable);\n                this._writer = encoderStream.writable.getWriter();\n                const read = () => {\n                    reader\n                        .read()\n                        .then(({ done, value }) => {\n                        if (done) {\n                            return;\n                        }\n                        this.onPacket(value);\n                        read();\n                    })\n                        .catch((err) => {\n                    });\n                };\n                read();\n                const packet = { type: \"open\" };\n                if (this.query.sid) {\n                    packet.data = `{\"sid\":\"${this.query.sid}\"}`;\n                }\n                this._writer.write(packet).then(() => this.onOpen());\n            });\n        });\n    }\n    write(packets) {\n        this.writable = false;\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            this._writer.write(packet).then(() => {\n                if (lastPacket) {\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        var _a;\n        (_a = this._transport) === null || _a === void 0 ? void 0 : _a.close();\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AAAA;;;;AASO,MAAM,WAAW,sKAAA,CAAA,YAAS;IAC7B,IAAI,OAAO;QACP,OAAO;IACX;IACA,SAAS;QACL,IAAI;YACA,aAAa;YACb,IAAI,CAAC,UAAU,GAAG,IAAI,aAAa,IAAI,CAAC,SAAS,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC;QACrG,EACA,OAAO,KAAK;YACR,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS;QACtC;QACA,IAAI,CAAC,UAAU,CAAC,MAAM,CACjB,IAAI,CAAC;YACN,IAAI,CAAC,OAAO;QAChB,GACK,KAAK,CAAC,CAAC;YACR,IAAI,CAAC,OAAO,CAAC,sBAAsB;QACvC;QACA,yFAAyF;QACzF,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC;YACvB,IAAI,CAAC,UAAU,CAAC,yBAAyB,GAAG,IAAI,CAAC,CAAC;gBAC9C,MAAM,gBAAgB,CAAA,GAAA,kLAAA,CAAA,4BAAyB,AAAD,EAAE,OAAO,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU;gBAC/F,MAAM,SAAS,OAAO,QAAQ,CAAC,WAAW,CAAC,eAAe,SAAS;gBACnE,MAAM,gBAAgB,CAAA,GAAA,kLAAA,CAAA,4BAAyB,AAAD;gBAC9C,cAAc,QAAQ,CAAC,MAAM,CAAC,OAAO,QAAQ;gBAC7C,IAAI,CAAC,OAAO,GAAG,cAAc,QAAQ,CAAC,SAAS;gBAC/C,MAAM,OAAO;oBACT,OACK,IAAI,GACJ,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE;wBACtB,IAAI,MAAM;4BACN;wBACJ;wBACA,IAAI,CAAC,QAAQ,CAAC;wBACd;oBACJ,GACK,KAAK,CAAC,CAAC,OACZ;gBACJ;gBACA;gBACA,MAAM,SAAS;oBAAE,MAAM;gBAAO;gBAC9B,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE;oBAChB,OAAO,IAAI,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC/C;gBACA,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,IAAI,CAAC,IAAM,IAAI,CAAC,MAAM;YACrD;QACJ;IACJ;IACA,MAAM,OAAO,EAAE;QACX,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACrC,MAAM,SAAS,OAAO,CAAC,EAAE;YACzB,MAAM,aAAa,MAAM,QAAQ,MAAM,GAAG;YAC1C,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,IAAI,CAAC;gBAC5B,IAAI,YAAY;oBACZ,CAAA,GAAA,oKAAA,CAAA,WAAQ,AAAD,EAAE;wBACL,IAAI,CAAC,QAAQ,GAAG;wBAChB,IAAI,CAAC,YAAY,CAAC;oBACtB,GAAG,IAAI,CAAC,YAAY;gBACxB;YACJ;QACJ;IACJ;IACA,UAAU;QACN,IAAI;QACJ,CAAC,KAAK,IAAI,CAAC,UAAU,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK;IACxE;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1439, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/engine.io-client/build/esm/transports/index.js"], "sourcesContent": ["import { XHR } from \"./polling-xhr.node.js\";\nimport { WS } from \"./websocket.node.js\";\nimport { WT } from \"./webtransport.js\";\nexport const transports = {\n    websocket: WS,\n    webtransport: WT,\n    polling: XHR,\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,MAAM,aAAa;IACtB,WAAW,oLAAA,CAAA,KAAE;IACb,cAAc,uLAAA,CAAA,KAAE;IAChB,SAAS,yLAAA,CAAA,MAAG;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1459, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/engine.io-client/build/esm/contrib/parseuri.js"], "sourcesContent": ["// imported from https://github.com/galkn/parseuri\n/**\n * Parses a URI\n *\n * Note: we could also have used the built-in URL object, but it isn't supported on all platforms.\n *\n * See:\n * - https://developer.mozilla.org/en-US/docs/Web/API/URL\n * - https://caniuse.com/url\n * - https://www.rfc-editor.org/rfc/rfc3986#appendix-B\n *\n * History of the parse() method:\n * - first commit: https://github.com/socketio/socket.io-client/commit/4ee1d5d94b3906a9c052b459f1a818b15f38f91c\n * - export into its own module: https://github.com/socketio/engine.io-client/commit/de2c561e4564efeb78f1bdb1ba39ef81b2822cb3\n * - reimport: https://github.com/socketio/engine.io-client/commit/df32277c3f6d622eec5ed09f493cae3f3391d242\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\nconst re = /^(?:(?![^:@\\/?#]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@\\/?#]*)(?::([^:@\\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\nconst parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\nexport function parse(str) {\n    if (str.length > 8000) {\n        throw \"URI too long\";\n    }\n    const src = str, b = str.indexOf('['), e = str.indexOf(']');\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n    let m = re.exec(str || ''), uri = {}, i = 14;\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n    return uri;\n}\nfunction pathNames(obj, path) {\n    const regx = /\\/{2,9}/g, names = path.replace(regx, \"/\").split(\"/\");\n    if (path.slice(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.slice(-1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n    return names;\n}\nfunction queryKey(uri, query) {\n    const data = {};\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n    return data;\n}\n"], "names": [], "mappings": "AAAA,kDAAkD;AAClD;;;;;;;;;;;;;;;;;CAiBC;;;AACD,MAAM,KAAK;AACX,MAAM,QAAQ;IACV;IAAU;IAAY;IAAa;IAAY;IAAQ;IAAY;IAAQ;IAAQ;IAAY;IAAQ;IAAa;IAAQ;IAAS;CACxI;AACM,SAAS,MAAM,GAAG;IACrB,IAAI,IAAI,MAAM,GAAG,MAAM;QACnB,MAAM;IACV;IACA,MAAM,MAAM,KAAK,IAAI,IAAI,OAAO,CAAC,MAAM,IAAI,IAAI,OAAO,CAAC;IACvD,IAAI,KAAK,CAAC,KAAK,KAAK,CAAC,GAAG;QACpB,MAAM,IAAI,SAAS,CAAC,GAAG,KAAK,IAAI,SAAS,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM,OAAO,IAAI,SAAS,CAAC,GAAG,IAAI,MAAM;IACpG;IACA,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,KAAK,MAAM,CAAC,GAAG,IAAI;IAC1C,MAAO,IAAK;QACR,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI;IAC5B;IACA,IAAI,KAAK,CAAC,KAAK,KAAK,CAAC,GAAG;QACpB,IAAI,MAAM,GAAG;QACb,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,IAAI,CAAC,MAAM,GAAG,GAAG,OAAO,CAAC,MAAM;QACpE,IAAI,SAAS,GAAG,IAAI,SAAS,CAAC,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,MAAM;QAC9E,IAAI,OAAO,GAAG;IAClB;IACA,IAAI,SAAS,GAAG,UAAU,KAAK,GAAG,CAAC,OAAO;IAC1C,IAAI,QAAQ,GAAG,SAAS,KAAK,GAAG,CAAC,QAAQ;IACzC,OAAO;AACX;AACA,SAAS,UAAU,GAAG,EAAE,IAAI;IACxB,MAAM,OAAO,YAAY,QAAQ,KAAK,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC;IAC/D,IAAI,KAAK,KAAK,CAAC,GAAG,MAAM,OAAO,KAAK,MAAM,KAAK,GAAG;QAC9C,MAAM,MAAM,CAAC,GAAG;IACpB;IACA,IAAI,KAAK,KAAK,CAAC,CAAC,MAAM,KAAK;QACvB,MAAM,MAAM,CAAC,MAAM,MAAM,GAAG,GAAG;IACnC;IACA,OAAO;AACX;AACA,SAAS,SAAS,GAAG,EAAE,KAAK;IACxB,MAAM,OAAO,CAAC;IACd,MAAM,OAAO,CAAC,6BAA6B,SAAU,EAAE,EAAE,EAAE,EAAE,EAAE;QAC3D,IAAI,IAAI;YACJ,IAAI,CAAC,GAAG,GAAG;QACf;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1544, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/engine.io-client/build/esm/socket.js"], "sourcesContent": ["import { transports as DEFAULT_TRANSPORTS } from \"./transports/index.js\";\nimport { installTimerFunctions, byteLength } from \"./util.js\";\nimport { decode } from \"./contrib/parseqs.js\";\nimport { parse } from \"./contrib/parseuri.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { protocol } from \"engine.io-parser\";\nimport { createCookieJar, defaultBinaryType, nextTick, } from \"./globals.node.js\";\nconst withEventListeners = typeof addEventListener === \"function\" &&\n    typeof removeEventListener === \"function\";\nconst OFFLINE_EVENT_LISTENERS = [];\nif (withEventListeners) {\n    // within a ServiceWorker, any event handler for the 'offline' event must be added on the initial evaluation of the\n    // script, so we create one single event listener here which will forward the event to the socket instances\n    addEventListener(\"offline\", () => {\n        OFFLINE_EVENT_LISTENERS.forEach((listener) => listener());\n    }, false);\n}\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes without upgrade mechanism, which means that it will keep the first low-level transport that\n * successfully establishes the connection.\n *\n * In order to allow tree-shaking, there are no transports included, that's why the `transports` option is mandatory.\n *\n * @example\n * import { SocketWithoutUpgrade, WebSocket } from \"engine.io-client\";\n *\n * const socket = new SocketWithoutUpgrade({\n *   transports: [WebSocket]\n * });\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithUpgrade\n * @see Socket\n */\nexport class SocketWithoutUpgrade extends Emitter {\n    /**\n     * Socket constructor.\n     *\n     * @param {String|Object} uri - uri or options\n     * @param {Object} opts - options\n     */\n    constructor(uri, opts) {\n        super();\n        this.binaryType = defaultBinaryType;\n        this.writeBuffer = [];\n        this._prevBufferLen = 0;\n        this._pingInterval = -1;\n        this._pingTimeout = -1;\n        this._maxPayload = -1;\n        /**\n         * The expiration timestamp of the {@link _pingTimeoutTimer} object is tracked, in case the timer is throttled and the\n         * callback is not fired on time. This can happen for example when a laptop is suspended or when a phone is locked.\n         */\n        this._pingTimeoutTime = Infinity;\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = null;\n        }\n        if (uri) {\n            const parsedUri = parse(uri);\n            opts.hostname = parsedUri.host;\n            opts.secure =\n                parsedUri.protocol === \"https\" || parsedUri.protocol === \"wss\";\n            opts.port = parsedUri.port;\n            if (parsedUri.query)\n                opts.query = parsedUri.query;\n        }\n        else if (opts.host) {\n            opts.hostname = parse(opts.host).host;\n        }\n        installTimerFunctions(this, opts);\n        this.secure =\n            null != opts.secure\n                ? opts.secure\n                : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n        if (opts.hostname && !opts.port) {\n            // if no port is specified manually, use the protocol default\n            opts.port = this.secure ? \"443\" : \"80\";\n        }\n        this.hostname =\n            opts.hostname ||\n                (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n        this.port =\n            opts.port ||\n                (typeof location !== \"undefined\" && location.port\n                    ? location.port\n                    : this.secure\n                        ? \"443\"\n                        : \"80\");\n        this.transports = [];\n        this._transportsByName = {};\n        opts.transports.forEach((t) => {\n            const transportName = t.prototype.name;\n            this.transports.push(transportName);\n            this._transportsByName[transportName] = t;\n        });\n        this.opts = Object.assign({\n            path: \"/engine.io\",\n            agent: false,\n            withCredentials: false,\n            upgrade: true,\n            timestampParam: \"t\",\n            rememberUpgrade: false,\n            addTrailingSlash: true,\n            rejectUnauthorized: true,\n            perMessageDeflate: {\n                threshold: 1024,\n            },\n            transportOptions: {},\n            closeOnBeforeunload: false,\n        }, opts);\n        this.opts.path =\n            this.opts.path.replace(/\\/$/, \"\") +\n                (this.opts.addTrailingSlash ? \"/\" : \"\");\n        if (typeof this.opts.query === \"string\") {\n            this.opts.query = decode(this.opts.query);\n        }\n        if (withEventListeners) {\n            if (this.opts.closeOnBeforeunload) {\n                // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n                // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n                // closed/reloaded)\n                this._beforeunloadEventListener = () => {\n                    if (this.transport) {\n                        // silently close the transport\n                        this.transport.removeAllListeners();\n                        this.transport.close();\n                    }\n                };\n                addEventListener(\"beforeunload\", this._beforeunloadEventListener, false);\n            }\n            if (this.hostname !== \"localhost\") {\n                this._offlineEventListener = () => {\n                    this._onClose(\"transport close\", {\n                        description: \"network connection lost\",\n                    });\n                };\n                OFFLINE_EVENT_LISTENERS.push(this._offlineEventListener);\n            }\n        }\n        if (this.opts.withCredentials) {\n            this._cookieJar = createCookieJar();\n        }\n        this._open();\n    }\n    /**\n     * Creates transport of the given type.\n     *\n     * @param {String} name - transport name\n     * @return {Transport}\n     * @private\n     */\n    createTransport(name) {\n        const query = Object.assign({}, this.opts.query);\n        // append engine.io protocol identifier\n        query.EIO = protocol;\n        // transport name\n        query.transport = name;\n        // session id if we already have one\n        if (this.id)\n            query.sid = this.id;\n        const opts = Object.assign({}, this.opts, {\n            query,\n            socket: this,\n            hostname: this.hostname,\n            secure: this.secure,\n            port: this.port,\n        }, this.opts.transportOptions[name]);\n        return new this._transportsByName[name](opts);\n    }\n    /**\n     * Initializes transport to use and starts probe.\n     *\n     * @private\n     */\n    _open() {\n        if (this.transports.length === 0) {\n            // Emit error on next tick so it can be listened to\n            this.setTimeoutFn(() => {\n                this.emitReserved(\"error\", \"No transports available\");\n            }, 0);\n            return;\n        }\n        const transportName = this.opts.rememberUpgrade &&\n            SocketWithoutUpgrade.priorWebsocketSuccess &&\n            this.transports.indexOf(\"websocket\") !== -1\n            ? \"websocket\"\n            : this.transports[0];\n        this.readyState = \"opening\";\n        const transport = this.createTransport(transportName);\n        transport.open();\n        this.setTransport(transport);\n    }\n    /**\n     * Sets the current transport. Disables the existing one (if any).\n     *\n     * @private\n     */\n    setTransport(transport) {\n        if (this.transport) {\n            this.transport.removeAllListeners();\n        }\n        // set up transport\n        this.transport = transport;\n        // set up transport listeners\n        transport\n            .on(\"drain\", this._onDrain.bind(this))\n            .on(\"packet\", this._onPacket.bind(this))\n            .on(\"error\", this._onError.bind(this))\n            .on(\"close\", (reason) => this._onClose(\"transport close\", reason));\n    }\n    /**\n     * Called when connection is deemed open.\n     *\n     * @private\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        SocketWithoutUpgrade.priorWebsocketSuccess =\n            \"websocket\" === this.transport.name;\n        this.emitReserved(\"open\");\n        this.flush();\n    }\n    /**\n     * Handles a packet.\n     *\n     * @private\n     */\n    _onPacket(packet) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            this.emitReserved(\"packet\", packet);\n            // Socket is live - any packet counts\n            this.emitReserved(\"heartbeat\");\n            switch (packet.type) {\n                case \"open\":\n                    this.onHandshake(JSON.parse(packet.data));\n                    break;\n                case \"ping\":\n                    this._sendPacket(\"pong\");\n                    this.emitReserved(\"ping\");\n                    this.emitReserved(\"pong\");\n                    this._resetPingTimeout();\n                    break;\n                case \"error\":\n                    const err = new Error(\"server error\");\n                    // @ts-ignore\n                    err.code = packet.data;\n                    this._onError(err);\n                    break;\n                case \"message\":\n                    this.emitReserved(\"data\", packet.data);\n                    this.emitReserved(\"message\", packet.data);\n                    break;\n            }\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon handshake completion.\n     *\n     * @param {Object} data - handshake obj\n     * @private\n     */\n    onHandshake(data) {\n        this.emitReserved(\"handshake\", data);\n        this.id = data.sid;\n        this.transport.query.sid = data.sid;\n        this._pingInterval = data.pingInterval;\n        this._pingTimeout = data.pingTimeout;\n        this._maxPayload = data.maxPayload;\n        this.onOpen();\n        // In case open handler closes socket\n        if (\"closed\" === this.readyState)\n            return;\n        this._resetPingTimeout();\n    }\n    /**\n     * Sets and resets ping timeout timer based on server pings.\n     *\n     * @private\n     */\n    _resetPingTimeout() {\n        this.clearTimeoutFn(this._pingTimeoutTimer);\n        const delay = this._pingInterval + this._pingTimeout;\n        this._pingTimeoutTime = Date.now() + delay;\n        this._pingTimeoutTimer = this.setTimeoutFn(() => {\n            this._onClose(\"ping timeout\");\n        }, delay);\n        if (this.opts.autoUnref) {\n            this._pingTimeoutTimer.unref();\n        }\n    }\n    /**\n     * Called on `drain` event\n     *\n     * @private\n     */\n    _onDrain() {\n        this.writeBuffer.splice(0, this._prevBufferLen);\n        // setting prevBufferLen = 0 is very important\n        // for example, when upgrading, upgrade packet is sent over,\n        // and a nonzero prevBufferLen could cause problems on `drain`\n        this._prevBufferLen = 0;\n        if (0 === this.writeBuffer.length) {\n            this.emitReserved(\"drain\");\n        }\n        else {\n            this.flush();\n        }\n    }\n    /**\n     * Flush write buffers.\n     *\n     * @private\n     */\n    flush() {\n        if (\"closed\" !== this.readyState &&\n            this.transport.writable &&\n            !this.upgrading &&\n            this.writeBuffer.length) {\n            const packets = this._getWritablePackets();\n            this.transport.send(packets);\n            // keep track of current length of writeBuffer\n            // splice writeBuffer and callbackBuffer on `drain`\n            this._prevBufferLen = packets.length;\n            this.emitReserved(\"flush\");\n        }\n    }\n    /**\n     * Ensure the encoded size of the writeBuffer is below the maxPayload value sent by the server (only for HTTP\n     * long-polling)\n     *\n     * @private\n     */\n    _getWritablePackets() {\n        const shouldCheckPayloadSize = this._maxPayload &&\n            this.transport.name === \"polling\" &&\n            this.writeBuffer.length > 1;\n        if (!shouldCheckPayloadSize) {\n            return this.writeBuffer;\n        }\n        let payloadSize = 1; // first packet type\n        for (let i = 0; i < this.writeBuffer.length; i++) {\n            const data = this.writeBuffer[i].data;\n            if (data) {\n                payloadSize += byteLength(data);\n            }\n            if (i > 0 && payloadSize > this._maxPayload) {\n                return this.writeBuffer.slice(0, i);\n            }\n            payloadSize += 2; // separator + packet type\n        }\n        return this.writeBuffer;\n    }\n    /**\n     * Checks whether the heartbeat timer has expired but the socket has not yet been notified.\n     *\n     * Note: this method is private for now because it does not really fit the WebSocket API, but if we put it in the\n     * `write()` method then the message would not be buffered by the Socket.IO client.\n     *\n     * @return {boolean}\n     * @private\n     */\n    /* private */ _hasPingExpired() {\n        if (!this._pingTimeoutTime)\n            return true;\n        const hasExpired = Date.now() > this._pingTimeoutTime;\n        if (hasExpired) {\n            this._pingTimeoutTime = 0;\n            nextTick(() => {\n                this._onClose(\"ping timeout\");\n            }, this.setTimeoutFn);\n        }\n        return hasExpired;\n    }\n    /**\n     * Sends a message.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @return {Socket} for chaining.\n     */\n    write(msg, options, fn) {\n        this._sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a message. Alias of {@link Socket#write}.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @return {Socket} for chaining.\n     */\n    send(msg, options, fn) {\n        this._sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param {String} type: packet type.\n     * @param {String} data.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @private\n     */\n    _sendPacket(type, data, options, fn) {\n        if (\"function\" === typeof data) {\n            fn = data;\n            data = undefined;\n        }\n        if (\"function\" === typeof options) {\n            fn = options;\n            options = null;\n        }\n        if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n            return;\n        }\n        options = options || {};\n        options.compress = false !== options.compress;\n        const packet = {\n            type: type,\n            data: data,\n            options: options,\n        };\n        this.emitReserved(\"packetCreate\", packet);\n        this.writeBuffer.push(packet);\n        if (fn)\n            this.once(\"flush\", fn);\n        this.flush();\n    }\n    /**\n     * Closes the connection.\n     */\n    close() {\n        const close = () => {\n            this._onClose(\"forced close\");\n            this.transport.close();\n        };\n        const cleanupAndClose = () => {\n            this.off(\"upgrade\", cleanupAndClose);\n            this.off(\"upgradeError\", cleanupAndClose);\n            close();\n        };\n        const waitForUpgrade = () => {\n            // wait for upgrade to finish since we can't send packets while pausing a transport\n            this.once(\"upgrade\", cleanupAndClose);\n            this.once(\"upgradeError\", cleanupAndClose);\n        };\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.readyState = \"closing\";\n            if (this.writeBuffer.length) {\n                this.once(\"drain\", () => {\n                    if (this.upgrading) {\n                        waitForUpgrade();\n                    }\n                    else {\n                        close();\n                    }\n                });\n            }\n            else if (this.upgrading) {\n                waitForUpgrade();\n            }\n            else {\n                close();\n            }\n        }\n        return this;\n    }\n    /**\n     * Called upon transport error\n     *\n     * @private\n     */\n    _onError(err) {\n        SocketWithoutUpgrade.priorWebsocketSuccess = false;\n        if (this.opts.tryAllTransports &&\n            this.transports.length > 1 &&\n            this.readyState === \"opening\") {\n            this.transports.shift();\n            return this._open();\n        }\n        this.emitReserved(\"error\", err);\n        this._onClose(\"transport error\", err);\n    }\n    /**\n     * Called upon transport close.\n     *\n     * @private\n     */\n    _onClose(reason, description) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            // clear timers\n            this.clearTimeoutFn(this._pingTimeoutTimer);\n            // stop event from firing again for transport\n            this.transport.removeAllListeners(\"close\");\n            // ensure transport won't stay open\n            this.transport.close();\n            // ignore further transport communication\n            this.transport.removeAllListeners();\n            if (withEventListeners) {\n                if (this._beforeunloadEventListener) {\n                    removeEventListener(\"beforeunload\", this._beforeunloadEventListener, false);\n                }\n                if (this._offlineEventListener) {\n                    const i = OFFLINE_EVENT_LISTENERS.indexOf(this._offlineEventListener);\n                    if (i !== -1) {\n                        OFFLINE_EVENT_LISTENERS.splice(i, 1);\n                    }\n                }\n            }\n            // set ready state\n            this.readyState = \"closed\";\n            // clear session id\n            this.id = null;\n            // emit close event\n            this.emitReserved(\"close\", reason, description);\n            // clean buffers after, so users can still\n            // grab the buffers on `close` event\n            this.writeBuffer = [];\n            this._prevBufferLen = 0;\n        }\n    }\n}\nSocketWithoutUpgrade.protocol = protocol;\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes with an upgrade mechanism, which means that once the connection is established with the first\n * low-level transport, it will try to upgrade to a better transport.\n *\n * In order to allow tree-shaking, there are no transports included, that's why the `transports` option is mandatory.\n *\n * @example\n * import { SocketWithUpgrade, WebSocket } from \"engine.io-client\";\n *\n * const socket = new SocketWithUpgrade({\n *   transports: [WebSocket]\n * });\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithoutUpgrade\n * @see Socket\n */\nexport class SocketWithUpgrade extends SocketWithoutUpgrade {\n    constructor() {\n        super(...arguments);\n        this._upgrades = [];\n    }\n    onOpen() {\n        super.onOpen();\n        if (\"open\" === this.readyState && this.opts.upgrade) {\n            for (let i = 0; i < this._upgrades.length; i++) {\n                this._probe(this._upgrades[i]);\n            }\n        }\n    }\n    /**\n     * Probes a transport.\n     *\n     * @param {String} name - transport name\n     * @private\n     */\n    _probe(name) {\n        let transport = this.createTransport(name);\n        let failed = false;\n        SocketWithoutUpgrade.priorWebsocketSuccess = false;\n        const onTransportOpen = () => {\n            if (failed)\n                return;\n            transport.send([{ type: \"ping\", data: \"probe\" }]);\n            transport.once(\"packet\", (msg) => {\n                if (failed)\n                    return;\n                if (\"pong\" === msg.type && \"probe\" === msg.data) {\n                    this.upgrading = true;\n                    this.emitReserved(\"upgrading\", transport);\n                    if (!transport)\n                        return;\n                    SocketWithoutUpgrade.priorWebsocketSuccess =\n                        \"websocket\" === transport.name;\n                    this.transport.pause(() => {\n                        if (failed)\n                            return;\n                        if (\"closed\" === this.readyState)\n                            return;\n                        cleanup();\n                        this.setTransport(transport);\n                        transport.send([{ type: \"upgrade\" }]);\n                        this.emitReserved(\"upgrade\", transport);\n                        transport = null;\n                        this.upgrading = false;\n                        this.flush();\n                    });\n                }\n                else {\n                    const err = new Error(\"probe error\");\n                    // @ts-ignore\n                    err.transport = transport.name;\n                    this.emitReserved(\"upgradeError\", err);\n                }\n            });\n        };\n        function freezeTransport() {\n            if (failed)\n                return;\n            // Any callback called by transport should be ignored since now\n            failed = true;\n            cleanup();\n            transport.close();\n            transport = null;\n        }\n        // Handle any error that happens while probing\n        const onerror = (err) => {\n            const error = new Error(\"probe error: \" + err);\n            // @ts-ignore\n            error.transport = transport.name;\n            freezeTransport();\n            this.emitReserved(\"upgradeError\", error);\n        };\n        function onTransportClose() {\n            onerror(\"transport closed\");\n        }\n        // When the socket is closed while we're probing\n        function onclose() {\n            onerror(\"socket closed\");\n        }\n        // When the socket is upgraded while we're probing\n        function onupgrade(to) {\n            if (transport && to.name !== transport.name) {\n                freezeTransport();\n            }\n        }\n        // Remove all listeners on the transport and on self\n        const cleanup = () => {\n            transport.removeListener(\"open\", onTransportOpen);\n            transport.removeListener(\"error\", onerror);\n            transport.removeListener(\"close\", onTransportClose);\n            this.off(\"close\", onclose);\n            this.off(\"upgrading\", onupgrade);\n        };\n        transport.once(\"open\", onTransportOpen);\n        transport.once(\"error\", onerror);\n        transport.once(\"close\", onTransportClose);\n        this.once(\"close\", onclose);\n        this.once(\"upgrading\", onupgrade);\n        if (this._upgrades.indexOf(\"webtransport\") !== -1 &&\n            name !== \"webtransport\") {\n            // favor WebTransport\n            this.setTimeoutFn(() => {\n                if (!failed) {\n                    transport.open();\n                }\n            }, 200);\n        }\n        else {\n            transport.open();\n        }\n    }\n    onHandshake(data) {\n        this._upgrades = this._filterUpgrades(data.upgrades);\n        super.onHandshake(data);\n    }\n    /**\n     * Filters upgrades, returning only those matching client transports.\n     *\n     * @param {Array} upgrades - server upgrades\n     * @private\n     */\n    _filterUpgrades(upgrades) {\n        const filteredUpgrades = [];\n        for (let i = 0; i < upgrades.length; i++) {\n            if (~this.transports.indexOf(upgrades[i]))\n                filteredUpgrades.push(upgrades[i]);\n        }\n        return filteredUpgrades;\n    }\n}\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes with an upgrade mechanism, which means that once the connection is established with the first\n * low-level transport, it will try to upgrade to a better transport.\n *\n * @example\n * import { Socket } from \"engine.io-client\";\n *\n * const socket = new Socket();\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithoutUpgrade\n * @see SocketWithUpgrade\n */\nexport class Socket extends SocketWithUpgrade {\n    constructor(uri, opts = {}) {\n        const o = typeof uri === \"object\" ? uri : opts;\n        if (!o.transports ||\n            (o.transports && typeof o.transports[0] === \"string\")) {\n            o.transports = (o.transports || [\"polling\", \"websocket\", \"webtransport\"])\n                .map((transportName) => DEFAULT_TRANSPORTS[transportName])\n                .filter((t) => !!t);\n        }\n        super(uri, o);\n    }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;;;;;;;;AACA,MAAM,qBAAqB,OAAO,qBAAqB,cACnD,OAAO,wBAAwB;AACnC,MAAM,0BAA0B,EAAE;AAClC,IAAI,oBAAoB;IACpB,mHAAmH;IACnH,2GAA2G;IAC3G,iBAAiB,WAAW;QACxB,wBAAwB,OAAO,CAAC,CAAC,WAAa;IAClD,GAAG;AACP;AAwBO,MAAM,6BAA6B,gLAAA,CAAA,UAAO;IAC7C;;;;;KAKC,GACD,YAAY,GAAG,EAAE,IAAI,CAAE;QACnB,KAAK;QACL,IAAI,CAAC,UAAU,GAAG,oKAAA,CAAA,oBAAiB;QACnC,IAAI,CAAC,WAAW,GAAG,EAAE;QACrB,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,aAAa,GAAG,CAAC;QACtB,IAAI,CAAC,YAAY,GAAG,CAAC;QACrB,IAAI,CAAC,WAAW,GAAG,CAAC;QACpB;;;SAGC,GACD,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,OAAO,aAAa,OAAO,KAAK;YAChC,OAAO;YACP,MAAM;QACV;QACA,IAAI,KAAK;YACL,MAAM,YAAY,CAAA,GAAA,gLAAA,CAAA,QAAK,AAAD,EAAE;YACxB,KAAK,QAAQ,GAAG,UAAU,IAAI;YAC9B,KAAK,MAAM,GACP,UAAU,QAAQ,KAAK,WAAW,UAAU,QAAQ,KAAK;YAC7D,KAAK,IAAI,GAAG,UAAU,IAAI;YAC1B,IAAI,UAAU,KAAK,EACf,KAAK,KAAK,GAAG,UAAU,KAAK;QACpC,OACK,IAAI,KAAK,IAAI,EAAE;YAChB,KAAK,QAAQ,GAAG,CAAA,GAAA,gLAAA,CAAA,QAAK,AAAD,EAAE,KAAK,IAAI,EAAE,IAAI;QACzC;QACA,CAAA,GAAA,iKAAA,CAAA,wBAAqB,AAAD,EAAE,IAAI,EAAE;QAC5B,IAAI,CAAC,MAAM,GACP,QAAQ,KAAK,MAAM,GACb,KAAK,MAAM,GACX,OAAO,aAAa,eAAe,aAAa,SAAS,QAAQ;QAC3E,IAAI,KAAK,QAAQ,IAAI,CAAC,KAAK,IAAI,EAAE;YAC7B,6DAA6D;YAC7D,KAAK,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,QAAQ;QACtC;QACA,IAAI,CAAC,QAAQ,GACT,KAAK,QAAQ,IACT,CAAC,OAAO,aAAa,cAAc,SAAS,QAAQ,GAAG,WAAW;QAC1E,IAAI,CAAC,IAAI,GACL,KAAK,IAAI,IACL,CAAC,OAAO,aAAa,eAAe,SAAS,IAAI,GAC3C,SAAS,IAAI,GACb,IAAI,CAAC,MAAM,GACP,QACA,IAAI;QACtB,IAAI,CAAC,UAAU,GAAG,EAAE;QACpB,IAAI,CAAC,iBAAiB,GAAG,CAAC;QAC1B,KAAK,UAAU,CAAC,OAAO,CAAC,CAAC;YACrB,MAAM,gBAAgB,EAAE,SAAS,CAAC,IAAI;YACtC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;YACrB,IAAI,CAAC,iBAAiB,CAAC,cAAc,GAAG;QAC5C;QACA,IAAI,CAAC,IAAI,GAAG,OAAO,MAAM,CAAC;YACtB,MAAM;YACN,OAAO;YACP,iBAAiB;YACjB,SAAS;YACT,gBAAgB;YAChB,iBAAiB;YACjB,kBAAkB;YAClB,oBAAoB;YACpB,mBAAmB;gBACf,WAAW;YACf;YACA,kBAAkB,CAAC;YACnB,qBAAqB;QACzB,GAAG;QACH,IAAI,CAAC,IAAI,CAAC,IAAI,GACV,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,MAC1B,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,GAAG,MAAM,EAAE;QAC9C,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,UAAU;YACrC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK;QAC5C;QACA,IAAI,oBAAoB;YACpB,IAAI,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBAC/B,6GAA6G;gBAC7G,wGAAwG;gBACxG,mBAAmB;gBACnB,IAAI,CAAC,0BAA0B,GAAG;oBAC9B,IAAI,IAAI,CAAC,SAAS,EAAE;wBAChB,+BAA+B;wBAC/B,IAAI,CAAC,SAAS,CAAC,kBAAkB;wBACjC,IAAI,CAAC,SAAS,CAAC,KAAK;oBACxB;gBACJ;gBACA,iBAAiB,gBAAgB,IAAI,CAAC,0BAA0B,EAAE;YACtE;YACA,IAAI,IAAI,CAAC,QAAQ,KAAK,aAAa;gBAC/B,IAAI,CAAC,qBAAqB,GAAG;oBACzB,IAAI,CAAC,QAAQ,CAAC,mBAAmB;wBAC7B,aAAa;oBACjB;gBACJ;gBACA,wBAAwB,IAAI,CAAC,IAAI,CAAC,qBAAqB;YAC3D;QACJ;QACA,IAAI,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YAC3B,IAAI,CAAC,UAAU,GAAG,CAAA,GAAA,oKAAA,CAAA,kBAAe,AAAD;QACpC;QACA,IAAI,CAAC,KAAK;IACd;IACA;;;;;;KAMC,GACD,gBAAgB,IAAI,EAAE;QAClB,MAAM,QAAQ,OAAO,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK;QAC/C,uCAAuC;QACvC,MAAM,GAAG,GAAG,kLAAA,CAAA,WAAQ;QACpB,iBAAiB;QACjB,MAAM,SAAS,GAAG;QAClB,oCAAoC;QACpC,IAAI,IAAI,CAAC,EAAE,EACP,MAAM,GAAG,GAAG,IAAI,CAAC,EAAE;QACvB,MAAM,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE;YACtC;YACA,QAAQ,IAAI;YACZ,UAAU,IAAI,CAAC,QAAQ;YACvB,QAAQ,IAAI,CAAC,MAAM;YACnB,MAAM,IAAI,CAAC,IAAI;QACnB,GAAG,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK;QACnC,OAAO,IAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;IAC5C;IACA;;;;KAIC,GACD,QAAQ;QACJ,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,GAAG;YAC9B,mDAAmD;YACnD,IAAI,CAAC,YAAY,CAAC;gBACd,IAAI,CAAC,YAAY,CAAC,SAAS;YAC/B,GAAG;YACH;QACJ;QACA,MAAM,gBAAgB,IAAI,CAAC,IAAI,CAAC,eAAe,IAC3C,qBAAqB,qBAAqB,IAC1C,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,iBAAiB,CAAC,IACxC,cACA,IAAI,CAAC,UAAU,CAAC,EAAE;QACxB,IAAI,CAAC,UAAU,GAAG;QAClB,MAAM,YAAY,IAAI,CAAC,eAAe,CAAC;QACvC,UAAU,IAAI;QACd,IAAI,CAAC,YAAY,CAAC;IACtB;IACA;;;;KAIC,GACD,aAAa,SAAS,EAAE;QACpB,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,SAAS,CAAC,kBAAkB;QACrC;QACA,mBAAmB;QACnB,IAAI,CAAC,SAAS,GAAG;QACjB,6BAA6B;QAC7B,UACK,EAAE,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,GACnC,EAAE,CAAC,UAAU,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,GACrC,EAAE,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,GACnC,EAAE,CAAC,SAAS,CAAC,SAAW,IAAI,CAAC,QAAQ,CAAC,mBAAmB;IAClE;IACA;;;;KAIC,GACD,SAAS;QACL,IAAI,CAAC,UAAU,GAAG;QAClB,qBAAqB,qBAAqB,GACtC,gBAAgB,IAAI,CAAC,SAAS,CAAC,IAAI;QACvC,IAAI,CAAC,YAAY,CAAC;QAClB,IAAI,CAAC,KAAK;IACd;IACA;;;;KAIC,GACD,UAAU,MAAM,EAAE;QACd,IAAI,cAAc,IAAI,CAAC,UAAU,IAC7B,WAAW,IAAI,CAAC,UAAU,IAC1B,cAAc,IAAI,CAAC,UAAU,EAAE;YAC/B,IAAI,CAAC,YAAY,CAAC,UAAU;YAC5B,qCAAqC;YACrC,IAAI,CAAC,YAAY,CAAC;YAClB,OAAQ,OAAO,IAAI;gBACf,KAAK;oBACD,IAAI,CAAC,WAAW,CAAC,KAAK,KAAK,CAAC,OAAO,IAAI;oBACvC;gBACJ,KAAK;oBACD,IAAI,CAAC,WAAW,CAAC;oBACjB,IAAI,CAAC,YAAY,CAAC;oBAClB,IAAI,CAAC,YAAY,CAAC;oBAClB,IAAI,CAAC,iBAAiB;oBACtB;gBACJ,KAAK;oBACD,MAAM,MAAM,IAAI,MAAM;oBACtB,aAAa;oBACb,IAAI,IAAI,GAAG,OAAO,IAAI;oBACtB,IAAI,CAAC,QAAQ,CAAC;oBACd;gBACJ,KAAK;oBACD,IAAI,CAAC,YAAY,CAAC,QAAQ,OAAO,IAAI;oBACrC,IAAI,CAAC,YAAY,CAAC,WAAW,OAAO,IAAI;oBACxC;YACR;QACJ,OACK,CACL;IACJ;IACA;;;;;KAKC,GACD,YAAY,IAAI,EAAE;QACd,IAAI,CAAC,YAAY,CAAC,aAAa;QAC/B,IAAI,CAAC,EAAE,GAAG,KAAK,GAAG;QAClB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,KAAK,GAAG;QACnC,IAAI,CAAC,aAAa,GAAG,KAAK,YAAY;QACtC,IAAI,CAAC,YAAY,GAAG,KAAK,WAAW;QACpC,IAAI,CAAC,WAAW,GAAG,KAAK,UAAU;QAClC,IAAI,CAAC,MAAM;QACX,qCAAqC;QACrC,IAAI,aAAa,IAAI,CAAC,UAAU,EAC5B;QACJ,IAAI,CAAC,iBAAiB;IAC1B;IACA;;;;KAIC,GACD,oBAAoB;QAChB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,iBAAiB;QAC1C,MAAM,QAAQ,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,YAAY;QACpD,IAAI,CAAC,gBAAgB,GAAG,KAAK,GAAG,KAAK;QACrC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,YAAY,CAAC;YACvC,IAAI,CAAC,QAAQ,CAAC;QAClB,GAAG;QACH,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACrB,IAAI,CAAC,iBAAiB,CAAC,KAAK;QAChC;IACJ;IACA;;;;KAIC,GACD,WAAW;QACP,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,cAAc;QAC9C,8CAA8C;QAC9C,4DAA4D;QAC5D,8DAA8D;QAC9D,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;YAC/B,IAAI,CAAC,YAAY,CAAC;QACtB,OACK;YACD,IAAI,CAAC,KAAK;QACd;IACJ;IACA;;;;KAIC,GACD,QAAQ;QACJ,IAAI,aAAa,IAAI,CAAC,UAAU,IAC5B,IAAI,CAAC,SAAS,CAAC,QAAQ,IACvB,CAAC,IAAI,CAAC,SAAS,IACf,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;YACzB,MAAM,UAAU,IAAI,CAAC,mBAAmB;YACxC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;YACpB,8CAA8C;YAC9C,mDAAmD;YACnD,IAAI,CAAC,cAAc,GAAG,QAAQ,MAAM;YACpC,IAAI,CAAC,YAAY,CAAC;QACtB;IACJ;IACA;;;;;KAKC,GACD,sBAAsB;QAClB,MAAM,yBAAyB,IAAI,CAAC,WAAW,IAC3C,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,aACxB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG;QAC9B,IAAI,CAAC,wBAAwB;YACzB,OAAO,IAAI,CAAC,WAAW;QAC3B;QACA,IAAI,cAAc,GAAG,oBAAoB;QACzC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,IAAK;YAC9C,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,IAAI;YACrC,IAAI,MAAM;gBACN,eAAe,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE;YAC9B;YACA,IAAI,IAAI,KAAK,cAAc,IAAI,CAAC,WAAW,EAAE;gBACzC,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG;YACrC;YACA,eAAe,GAAG,0BAA0B;QAChD;QACA,OAAO,IAAI,CAAC,WAAW;IAC3B;IACA;;;;;;;;KAQC,GACD,WAAW,GAAG,kBAAkB;QAC5B,IAAI,CAAC,IAAI,CAAC,gBAAgB,EACtB,OAAO;QACX,MAAM,aAAa,KAAK,GAAG,KAAK,IAAI,CAAC,gBAAgB;QACrD,IAAI,YAAY;YACZ,IAAI,CAAC,gBAAgB,GAAG;YACxB,CAAA,GAAA,oKAAA,CAAA,WAAQ,AAAD,EAAE;gBACL,IAAI,CAAC,QAAQ,CAAC;YAClB,GAAG,IAAI,CAAC,YAAY;QACxB;QACA,OAAO;IACX;IACA;;;;;;;KAOC,GACD,MAAM,GAAG,EAAE,OAAO,EAAE,EAAE,EAAE;QACpB,IAAI,CAAC,WAAW,CAAC,WAAW,KAAK,SAAS;QAC1C,OAAO,IAAI;IACf;IACA;;;;;;;KAOC,GACD,KAAK,GAAG,EAAE,OAAO,EAAE,EAAE,EAAE;QACnB,IAAI,CAAC,WAAW,CAAC,WAAW,KAAK,SAAS;QAC1C,OAAO,IAAI;IACf;IACA;;;;;;;;KAQC,GACD,YAAY,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE;QACjC,IAAI,eAAe,OAAO,MAAM;YAC5B,KAAK;YACL,OAAO;QACX;QACA,IAAI,eAAe,OAAO,SAAS;YAC/B,KAAK;YACL,UAAU;QACd;QACA,IAAI,cAAc,IAAI,CAAC,UAAU,IAAI,aAAa,IAAI,CAAC,UAAU,EAAE;YAC/D;QACJ;QACA,UAAU,WAAW,CAAC;QACtB,QAAQ,QAAQ,GAAG,UAAU,QAAQ,QAAQ;QAC7C,MAAM,SAAS;YACX,MAAM;YACN,MAAM;YACN,SAAS;QACb;QACA,IAAI,CAAC,YAAY,CAAC,gBAAgB;QAClC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;QACtB,IAAI,IACA,IAAI,CAAC,IAAI,CAAC,SAAS;QACvB,IAAI,CAAC,KAAK;IACd;IACA;;KAEC,GACD,QAAQ;QACJ,MAAM,QAAQ;YACV,IAAI,CAAC,QAAQ,CAAC;YACd,IAAI,CAAC,SAAS,CAAC,KAAK;QACxB;QACA,MAAM,kBAAkB;YACpB,IAAI,CAAC,GAAG,CAAC,WAAW;YACpB,IAAI,CAAC,GAAG,CAAC,gBAAgB;YACzB;QACJ;QACA,MAAM,iBAAiB;YACnB,mFAAmF;YACnF,IAAI,CAAC,IAAI,CAAC,WAAW;YACrB,IAAI,CAAC,IAAI,CAAC,gBAAgB;QAC9B;QACA,IAAI,cAAc,IAAI,CAAC,UAAU,IAAI,WAAW,IAAI,CAAC,UAAU,EAAE;YAC7D,IAAI,CAAC,UAAU,GAAG;YAClB,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;gBACzB,IAAI,CAAC,IAAI,CAAC,SAAS;oBACf,IAAI,IAAI,CAAC,SAAS,EAAE;wBAChB;oBACJ,OACK;wBACD;oBACJ;gBACJ;YACJ,OACK,IAAI,IAAI,CAAC,SAAS,EAAE;gBACrB;YACJ,OACK;gBACD;YACJ;QACJ;QACA,OAAO,IAAI;IACf;IACA;;;;KAIC,GACD,SAAS,GAAG,EAAE;QACV,qBAAqB,qBAAqB,GAAG;QAC7C,IAAI,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAC1B,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,KACzB,IAAI,CAAC,UAAU,KAAK,WAAW;YAC/B,IAAI,CAAC,UAAU,CAAC,KAAK;YACrB,OAAO,IAAI,CAAC,KAAK;QACrB;QACA,IAAI,CAAC,YAAY,CAAC,SAAS;QAC3B,IAAI,CAAC,QAAQ,CAAC,mBAAmB;IACrC;IACA;;;;KAIC,GACD,SAAS,MAAM,EAAE,WAAW,EAAE;QAC1B,IAAI,cAAc,IAAI,CAAC,UAAU,IAC7B,WAAW,IAAI,CAAC,UAAU,IAC1B,cAAc,IAAI,CAAC,UAAU,EAAE;YAC/B,eAAe;YACf,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,iBAAiB;YAC1C,6CAA6C;YAC7C,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC;YAClC,mCAAmC;YACnC,IAAI,CAAC,SAAS,CAAC,KAAK;YACpB,yCAAyC;YACzC,IAAI,CAAC,SAAS,CAAC,kBAAkB;YACjC,IAAI,oBAAoB;gBACpB,IAAI,IAAI,CAAC,0BAA0B,EAAE;oBACjC,oBAAoB,gBAAgB,IAAI,CAAC,0BAA0B,EAAE;gBACzE;gBACA,IAAI,IAAI,CAAC,qBAAqB,EAAE;oBAC5B,MAAM,IAAI,wBAAwB,OAAO,CAAC,IAAI,CAAC,qBAAqB;oBACpE,IAAI,MAAM,CAAC,GAAG;wBACV,wBAAwB,MAAM,CAAC,GAAG;oBACtC;gBACJ;YACJ;YACA,kBAAkB;YAClB,IAAI,CAAC,UAAU,GAAG;YAClB,mBAAmB;YACnB,IAAI,CAAC,EAAE,GAAG;YACV,mBAAmB;YACnB,IAAI,CAAC,YAAY,CAAC,SAAS,QAAQ;YACnC,0CAA0C;YAC1C,oCAAoC;YACpC,IAAI,CAAC,WAAW,GAAG,EAAE;YACrB,IAAI,CAAC,cAAc,GAAG;QAC1B;IACJ;AACJ;AACA,qBAAqB,QAAQ,GAAG,kLAAA,CAAA,WAAQ;AAwBjC,MAAM,0BAA0B;IACnC,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,SAAS,GAAG,EAAE;IACvB;IACA,SAAS;QACL,KAAK,CAAC;QACN,IAAI,WAAW,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjD,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAK;gBAC5C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;YACjC;QACJ;IACJ;IACA;;;;;KAKC,GACD,OAAO,IAAI,EAAE;QACT,IAAI,YAAY,IAAI,CAAC,eAAe,CAAC;QACrC,IAAI,SAAS;QACb,qBAAqB,qBAAqB,GAAG;QAC7C,MAAM,kBAAkB;YACpB,IAAI,QACA;YACJ,UAAU,IAAI,CAAC;gBAAC;oBAAE,MAAM;oBAAQ,MAAM;gBAAQ;aAAE;YAChD,UAAU,IAAI,CAAC,UAAU,CAAC;gBACtB,IAAI,QACA;gBACJ,IAAI,WAAW,IAAI,IAAI,IAAI,YAAY,IAAI,IAAI,EAAE;oBAC7C,IAAI,CAAC,SAAS,GAAG;oBACjB,IAAI,CAAC,YAAY,CAAC,aAAa;oBAC/B,IAAI,CAAC,WACD;oBACJ,qBAAqB,qBAAqB,GACtC,gBAAgB,UAAU,IAAI;oBAClC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;wBACjB,IAAI,QACA;wBACJ,IAAI,aAAa,IAAI,CAAC,UAAU,EAC5B;wBACJ;wBACA,IAAI,CAAC,YAAY,CAAC;wBAClB,UAAU,IAAI,CAAC;4BAAC;gCAAE,MAAM;4BAAU;yBAAE;wBACpC,IAAI,CAAC,YAAY,CAAC,WAAW;wBAC7B,YAAY;wBACZ,IAAI,CAAC,SAAS,GAAG;wBACjB,IAAI,CAAC,KAAK;oBACd;gBACJ,OACK;oBACD,MAAM,MAAM,IAAI,MAAM;oBACtB,aAAa;oBACb,IAAI,SAAS,GAAG,UAAU,IAAI;oBAC9B,IAAI,CAAC,YAAY,CAAC,gBAAgB;gBACtC;YACJ;QACJ;QACA,SAAS;YACL,IAAI,QACA;YACJ,+DAA+D;YAC/D,SAAS;YACT;YACA,UAAU,KAAK;YACf,YAAY;QAChB;QACA,8CAA8C;QAC9C,MAAM,UAAU,CAAC;YACb,MAAM,QAAQ,IAAI,MAAM,kBAAkB;YAC1C,aAAa;YACb,MAAM,SAAS,GAAG,UAAU,IAAI;YAChC;YACA,IAAI,CAAC,YAAY,CAAC,gBAAgB;QACtC;QACA,SAAS;YACL,QAAQ;QACZ;QACA,gDAAgD;QAChD,SAAS;YACL,QAAQ;QACZ;QACA,kDAAkD;QAClD,SAAS,UAAU,EAAE;YACjB,IAAI,aAAa,GAAG,IAAI,KAAK,UAAU,IAAI,EAAE;gBACzC;YACJ;QACJ;QACA,oDAAoD;QACpD,MAAM,UAAU;YACZ,UAAU,cAAc,CAAC,QAAQ;YACjC,UAAU,cAAc,CAAC,SAAS;YAClC,UAAU,cAAc,CAAC,SAAS;YAClC,IAAI,CAAC,GAAG,CAAC,SAAS;YAClB,IAAI,CAAC,GAAG,CAAC,aAAa;QAC1B;QACA,UAAU,IAAI,CAAC,QAAQ;QACvB,UAAU,IAAI,CAAC,SAAS;QACxB,UAAU,IAAI,CAAC,SAAS;QACxB,IAAI,CAAC,IAAI,CAAC,SAAS;QACnB,IAAI,CAAC,IAAI,CAAC,aAAa;QACvB,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,oBAAoB,CAAC,KAC5C,SAAS,gBAAgB;YACzB,qBAAqB;YACrB,IAAI,CAAC,YAAY,CAAC;gBACd,IAAI,CAAC,QAAQ;oBACT,UAAU,IAAI;gBAClB;YACJ,GAAG;QACP,OACK;YACD,UAAU,IAAI;QAClB;IACJ;IACA,YAAY,IAAI,EAAE;QACd,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,QAAQ;QACnD,KAAK,CAAC,YAAY;IACtB;IACA;;;;;KAKC,GACD,gBAAgB,QAAQ,EAAE;QACtB,MAAM,mBAAmB,EAAE;QAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;YACtC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,GACpC,iBAAiB,IAAI,CAAC,QAAQ,CAAC,EAAE;QACzC;QACA,OAAO;IACX;AACJ;AAoBO,MAAM,eAAe;IACxB,YAAY,GAAG,EAAE,OAAO,CAAC,CAAC,CAAE;QACxB,MAAM,IAAI,OAAO,QAAQ,WAAW,MAAM;QAC1C,IAAI,CAAC,EAAE,UAAU,IACZ,EAAE,UAAU,IAAI,OAAO,EAAE,UAAU,CAAC,EAAE,KAAK,UAAW;YACvD,EAAE,UAAU,GAAG,CAAC,EAAE,UAAU,IAAI;gBAAC;gBAAW;gBAAa;aAAe,EACnE,GAAG,CAAC,CAAC,gBAAkB,gLAAA,CAAA,aAAkB,CAAC,cAAc,EACxD,MAAM,CAAC,CAAC,IAAM,CAAC,CAAC;QACzB;QACA,KAAK,CAAC,KAAK;IACf;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2156, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/engine.io-client/build/esm/transports/polling-fetch.js"], "sourcesContent": ["import { Polling } from \"./polling.js\";\n/**\n * HTTP long-polling based on the built-in `fetch()` method.\n *\n * Usage: browser, Node.js (since v18), <PERSON><PERSON>, <PERSON>un\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/fetch\n * @see https://caniuse.com/fetch\n * @see https://nodejs.org/api/globals.html#fetch\n */\nexport class Fetch extends Polling {\n    doPoll() {\n        this._fetch()\n            .then((res) => {\n            if (!res.ok) {\n                return this.onError(\"fetch read error\", res.status, res);\n            }\n            res.text().then((data) => this.onData(data));\n        })\n            .catch((err) => {\n            this.onError(\"fetch read error\", err);\n        });\n    }\n    doWrite(data, callback) {\n        this._fetch(data)\n            .then((res) => {\n            if (!res.ok) {\n                return this.onError(\"fetch write error\", res.status, res);\n            }\n            callback();\n        })\n            .catch((err) => {\n            this.onError(\"fetch write error\", err);\n        });\n    }\n    _fetch(data) {\n        var _a;\n        const isPost = data !== undefined;\n        const headers = new Headers(this.opts.extraHeaders);\n        if (isPost) {\n            headers.set(\"content-type\", \"text/plain;charset=UTF-8\");\n        }\n        (_a = this.socket._cookieJar) === null || _a === void 0 ? void 0 : _a.appendCookies(headers);\n        return fetch(this.uri(), {\n            method: isPost ? \"POST\" : \"GET\",\n            body: isPost ? data : null,\n            headers,\n            credentials: this.opts.withCredentials ? \"include\" : \"omit\",\n        }).then((res) => {\n            var _a;\n            // @ts-ignore getSetCookie() was added in Node.js v19.7.0\n            (_a = this.socket._cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies(res.headers.getSetCookie());\n            return res;\n        });\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAUO,MAAM,cAAc,kLAAA,CAAA,UAAO;IAC9B,SAAS;QACL,IAAI,CAAC,MAAM,GACN,IAAI,CAAC,CAAC;YACP,IAAI,CAAC,IAAI,EAAE,EAAE;gBACT,OAAO,IAAI,CAAC,OAAO,CAAC,oBAAoB,IAAI,MAAM,EAAE;YACxD;YACA,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,OAAS,IAAI,CAAC,MAAM,CAAC;QAC1C,GACK,KAAK,CAAC,CAAC;YACR,IAAI,CAAC,OAAO,CAAC,oBAAoB;QACrC;IACJ;IACA,QAAQ,IAAI,EAAE,QAAQ,EAAE;QACpB,IAAI,CAAC,MAAM,CAAC,MACP,IAAI,CAAC,CAAC;YACP,IAAI,CAAC,IAAI,EAAE,EAAE;gBACT,OAAO,IAAI,CAAC,OAAO,CAAC,qBAAqB,IAAI,MAAM,EAAE;YACzD;YACA;QACJ,GACK,KAAK,CAAC,CAAC;YACR,IAAI,CAAC,OAAO,CAAC,qBAAqB;QACtC;IACJ;IACA,OAAO,IAAI,EAAE;QACT,IAAI;QACJ,MAAM,SAAS,SAAS;QACxB,MAAM,UAAU,IAAI,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY;QAClD,IAAI,QAAQ;YACR,QAAQ,GAAG,CAAC,gBAAgB;QAChC;QACA,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,UAAU,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,aAAa,CAAC;QACpF,OAAO,MAAM,IAAI,CAAC,GAAG,IAAI;YACrB,QAAQ,SAAS,SAAS;YAC1B,MAAM,SAAS,OAAO;YACtB;YACA,aAAa,IAAI,CAAC,IAAI,CAAC,eAAe,GAAG,YAAY;QACzD,GAAG,IAAI,CAAC,CAAC;YACL,IAAI;YACJ,yDAAyD;YACzD,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,UAAU,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,YAAY,CAAC,IAAI,OAAO,CAAC,YAAY;YAC3G,OAAO;QACX;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2209, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/engine.io-client/build/esm/index.js"], "sourcesContent": ["import { Socket } from \"./socket.js\";\nexport { Socket };\nexport { SocketWithoutUpgrade, SocketWithUpgrade, } from \"./socket.js\";\nexport const protocol = Socket.protocol;\nexport { Transport, TransportError } from \"./transport.js\";\nexport { transports } from \"./transports/index.js\";\nexport { installTimerFunctions } from \"./util.js\";\nexport { parse } from \"./contrib/parseuri.js\";\nexport { nextTick } from \"./globals.node.js\";\nexport { Fetch } from \"./transports/polling-fetch.js\";\nexport { XHR as NodeXHR } from \"./transports/polling-xhr.node.js\";\nexport { XHR } from \"./transports/polling-xhr.js\";\nexport { WS as NodeWebSocket } from \"./transports/websocket.node.js\";\nexport { WS as WebSocket } from \"./transports/websocket.js\";\nexport { WT as WebTransport } from \"./transports/webtransport.js\";\n"], "names": [], "mappings": ";;;AAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;;;;AAXO,MAAM,WAAW,mKAAA,CAAA,SAAM,CAAC,QAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2261, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/socket.io-client/build/esm/url.js"], "sourcesContent": ["import { parse } from \"engine.io-client\";\n/**\n * URL parser.\n *\n * @param uri - url\n * @param path - the request path of the connection\n * @param loc - An object meant to mimic window.location.\n *        Defaults to window.location.\n * @public\n */\nexport function url(uri, path = \"\", loc) {\n    let obj = uri;\n    // default to window.location\n    loc = loc || (typeof location !== \"undefined\" && location);\n    if (null == uri)\n        uri = loc.protocol + \"//\" + loc.host;\n    // relative path support\n    if (typeof uri === \"string\") {\n        if (\"/\" === uri.charAt(0)) {\n            if (\"/\" === uri.charAt(1)) {\n                uri = loc.protocol + uri;\n            }\n            else {\n                uri = loc.host + uri;\n            }\n        }\n        if (!/^(https?|wss?):\\/\\//.test(uri)) {\n            if (\"undefined\" !== typeof loc) {\n                uri = loc.protocol + \"//\" + uri;\n            }\n            else {\n                uri = \"https://\" + uri;\n            }\n        }\n        // parse\n        obj = parse(uri);\n    }\n    // make sure we treat `localhost:80` and `localhost` equally\n    if (!obj.port) {\n        if (/^(http|ws)$/.test(obj.protocol)) {\n            obj.port = \"80\";\n        }\n        else if (/^(http|ws)s$/.test(obj.protocol)) {\n            obj.port = \"443\";\n        }\n    }\n    obj.path = obj.path || \"/\";\n    const ipv6 = obj.host.indexOf(\":\") !== -1;\n    const host = ipv6 ? \"[\" + obj.host + \"]\" : obj.host;\n    // define unique id\n    obj.id = obj.protocol + \"://\" + host + \":\" + obj.port + path;\n    // define href\n    obj.href =\n        obj.protocol +\n            \"://\" +\n            host +\n            (loc && loc.port === obj.port ? \"\" : \":\" + obj.port);\n    return obj;\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAUO,SAAS,IAAI,GAAG,EAAE,OAAO,EAAE,EAAE,GAAG;IACnC,IAAI,MAAM;IACV,6BAA6B;IAC7B,MAAM,OAAQ,OAAO,aAAa,eAAe;IACjD,IAAI,QAAQ,KACR,MAAM,IAAI,QAAQ,GAAG,OAAO,IAAI,IAAI;IACxC,wBAAwB;IACxB,IAAI,OAAO,QAAQ,UAAU;QACzB,IAAI,QAAQ,IAAI,MAAM,CAAC,IAAI;YACvB,IAAI,QAAQ,IAAI,MAAM,CAAC,IAAI;gBACvB,MAAM,IAAI,QAAQ,GAAG;YACzB,OACK;gBACD,MAAM,IAAI,IAAI,GAAG;YACrB;QACJ;QACA,IAAI,CAAC,sBAAsB,IAAI,CAAC,MAAM;YAClC,IAAI,gBAAgB,OAAO,KAAK;gBAC5B,MAAM,IAAI,QAAQ,GAAG,OAAO;YAChC,OACK;gBACD,MAAM,aAAa;YACvB;QACJ;QACA,QAAQ;QACR,MAAM,CAAA,GAAA,gLAAA,CAAA,QAAK,AAAD,EAAE;IAChB;IACA,4DAA4D;IAC5D,IAAI,CAAC,IAAI,IAAI,EAAE;QACX,IAAI,cAAc,IAAI,CAAC,IAAI,QAAQ,GAAG;YAClC,IAAI,IAAI,GAAG;QACf,OACK,IAAI,eAAe,IAAI,CAAC,IAAI,QAAQ,GAAG;YACxC,IAAI,IAAI,GAAG;QACf;IACJ;IACA,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI;IACvB,MAAM,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;IACxC,MAAM,OAAO,OAAO,MAAM,IAAI,IAAI,GAAG,MAAM,IAAI,IAAI;IACnD,mBAAmB;IACnB,IAAI,EAAE,GAAG,IAAI,QAAQ,GAAG,QAAQ,OAAO,MAAM,IAAI,IAAI,GAAG;IACxD,cAAc;IACd,IAAI,IAAI,GACJ,IAAI,QAAQ,GACR,QACA,OACA,CAAC,OAAO,IAAI,IAAI,KAAK,IAAI,IAAI,GAAG,KAAK,MAAM,IAAI,IAAI;IAC3D,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2314, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/socket.io-parser/build/esm/is-binary.js"], "sourcesContent": ["const withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst isView = (obj) => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj.buffer instanceof ArrayBuffer;\n};\nconst toString = Object.prototype.toString;\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeFile = typeof File === \"function\" ||\n    (typeof File !== \"undefined\" &&\n        toString.call(File) === \"[object FileConstructor]\");\n/**\n * Returns true if obj is a Buffer, an ArrayBuffer, a Blob or a File.\n *\n * @private\n */\nexport function isBinary(obj) {\n    return ((withNativeArrayBuffer && (obj instanceof ArrayBuffer || isView(obj))) ||\n        (withNativeBlob && obj instanceof Blob) ||\n        (withNativeFile && obj instanceof File));\n}\nexport function hasBinary(obj, toJSON) {\n    if (!obj || typeof obj !== \"object\") {\n        return false;\n    }\n    if (Array.isArray(obj)) {\n        for (let i = 0, l = obj.length; i < l; i++) {\n            if (hasBinary(obj[i])) {\n                return true;\n            }\n        }\n        return false;\n    }\n    if (isBinary(obj)) {\n        return true;\n    }\n    if (obj.toJSON &&\n        typeof obj.toJSON === \"function\" &&\n        arguments.length === 1) {\n        return hasBinary(obj.toJSON(), true);\n    }\n    for (const key in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, key) && hasBinary(obj[key])) {\n            return true;\n        }\n    }\n    return false;\n}\n"], "names": [], "mappings": ";;;;AAAA,MAAM,wBAAwB,OAAO,gBAAgB;AACrD,MAAM,SAAS,CAAC;IACZ,OAAO,OAAO,YAAY,MAAM,KAAK,aAC/B,YAAY,MAAM,CAAC,OACnB,IAAI,MAAM,YAAY;AAChC;AACA,MAAM,WAAW,OAAO,SAAS,CAAC,QAAQ;AAC1C,MAAM,iBAAiB,OAAO,SAAS,cAClC,OAAO,SAAS,eACb,SAAS,IAAI,CAAC,UAAU;AAChC,MAAM,iBAAiB,OAAO,SAAS,cAClC,OAAO,SAAS,eACb,SAAS,IAAI,CAAC,UAAU;AAMzB,SAAS,SAAS,GAAG;IACxB,OAAQ,AAAC,yBAAyB,CAAC,eAAe,eAAe,OAAO,IAAI,KACvE,kBAAkB,eAAe,QACjC,kBAAkB,eAAe;AAC1C;AACO,SAAS,UAAU,GAAG,EAAE,MAAM;IACjC,IAAI,CAAC,OAAO,OAAO,QAAQ,UAAU;QACjC,OAAO;IACX;IACA,IAAI,MAAM,OAAO,CAAC,MAAM;QACpB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAI,GAAG,IAAK;YACxC,IAAI,UAAU,GAAG,CAAC,EAAE,GAAG;gBACnB,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA,IAAI,SAAS,MAAM;QACf,OAAO;IACX;IACA,IAAI,IAAI,MAAM,IACV,OAAO,IAAI,MAAM,KAAK,cACtB,UAAU,MAAM,KAAK,GAAG;QACxB,OAAO,UAAU,IAAI,MAAM,IAAI;IACnC;IACA,IAAK,MAAM,OAAO,IAAK;QACnB,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,QAAQ,UAAU,GAAG,CAAC,IAAI,GAAG;YACvE,OAAO;QACX;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2359, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/socket.io-parser/build/esm/binary.js"], "sourcesContent": ["import { isBinary } from \"./is-binary.js\";\n/**\n * Replaces every Buffer | ArrayBuffer | Blob | File in packet with a numbered placeholder.\n *\n * @param {Object} packet - socket.io event packet\n * @return {Object} with deconstructed packet and list of buffers\n * @public\n */\nexport function deconstructPacket(packet) {\n    const buffers = [];\n    const packetData = packet.data;\n    const pack = packet;\n    pack.data = _deconstructPacket(packetData, buffers);\n    pack.attachments = buffers.length; // number of binary 'attachments'\n    return { packet: pack, buffers: buffers };\n}\nfunction _deconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (isBinary(data)) {\n        const placeholder = { _placeholder: true, num: buffers.length };\n        buffers.push(data);\n        return placeholder;\n    }\n    else if (Array.isArray(data)) {\n        const newData = new Array(data.length);\n        for (let i = 0; i < data.length; i++) {\n            newData[i] = _deconstructPacket(data[i], buffers);\n        }\n        return newData;\n    }\n    else if (typeof data === \"object\" && !(data instanceof Date)) {\n        const newData = {};\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                newData[key] = _deconstructPacket(data[key], buffers);\n            }\n        }\n        return newData;\n    }\n    return data;\n}\n/**\n * Reconstructs a binary packet from its placeholder packet and buffers\n *\n * @param {Object} packet - event packet with placeholders\n * @param {Array} buffers - binary buffers to put in placeholder positions\n * @return {Object} reconstructed packet\n * @public\n */\nexport function reconstructPacket(packet, buffers) {\n    packet.data = _reconstructPacket(packet.data, buffers);\n    delete packet.attachments; // no longer useful\n    return packet;\n}\nfunction _reconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (data && data._placeholder === true) {\n        const isIndexValid = typeof data.num === \"number\" &&\n            data.num >= 0 &&\n            data.num < buffers.length;\n        if (isIndexValid) {\n            return buffers[data.num]; // appropriate buffer (should be natural order anyway)\n        }\n        else {\n            throw new Error(\"illegal attachments\");\n        }\n    }\n    else if (Array.isArray(data)) {\n        for (let i = 0; i < data.length; i++) {\n            data[i] = _reconstructPacket(data[i], buffers);\n        }\n    }\n    else if (typeof data === \"object\") {\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                data[key] = _reconstructPacket(data[key], buffers);\n            }\n        }\n    }\n    return data;\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAQO,SAAS,kBAAkB,MAAM;IACpC,MAAM,UAAU,EAAE;IAClB,MAAM,aAAa,OAAO,IAAI;IAC9B,MAAM,OAAO;IACb,KAAK,IAAI,GAAG,mBAAmB,YAAY;IAC3C,KAAK,WAAW,GAAG,QAAQ,MAAM,EAAE,iCAAiC;IACpE,OAAO;QAAE,QAAQ;QAAM,SAAS;IAAQ;AAC5C;AACA,SAAS,mBAAmB,IAAI,EAAE,OAAO;IACrC,IAAI,CAAC,MACD,OAAO;IACX,IAAI,CAAA,GAAA,yKAAA,CAAA,WAAQ,AAAD,EAAE,OAAO;QAChB,MAAM,cAAc;YAAE,cAAc;YAAM,KAAK,QAAQ,MAAM;QAAC;QAC9D,QAAQ,IAAI,CAAC;QACb,OAAO;IACX,OACK,IAAI,MAAM,OAAO,CAAC,OAAO;QAC1B,MAAM,UAAU,IAAI,MAAM,KAAK,MAAM;QACrC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YAClC,OAAO,CAAC,EAAE,GAAG,mBAAmB,IAAI,CAAC,EAAE,EAAE;QAC7C;QACA,OAAO;IACX,OACK,IAAI,OAAO,SAAS,YAAY,CAAC,CAAC,gBAAgB,IAAI,GAAG;QAC1D,MAAM,UAAU,CAAC;QACjB,IAAK,MAAM,OAAO,KAAM;YACpB,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,MAAM;gBACjD,OAAO,CAAC,IAAI,GAAG,mBAAmB,IAAI,CAAC,IAAI,EAAE;YACjD;QACJ;QACA,OAAO;IACX;IACA,OAAO;AACX;AASO,SAAS,kBAAkB,MAAM,EAAE,OAAO;IAC7C,OAAO,IAAI,GAAG,mBAAmB,OAAO,IAAI,EAAE;IAC9C,OAAO,OAAO,WAAW,EAAE,mBAAmB;IAC9C,OAAO;AACX;AACA,SAAS,mBAAmB,IAAI,EAAE,OAAO;IACrC,IAAI,CAAC,MACD,OAAO;IACX,IAAI,QAAQ,KAAK,YAAY,KAAK,MAAM;QACpC,MAAM,eAAe,OAAO,KAAK,GAAG,KAAK,YACrC,KAAK,GAAG,IAAI,KACZ,KAAK,GAAG,GAAG,QAAQ,MAAM;QAC7B,IAAI,cAAc;YACd,OAAO,OAAO,CAAC,KAAK,GAAG,CAAC,EAAE,sDAAsD;QACpF,OACK;YACD,MAAM,IAAI,MAAM;QACpB;IACJ,OACK,IAAI,MAAM,OAAO,CAAC,OAAO;QAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YAClC,IAAI,CAAC,EAAE,GAAG,mBAAmB,IAAI,CAAC,EAAE,EAAE;QAC1C;IACJ,OACK,IAAI,OAAO,SAAS,UAAU;QAC/B,IAAK,MAAM,OAAO,KAAM;YACpB,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,MAAM;gBACjD,IAAI,CAAC,IAAI,GAAG,mBAAmB,IAAI,CAAC,IAAI,EAAE;YAC9C;QACJ;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2435, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/socket.io-parser/build/esm/index.js"], "sourcesContent": ["import { Emitter } from \"@socket.io/component-emitter\";\nimport { deconstructPacket, reconstructPacket } from \"./binary.js\";\nimport { isBinary, hasBinary } from \"./is-binary.js\";\n/**\n * These strings must not be used as event names, as they have a special meaning.\n */\nconst RESERVED_EVENTS = [\n    \"connect\",\n    \"connect_error\",\n    \"disconnect\",\n    \"disconnecting\",\n    \"newListener\",\n    \"removeListener\", // used by the Node.js EventEmitter\n];\n/**\n * Protocol version.\n *\n * @public\n */\nexport const protocol = 5;\nexport var PacketType;\n(function (PacketType) {\n    PacketType[PacketType[\"CONNECT\"] = 0] = \"CONNECT\";\n    PacketType[PacketType[\"DISCONNECT\"] = 1] = \"DISCONNECT\";\n    PacketType[PacketType[\"EVENT\"] = 2] = \"EVENT\";\n    PacketType[PacketType[\"ACK\"] = 3] = \"ACK\";\n    PacketType[PacketType[\"CONNECT_ERROR\"] = 4] = \"CONNECT_ERROR\";\n    PacketType[PacketType[\"BINARY_EVENT\"] = 5] = \"BINARY_EVENT\";\n    PacketType[PacketType[\"BINARY_ACK\"] = 6] = \"BINARY_ACK\";\n})(PacketType || (PacketType = {}));\n/**\n * A socket.io Encoder instance\n */\nexport class Encoder {\n    /**\n     * Encoder constructor\n     *\n     * @param {function} replacer - custom replacer to pass down to JSON.parse\n     */\n    constructor(replacer) {\n        this.replacer = replacer;\n    }\n    /**\n     * Encode a packet as a single string if non-binary, or as a\n     * buffer sequence, depending on packet type.\n     *\n     * @param {Object} obj - packet object\n     */\n    encode(obj) {\n        if (obj.type === PacketType.EVENT || obj.type === PacketType.ACK) {\n            if (hasBinary(obj)) {\n                return this.encodeAsBinary({\n                    type: obj.type === PacketType.EVENT\n                        ? PacketType.BINARY_EVENT\n                        : PacketType.BINARY_ACK,\n                    nsp: obj.nsp,\n                    data: obj.data,\n                    id: obj.id,\n                });\n            }\n        }\n        return [this.encodeAsString(obj)];\n    }\n    /**\n     * Encode packet as string.\n     */\n    encodeAsString(obj) {\n        // first is type\n        let str = \"\" + obj.type;\n        // attachments if we have them\n        if (obj.type === PacketType.BINARY_EVENT ||\n            obj.type === PacketType.BINARY_ACK) {\n            str += obj.attachments + \"-\";\n        }\n        // if we have a namespace other than `/`\n        // we append it followed by a comma `,`\n        if (obj.nsp && \"/\" !== obj.nsp) {\n            str += obj.nsp + \",\";\n        }\n        // immediately followed by the id\n        if (null != obj.id) {\n            str += obj.id;\n        }\n        // json data\n        if (null != obj.data) {\n            str += JSON.stringify(obj.data, this.replacer);\n        }\n        return str;\n    }\n    /**\n     * Encode packet as 'buffer sequence' by removing blobs, and\n     * deconstructing packet into object with placeholders and\n     * a list of buffers.\n     */\n    encodeAsBinary(obj) {\n        const deconstruction = deconstructPacket(obj);\n        const pack = this.encodeAsString(deconstruction.packet);\n        const buffers = deconstruction.buffers;\n        buffers.unshift(pack); // add packet info to beginning of data list\n        return buffers; // write all the buffers\n    }\n}\n// see https://stackoverflow.com/questions/8511281/check-if-a-value-is-an-object-in-javascript\nfunction isObject(value) {\n    return Object.prototype.toString.call(value) === \"[object Object]\";\n}\n/**\n * A socket.io Decoder instance\n *\n * @return {Object} decoder\n */\nexport class Decoder extends Emitter {\n    /**\n     * Decoder constructor\n     *\n     * @param {function} reviver - custom reviver to pass down to JSON.stringify\n     */\n    constructor(reviver) {\n        super();\n        this.reviver = reviver;\n    }\n    /**\n     * Decodes an encoded packet string into packet JSON.\n     *\n     * @param {String} obj - encoded packet\n     */\n    add(obj) {\n        let packet;\n        if (typeof obj === \"string\") {\n            if (this.reconstructor) {\n                throw new Error(\"got plaintext data when reconstructing a packet\");\n            }\n            packet = this.decodeString(obj);\n            const isBinaryEvent = packet.type === PacketType.BINARY_EVENT;\n            if (isBinaryEvent || packet.type === PacketType.BINARY_ACK) {\n                packet.type = isBinaryEvent ? PacketType.EVENT : PacketType.ACK;\n                // binary packet's json\n                this.reconstructor = new BinaryReconstructor(packet);\n                // no attachments, labeled binary but no binary data to follow\n                if (packet.attachments === 0) {\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n            else {\n                // non-binary full packet\n                super.emitReserved(\"decoded\", packet);\n            }\n        }\n        else if (isBinary(obj) || obj.base64) {\n            // raw binary data\n            if (!this.reconstructor) {\n                throw new Error(\"got binary data when not reconstructing a packet\");\n            }\n            else {\n                packet = this.reconstructor.takeBinaryData(obj);\n                if (packet) {\n                    // received final buffer\n                    this.reconstructor = null;\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n        }\n        else {\n            throw new Error(\"Unknown type: \" + obj);\n        }\n    }\n    /**\n     * Decode a packet String (JSON data)\n     *\n     * @param {String} str\n     * @return {Object} packet\n     */\n    decodeString(str) {\n        let i = 0;\n        // look up type\n        const p = {\n            type: Number(str.charAt(0)),\n        };\n        if (PacketType[p.type] === undefined) {\n            throw new Error(\"unknown packet type \" + p.type);\n        }\n        // look up attachments if type binary\n        if (p.type === PacketType.BINARY_EVENT ||\n            p.type === PacketType.BINARY_ACK) {\n            const start = i + 1;\n            while (str.charAt(++i) !== \"-\" && i != str.length) { }\n            const buf = str.substring(start, i);\n            if (buf != Number(buf) || str.charAt(i) !== \"-\") {\n                throw new Error(\"Illegal attachments\");\n            }\n            p.attachments = Number(buf);\n        }\n        // look up namespace (if any)\n        if (\"/\" === str.charAt(i + 1)) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (\",\" === c)\n                    break;\n                if (i === str.length)\n                    break;\n            }\n            p.nsp = str.substring(start, i);\n        }\n        else {\n            p.nsp = \"/\";\n        }\n        // look up id\n        const next = str.charAt(i + 1);\n        if (\"\" !== next && Number(next) == next) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (null == c || Number(c) != c) {\n                    --i;\n                    break;\n                }\n                if (i === str.length)\n                    break;\n            }\n            p.id = Number(str.substring(start, i + 1));\n        }\n        // look up json data\n        if (str.charAt(++i)) {\n            const payload = this.tryParse(str.substr(i));\n            if (Decoder.isPayloadValid(p.type, payload)) {\n                p.data = payload;\n            }\n            else {\n                throw new Error(\"invalid payload\");\n            }\n        }\n        return p;\n    }\n    tryParse(str) {\n        try {\n            return JSON.parse(str, this.reviver);\n        }\n        catch (e) {\n            return false;\n        }\n    }\n    static isPayloadValid(type, payload) {\n        switch (type) {\n            case PacketType.CONNECT:\n                return isObject(payload);\n            case PacketType.DISCONNECT:\n                return payload === undefined;\n            case PacketType.CONNECT_ERROR:\n                return typeof payload === \"string\" || isObject(payload);\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                return (Array.isArray(payload) &&\n                    (typeof payload[0] === \"number\" ||\n                        (typeof payload[0] === \"string\" &&\n                            RESERVED_EVENTS.indexOf(payload[0]) === -1)));\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                return Array.isArray(payload);\n        }\n    }\n    /**\n     * Deallocates a parser's resources\n     */\n    destroy() {\n        if (this.reconstructor) {\n            this.reconstructor.finishedReconstruction();\n            this.reconstructor = null;\n        }\n    }\n}\n/**\n * A manager of a binary event's 'buffer sequence'. Should\n * be constructed whenever a packet of type BINARY_EVENT is\n * decoded.\n *\n * @param {Object} packet\n * @return {BinaryReconstructor} initialized reconstructor\n */\nclass BinaryReconstructor {\n    constructor(packet) {\n        this.packet = packet;\n        this.buffers = [];\n        this.reconPack = packet;\n    }\n    /**\n     * Method to be called when binary data received from connection\n     * after a BINARY_EVENT packet.\n     *\n     * @param {Buffer | ArrayBuffer} binData - the raw binary data received\n     * @return {null | Object} returns null if more binary data is expected or\n     *   a reconstructed packet object if all buffers have been received.\n     */\n    takeBinaryData(binData) {\n        this.buffers.push(binData);\n        if (this.buffers.length === this.reconPack.attachments) {\n            // done with buffer list\n            const packet = reconstructPacket(this.reconPack, this.buffers);\n            this.finishedReconstruction();\n            return packet;\n        }\n        return null;\n    }\n    /**\n     * Cleans up binary packet reconstruction variables.\n     */\n    finishedReconstruction() {\n        this.reconPack = null;\n        this.buffers = [];\n    }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;AACA;;CAEC,GACD,MAAM,kBAAkB;IACpB;IACA;IACA;IACA;IACA;IACA;CACH;AAMM,MAAM,WAAW;AACjB,IAAI;AACX,CAAC,SAAU,UAAU;IACjB,UAAU,CAAC,UAAU,CAAC,UAAU,GAAG,EAAE,GAAG;IACxC,UAAU,CAAC,UAAU,CAAC,aAAa,GAAG,EAAE,GAAG;IAC3C,UAAU,CAAC,UAAU,CAAC,QAAQ,GAAG,EAAE,GAAG;IACtC,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,EAAE,GAAG;IACpC,UAAU,CAAC,UAAU,CAAC,gBAAgB,GAAG,EAAE,GAAG;IAC9C,UAAU,CAAC,UAAU,CAAC,eAAe,GAAG,EAAE,GAAG;IAC7C,UAAU,CAAC,UAAU,CAAC,aAAa,GAAG,EAAE,GAAG;AAC/C,CAAC,EAAE,cAAc,CAAC,aAAa,CAAC,CAAC;AAI1B,MAAM;IACT;;;;KAIC,GACD,YAAY,QAAQ,CAAE;QAClB,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA;;;;;KAKC,GACD,OAAO,GAAG,EAAE;QACR,IAAI,IAAI,IAAI,KAAK,WAAW,KAAK,IAAI,IAAI,IAAI,KAAK,WAAW,GAAG,EAAE;YAC9D,IAAI,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE,MAAM;gBAChB,OAAO,IAAI,CAAC,cAAc,CAAC;oBACvB,MAAM,IAAI,IAAI,KAAK,WAAW,KAAK,GAC7B,WAAW,YAAY,GACvB,WAAW,UAAU;oBAC3B,KAAK,IAAI,GAAG;oBACZ,MAAM,IAAI,IAAI;oBACd,IAAI,IAAI,EAAE;gBACd;YACJ;QACJ;QACA,OAAO;YAAC,IAAI,CAAC,cAAc,CAAC;SAAK;IACrC;IACA;;KAEC,GACD,eAAe,GAAG,EAAE;QAChB,gBAAgB;QAChB,IAAI,MAAM,KAAK,IAAI,IAAI;QACvB,8BAA8B;QAC9B,IAAI,IAAI,IAAI,KAAK,WAAW,YAAY,IACpC,IAAI,IAAI,KAAK,WAAW,UAAU,EAAE;YACpC,OAAO,IAAI,WAAW,GAAG;QAC7B;QACA,wCAAwC;QACxC,uCAAuC;QACvC,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAI,GAAG,EAAE;YAC5B,OAAO,IAAI,GAAG,GAAG;QACrB;QACA,iCAAiC;QACjC,IAAI,QAAQ,IAAI,EAAE,EAAE;YAChB,OAAO,IAAI,EAAE;QACjB;QACA,YAAY;QACZ,IAAI,QAAQ,IAAI,IAAI,EAAE;YAClB,OAAO,KAAK,SAAS,CAAC,IAAI,IAAI,EAAE,IAAI,CAAC,QAAQ;QACjD;QACA,OAAO;IACX;IACA;;;;KAIC,GACD,eAAe,GAAG,EAAE;QAChB,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,oBAAiB,AAAD,EAAE;QACzC,MAAM,OAAO,IAAI,CAAC,cAAc,CAAC,eAAe,MAAM;QACtD,MAAM,UAAU,eAAe,OAAO;QACtC,QAAQ,OAAO,CAAC,OAAO,4CAA4C;QACnE,OAAO,SAAS,wBAAwB;IAC5C;AACJ;AACA,8FAA8F;AAC9F,SAAS,SAAS,KAAK;IACnB,OAAO,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW;AACrD;AAMO,MAAM,gBAAgB,gLAAA,CAAA,UAAO;IAChC;;;;KAIC,GACD,YAAY,OAAO,CAAE;QACjB,KAAK;QACL,IAAI,CAAC,OAAO,GAAG;IACnB;IACA;;;;KAIC,GACD,IAAI,GAAG,EAAE;QACL,IAAI;QACJ,IAAI,OAAO,QAAQ,UAAU;YACzB,IAAI,IAAI,CAAC,aAAa,EAAE;gBACpB,MAAM,IAAI,MAAM;YACpB;YACA,SAAS,IAAI,CAAC,YAAY,CAAC;YAC3B,MAAM,gBAAgB,OAAO,IAAI,KAAK,WAAW,YAAY;YAC7D,IAAI,iBAAiB,OAAO,IAAI,KAAK,WAAW,UAAU,EAAE;gBACxD,OAAO,IAAI,GAAG,gBAAgB,WAAW,KAAK,GAAG,WAAW,GAAG;gBAC/D,uBAAuB;gBACvB,IAAI,CAAC,aAAa,GAAG,IAAI,oBAAoB;gBAC7C,8DAA8D;gBAC9D,IAAI,OAAO,WAAW,KAAK,GAAG;oBAC1B,KAAK,CAAC,aAAa,WAAW;gBAClC;YACJ,OACK;gBACD,yBAAyB;gBACzB,KAAK,CAAC,aAAa,WAAW;YAClC;QACJ,OACK,IAAI,CAAA,GAAA,yKAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,IAAI,MAAM,EAAE;YAClC,kBAAkB;YAClB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;gBACrB,MAAM,IAAI,MAAM;YACpB,OACK;gBACD,SAAS,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC;gBAC3C,IAAI,QAAQ;oBACR,wBAAwB;oBACxB,IAAI,CAAC,aAAa,GAAG;oBACrB,KAAK,CAAC,aAAa,WAAW;gBAClC;YACJ;QACJ,OACK;YACD,MAAM,IAAI,MAAM,mBAAmB;QACvC;IACJ;IACA;;;;;KAKC,GACD,aAAa,GAAG,EAAE;QACd,IAAI,IAAI;QACR,eAAe;QACf,MAAM,IAAI;YACN,MAAM,OAAO,IAAI,MAAM,CAAC;QAC5B;QACA,IAAI,UAAU,CAAC,EAAE,IAAI,CAAC,KAAK,WAAW;YAClC,MAAM,IAAI,MAAM,yBAAyB,EAAE,IAAI;QACnD;QACA,qCAAqC;QACrC,IAAI,EAAE,IAAI,KAAK,WAAW,YAAY,IAClC,EAAE,IAAI,KAAK,WAAW,UAAU,EAAE;YAClC,MAAM,QAAQ,IAAI;YAClB,MAAO,IAAI,MAAM,CAAC,EAAE,OAAO,OAAO,KAAK,IAAI,MAAM,CAAE,CAAE;YACrD,MAAM,MAAM,IAAI,SAAS,CAAC,OAAO;YACjC,IAAI,OAAO,OAAO,QAAQ,IAAI,MAAM,CAAC,OAAO,KAAK;gBAC7C,MAAM,IAAI,MAAM;YACpB;YACA,EAAE,WAAW,GAAG,OAAO;QAC3B;QACA,6BAA6B;QAC7B,IAAI,QAAQ,IAAI,MAAM,CAAC,IAAI,IAAI;YAC3B,MAAM,QAAQ,IAAI;YAClB,MAAO,EAAE,EAAG;gBACR,MAAM,IAAI,IAAI,MAAM,CAAC;gBACrB,IAAI,QAAQ,GACR;gBACJ,IAAI,MAAM,IAAI,MAAM,EAChB;YACR;YACA,EAAE,GAAG,GAAG,IAAI,SAAS,CAAC,OAAO;QACjC,OACK;YACD,EAAE,GAAG,GAAG;QACZ;QACA,aAAa;QACb,MAAM,OAAO,IAAI,MAAM,CAAC,IAAI;QAC5B,IAAI,OAAO,QAAQ,OAAO,SAAS,MAAM;YACrC,MAAM,QAAQ,IAAI;YAClB,MAAO,EAAE,EAAG;gBACR,MAAM,IAAI,IAAI,MAAM,CAAC;gBACrB,IAAI,QAAQ,KAAK,OAAO,MAAM,GAAG;oBAC7B,EAAE;oBACF;gBACJ;gBACA,IAAI,MAAM,IAAI,MAAM,EAChB;YACR;YACA,EAAE,EAAE,GAAG,OAAO,IAAI,SAAS,CAAC,OAAO,IAAI;QAC3C;QACA,oBAAoB;QACpB,IAAI,IAAI,MAAM,CAAC,EAAE,IAAI;YACjB,MAAM,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,MAAM,CAAC;YACzC,IAAI,QAAQ,cAAc,CAAC,EAAE,IAAI,EAAE,UAAU;gBACzC,EAAE,IAAI,GAAG;YACb,OACK;gBACD,MAAM,IAAI,MAAM;YACpB;QACJ;QACA,OAAO;IACX;IACA,SAAS,GAAG,EAAE;QACV,IAAI;YACA,OAAO,KAAK,KAAK,CAAC,KAAK,IAAI,CAAC,OAAO;QACvC,EACA,OAAO,GAAG;YACN,OAAO;QACX;IACJ;IACA,OAAO,eAAe,IAAI,EAAE,OAAO,EAAE;QACjC,OAAQ;YACJ,KAAK,WAAW,OAAO;gBACnB,OAAO,SAAS;YACpB,KAAK,WAAW,UAAU;gBACtB,OAAO,YAAY;YACvB,KAAK,WAAW,aAAa;gBACzB,OAAO,OAAO,YAAY,YAAY,SAAS;YACnD,KAAK,WAAW,KAAK;YACrB,KAAK,WAAW,YAAY;gBACxB,OAAQ,MAAM,OAAO,CAAC,YAClB,CAAC,OAAO,OAAO,CAAC,EAAE,KAAK,YAClB,OAAO,OAAO,CAAC,EAAE,KAAK,YACnB,gBAAgB,OAAO,CAAC,OAAO,CAAC,EAAE,MAAM,CAAC,CAAE;YAC3D,KAAK,WAAW,GAAG;YACnB,KAAK,WAAW,UAAU;gBACtB,OAAO,MAAM,OAAO,CAAC;QAC7B;IACJ;IACA;;KAEC,GACD,UAAU;QACN,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,sBAAsB;YACzC,IAAI,CAAC,aAAa,GAAG;QACzB;IACJ;AACJ;AACA;;;;;;;CAOC,GACD,MAAM;IACF,YAAY,MAAM,CAAE;QAChB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,GAAG,EAAE;QACjB,IAAI,CAAC,SAAS,GAAG;IACrB;IACA;;;;;;;KAOC,GACD,eAAe,OAAO,EAAE;QACpB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QAClB,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;YACpD,wBAAwB;YACxB,MAAM,SAAS,CAAA,GAAA,mKAAA,CAAA,oBAAiB,AAAD,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO;YAC7D,IAAI,CAAC,sBAAsB;YAC3B,OAAO;QACX;QACA,OAAO;IACX;IACA;;KAEC,GACD,yBAAyB;QACrB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,OAAO,GAAG,EAAE;IACrB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2721, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/socket.io-client/build/esm/on.js"], "sourcesContent": ["export function on(obj, ev, fn) {\n    obj.on(ev, fn);\n    return function subDestroy() {\n        obj.off(ev, fn);\n    };\n}\n"], "names": [], "mappings": ";;;AAAO,SAAS,GAAG,GAAG,EAAE,EAAE,EAAE,EAAE;IAC1B,IAAI,EAAE,CAAC,IAAI;IACX,OAAO,SAAS;QACZ,IAAI,GAAG,CAAC,IAAI;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2736, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/socket.io-client/build/esm/socket.js"], "sourcesContent": ["import { PacketType } from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\n/**\n * Internal events.\n * These events can't be emitted by the user.\n */\nconst RESERVED_EVENTS = Object.freeze({\n    connect: 1,\n    connect_error: 1,\n    disconnect: 1,\n    disconnecting: 1,\n    // EventEmitter reserved events: https://nodejs.org/api/events.html#events_event_newlistener\n    newListener: 1,\n    removeListener: 1,\n});\n/**\n * A Socket is the fundamental class for interacting with the server.\n *\n * A Socket belongs to a certain Namespace (by default /) and uses an underlying {@link Manager} to communicate.\n *\n * @example\n * const socket = io();\n *\n * socket.on(\"connect\", () => {\n *   console.log(\"connected\");\n * });\n *\n * // send an event to the server\n * socket.emit(\"foo\", \"bar\");\n *\n * socket.on(\"foobar\", () => {\n *   // an event was received from the server\n * });\n *\n * // upon disconnection\n * socket.on(\"disconnect\", (reason) => {\n *   console.log(`disconnected due to ${reason}`);\n * });\n */\nexport class Socket extends Emitter {\n    /**\n     * `Socket` constructor.\n     */\n    constructor(io, nsp, opts) {\n        super();\n        /**\n         * Whether the socket is currently connected to the server.\n         *\n         * @example\n         * const socket = io();\n         *\n         * socket.on(\"connect\", () => {\n         *   console.log(socket.connected); // true\n         * });\n         *\n         * socket.on(\"disconnect\", () => {\n         *   console.log(socket.connected); // false\n         * });\n         */\n        this.connected = false;\n        /**\n         * Whether the connection state was recovered after a temporary disconnection. In that case, any missed packets will\n         * be transmitted by the server.\n         */\n        this.recovered = false;\n        /**\n         * Buffer for packets received before the CONNECT packet\n         */\n        this.receiveBuffer = [];\n        /**\n         * Buffer for packets that will be sent once the socket is connected\n         */\n        this.sendBuffer = [];\n        /**\n         * The queue of packets to be sent with retry in case of failure.\n         *\n         * Packets are sent one by one, each waiting for the server acknowledgement, in order to guarantee the delivery order.\n         * @private\n         */\n        this._queue = [];\n        /**\n         * A sequence to generate the ID of the {@link QueuedPacket}.\n         * @private\n         */\n        this._queueSeq = 0;\n        this.ids = 0;\n        /**\n         * A map containing acknowledgement handlers.\n         *\n         * The `withError` attribute is used to differentiate handlers that accept an error as first argument:\n         *\n         * - `socket.emit(\"test\", (err, value) => { ... })` with `ackTimeout` option\n         * - `socket.timeout(5000).emit(\"test\", (err, value) => { ... })`\n         * - `const value = await socket.emitWithAck(\"test\")`\n         *\n         * From those that don't:\n         *\n         * - `socket.emit(\"test\", (value) => { ... });`\n         *\n         * In the first case, the handlers will be called with an error when:\n         *\n         * - the timeout is reached\n         * - the socket gets disconnected\n         *\n         * In the second case, the handlers will be simply discarded upon disconnection, since the client will never receive\n         * an acknowledgement from the server.\n         *\n         * @private\n         */\n        this.acks = {};\n        this.flags = {};\n        this.io = io;\n        this.nsp = nsp;\n        if (opts && opts.auth) {\n            this.auth = opts.auth;\n        }\n        this._opts = Object.assign({}, opts);\n        if (this.io._autoConnect)\n            this.open();\n    }\n    /**\n     * Whether the socket is currently disconnected\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"connect\", () => {\n     *   console.log(socket.disconnected); // false\n     * });\n     *\n     * socket.on(\"disconnect\", () => {\n     *   console.log(socket.disconnected); // true\n     * });\n     */\n    get disconnected() {\n        return !this.connected;\n    }\n    /**\n     * Subscribe to open, close and packet events\n     *\n     * @private\n     */\n    subEvents() {\n        if (this.subs)\n            return;\n        const io = this.io;\n        this.subs = [\n            on(io, \"open\", this.onopen.bind(this)),\n            on(io, \"packet\", this.onpacket.bind(this)),\n            on(io, \"error\", this.onerror.bind(this)),\n            on(io, \"close\", this.onclose.bind(this)),\n        ];\n    }\n    /**\n     * Whether the Socket will try to reconnect when its Manager connects or reconnects.\n     *\n     * @example\n     * const socket = io();\n     *\n     * console.log(socket.active); // true\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   if (reason === \"io server disconnect\") {\n     *     // the disconnection was initiated by the server, you need to manually reconnect\n     *     console.log(socket.active); // false\n     *   }\n     *   // else the socket will automatically try to reconnect\n     *   console.log(socket.active); // true\n     * });\n     */\n    get active() {\n        return !!this.subs;\n    }\n    /**\n     * \"Opens\" the socket.\n     *\n     * @example\n     * const socket = io({\n     *   autoConnect: false\n     * });\n     *\n     * socket.connect();\n     */\n    connect() {\n        if (this.connected)\n            return this;\n        this.subEvents();\n        if (!this.io[\"_reconnecting\"])\n            this.io.open(); // ensure open\n        if (\"open\" === this.io._readyState)\n            this.onopen();\n        return this;\n    }\n    /**\n     * Alias for {@link connect()}.\n     */\n    open() {\n        return this.connect();\n    }\n    /**\n     * Sends a `message` event.\n     *\n     * This method mimics the WebSocket.send() method.\n     *\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/send\n     *\n     * @example\n     * socket.send(\"hello\");\n     *\n     * // this is equivalent to\n     * socket.emit(\"message\", \"hello\");\n     *\n     * @return self\n     */\n    send(...args) {\n        args.unshift(\"message\");\n        this.emit.apply(this, args);\n        return this;\n    }\n    /**\n     * Override `emit`.\n     * If the event is in `events`, it's emitted normally.\n     *\n     * @example\n     * socket.emit(\"hello\", \"world\");\n     *\n     * // all serializable datastructures are supported (no need to call JSON.stringify)\n     * socket.emit(\"hello\", 1, \"2\", { 3: [\"4\"], 5: Uint8Array.from([6]) });\n     *\n     * // with an acknowledgement from the server\n     * socket.emit(\"hello\", \"world\", (val) => {\n     *   // ...\n     * });\n     *\n     * @return self\n     */\n    emit(ev, ...args) {\n        var _a, _b, _c;\n        if (RESERVED_EVENTS.hasOwnProperty(ev)) {\n            throw new Error('\"' + ev.toString() + '\" is a reserved event name');\n        }\n        args.unshift(ev);\n        if (this._opts.retries && !this.flags.fromQueue && !this.flags.volatile) {\n            this._addToQueue(args);\n            return this;\n        }\n        const packet = {\n            type: PacketType.EVENT,\n            data: args,\n        };\n        packet.options = {};\n        packet.options.compress = this.flags.compress !== false;\n        // event ack callback\n        if (\"function\" === typeof args[args.length - 1]) {\n            const id = this.ids++;\n            const ack = args.pop();\n            this._registerAckCallback(id, ack);\n            packet.id = id;\n        }\n        const isTransportWritable = (_b = (_a = this.io.engine) === null || _a === void 0 ? void 0 : _a.transport) === null || _b === void 0 ? void 0 : _b.writable;\n        const isConnected = this.connected && !((_c = this.io.engine) === null || _c === void 0 ? void 0 : _c._hasPingExpired());\n        const discardPacket = this.flags.volatile && !isTransportWritable;\n        if (discardPacket) {\n        }\n        else if (isConnected) {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        }\n        else {\n            this.sendBuffer.push(packet);\n        }\n        this.flags = {};\n        return this;\n    }\n    /**\n     * @private\n     */\n    _registerAckCallback(id, ack) {\n        var _a;\n        const timeout = (_a = this.flags.timeout) !== null && _a !== void 0 ? _a : this._opts.ackTimeout;\n        if (timeout === undefined) {\n            this.acks[id] = ack;\n            return;\n        }\n        // @ts-ignore\n        const timer = this.io.setTimeoutFn(() => {\n            delete this.acks[id];\n            for (let i = 0; i < this.sendBuffer.length; i++) {\n                if (this.sendBuffer[i].id === id) {\n                    this.sendBuffer.splice(i, 1);\n                }\n            }\n            ack.call(this, new Error(\"operation has timed out\"));\n        }, timeout);\n        const fn = (...args) => {\n            // @ts-ignore\n            this.io.clearTimeoutFn(timer);\n            ack.apply(this, args);\n        };\n        fn.withError = true;\n        this.acks[id] = fn;\n    }\n    /**\n     * Emits an event and waits for an acknowledgement\n     *\n     * @example\n     * // without timeout\n     * const response = await socket.emitWithAck(\"hello\", \"world\");\n     *\n     * // with a specific timeout\n     * try {\n     *   const response = await socket.timeout(1000).emitWithAck(\"hello\", \"world\");\n     * } catch (err) {\n     *   // the server did not acknowledge the event in the given delay\n     * }\n     *\n     * @return a Promise that will be fulfilled when the server acknowledges the event\n     */\n    emitWithAck(ev, ...args) {\n        return new Promise((resolve, reject) => {\n            const fn = (arg1, arg2) => {\n                return arg1 ? reject(arg1) : resolve(arg2);\n            };\n            fn.withError = true;\n            args.push(fn);\n            this.emit(ev, ...args);\n        });\n    }\n    /**\n     * Add the packet to the queue.\n     * @param args\n     * @private\n     */\n    _addToQueue(args) {\n        let ack;\n        if (typeof args[args.length - 1] === \"function\") {\n            ack = args.pop();\n        }\n        const packet = {\n            id: this._queueSeq++,\n            tryCount: 0,\n            pending: false,\n            args,\n            flags: Object.assign({ fromQueue: true }, this.flags),\n        };\n        args.push((err, ...responseArgs) => {\n            if (packet !== this._queue[0]) {\n                // the packet has already been acknowledged\n                return;\n            }\n            const hasError = err !== null;\n            if (hasError) {\n                if (packet.tryCount > this._opts.retries) {\n                    this._queue.shift();\n                    if (ack) {\n                        ack(err);\n                    }\n                }\n            }\n            else {\n                this._queue.shift();\n                if (ack) {\n                    ack(null, ...responseArgs);\n                }\n            }\n            packet.pending = false;\n            return this._drainQueue();\n        });\n        this._queue.push(packet);\n        this._drainQueue();\n    }\n    /**\n     * Send the first packet of the queue, and wait for an acknowledgement from the server.\n     * @param force - whether to resend a packet that has not been acknowledged yet\n     *\n     * @private\n     */\n    _drainQueue(force = false) {\n        if (!this.connected || this._queue.length === 0) {\n            return;\n        }\n        const packet = this._queue[0];\n        if (packet.pending && !force) {\n            return;\n        }\n        packet.pending = true;\n        packet.tryCount++;\n        this.flags = packet.flags;\n        this.emit.apply(this, packet.args);\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param packet\n     * @private\n     */\n    packet(packet) {\n        packet.nsp = this.nsp;\n        this.io._packet(packet);\n    }\n    /**\n     * Called upon engine `open`.\n     *\n     * @private\n     */\n    onopen() {\n        if (typeof this.auth == \"function\") {\n            this.auth((data) => {\n                this._sendConnectPacket(data);\n            });\n        }\n        else {\n            this._sendConnectPacket(this.auth);\n        }\n    }\n    /**\n     * Sends a CONNECT packet to initiate the Socket.IO session.\n     *\n     * @param data\n     * @private\n     */\n    _sendConnectPacket(data) {\n        this.packet({\n            type: PacketType.CONNECT,\n            data: this._pid\n                ? Object.assign({ pid: this._pid, offset: this._lastOffset }, data)\n                : data,\n        });\n    }\n    /**\n     * Called upon engine or manager `error`.\n     *\n     * @param err\n     * @private\n     */\n    onerror(err) {\n        if (!this.connected) {\n            this.emitReserved(\"connect_error\", err);\n        }\n    }\n    /**\n     * Called upon engine `close`.\n     *\n     * @param reason\n     * @param description\n     * @private\n     */\n    onclose(reason, description) {\n        this.connected = false;\n        delete this.id;\n        this.emitReserved(\"disconnect\", reason, description);\n        this._clearAcks();\n    }\n    /**\n     * Clears the acknowledgement handlers upon disconnection, since the client will never receive an acknowledgement from\n     * the server.\n     *\n     * @private\n     */\n    _clearAcks() {\n        Object.keys(this.acks).forEach((id) => {\n            const isBuffered = this.sendBuffer.some((packet) => String(packet.id) === id);\n            if (!isBuffered) {\n                // note: handlers that do not accept an error as first argument are ignored here\n                const ack = this.acks[id];\n                delete this.acks[id];\n                if (ack.withError) {\n                    ack.call(this, new Error(\"socket has been disconnected\"));\n                }\n            }\n        });\n    }\n    /**\n     * Called with socket packet.\n     *\n     * @param packet\n     * @private\n     */\n    onpacket(packet) {\n        const sameNamespace = packet.nsp === this.nsp;\n        if (!sameNamespace)\n            return;\n        switch (packet.type) {\n            case PacketType.CONNECT:\n                if (packet.data && packet.data.sid) {\n                    this.onconnect(packet.data.sid, packet.data.pid);\n                }\n                else {\n                    this.emitReserved(\"connect_error\", new Error(\"It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)\"));\n                }\n                break;\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                this.onevent(packet);\n                break;\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                this.onack(packet);\n                break;\n            case PacketType.DISCONNECT:\n                this.ondisconnect();\n                break;\n            case PacketType.CONNECT_ERROR:\n                this.destroy();\n                const err = new Error(packet.data.message);\n                // @ts-ignore\n                err.data = packet.data.data;\n                this.emitReserved(\"connect_error\", err);\n                break;\n        }\n    }\n    /**\n     * Called upon a server event.\n     *\n     * @param packet\n     * @private\n     */\n    onevent(packet) {\n        const args = packet.data || [];\n        if (null != packet.id) {\n            args.push(this.ack(packet.id));\n        }\n        if (this.connected) {\n            this.emitEvent(args);\n        }\n        else {\n            this.receiveBuffer.push(Object.freeze(args));\n        }\n    }\n    emitEvent(args) {\n        if (this._anyListeners && this._anyListeners.length) {\n            const listeners = this._anyListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, args);\n            }\n        }\n        super.emit.apply(this, args);\n        if (this._pid && args.length && typeof args[args.length - 1] === \"string\") {\n            this._lastOffset = args[args.length - 1];\n        }\n    }\n    /**\n     * Produces an ack callback to emit with an event.\n     *\n     * @private\n     */\n    ack(id) {\n        const self = this;\n        let sent = false;\n        return function (...args) {\n            // prevent double callbacks\n            if (sent)\n                return;\n            sent = true;\n            self.packet({\n                type: PacketType.ACK,\n                id: id,\n                data: args,\n            });\n        };\n    }\n    /**\n     * Called upon a server acknowledgement.\n     *\n     * @param packet\n     * @private\n     */\n    onack(packet) {\n        const ack = this.acks[packet.id];\n        if (typeof ack !== \"function\") {\n            return;\n        }\n        delete this.acks[packet.id];\n        // @ts-ignore FIXME ack is incorrectly inferred as 'never'\n        if (ack.withError) {\n            packet.data.unshift(null);\n        }\n        // @ts-ignore\n        ack.apply(this, packet.data);\n    }\n    /**\n     * Called upon server connect.\n     *\n     * @private\n     */\n    onconnect(id, pid) {\n        this.id = id;\n        this.recovered = pid && this._pid === pid;\n        this._pid = pid; // defined only if connection state recovery is enabled\n        this.connected = true;\n        this.emitBuffered();\n        this.emitReserved(\"connect\");\n        this._drainQueue(true);\n    }\n    /**\n     * Emit buffered events (received and emitted).\n     *\n     * @private\n     */\n    emitBuffered() {\n        this.receiveBuffer.forEach((args) => this.emitEvent(args));\n        this.receiveBuffer = [];\n        this.sendBuffer.forEach((packet) => {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        });\n        this.sendBuffer = [];\n    }\n    /**\n     * Called upon server disconnect.\n     *\n     * @private\n     */\n    ondisconnect() {\n        this.destroy();\n        this.onclose(\"io server disconnect\");\n    }\n    /**\n     * Called upon forced client/server side disconnections,\n     * this method ensures the manager stops tracking us and\n     * that reconnections don't get triggered for this.\n     *\n     * @private\n     */\n    destroy() {\n        if (this.subs) {\n            // clean subscriptions to avoid reconnections\n            this.subs.forEach((subDestroy) => subDestroy());\n            this.subs = undefined;\n        }\n        this.io[\"_destroy\"](this);\n    }\n    /**\n     * Disconnects the socket manually. In that case, the socket will not try to reconnect.\n     *\n     * If this is the last active Socket instance of the {@link Manager}, the low-level connection will be closed.\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   // console.log(reason); prints \"io client disconnect\"\n     * });\n     *\n     * socket.disconnect();\n     *\n     * @return self\n     */\n    disconnect() {\n        if (this.connected) {\n            this.packet({ type: PacketType.DISCONNECT });\n        }\n        // remove socket from pool\n        this.destroy();\n        if (this.connected) {\n            // fire events\n            this.onclose(\"io client disconnect\");\n        }\n        return this;\n    }\n    /**\n     * Alias for {@link disconnect()}.\n     *\n     * @return self\n     */\n    close() {\n        return this.disconnect();\n    }\n    /**\n     * Sets the compress flag.\n     *\n     * @example\n     * socket.compress(false).emit(\"hello\");\n     *\n     * @param compress - if `true`, compresses the sending data\n     * @return self\n     */\n    compress(compress) {\n        this.flags.compress = compress;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the event message will be dropped when this socket is not\n     * ready to send messages.\n     *\n     * @example\n     * socket.volatile.emit(\"hello\"); // the server may or may not receive it\n     *\n     * @returns self\n     */\n    get volatile() {\n        this.flags.volatile = true;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the callback will be called with an error when the\n     * given number of milliseconds have elapsed without an acknowledgement from the server:\n     *\n     * @example\n     * socket.timeout(5000).emit(\"my-event\", (err) => {\n     *   if (err) {\n     *     // the server did not acknowledge the event in the given delay\n     *   }\n     * });\n     *\n     * @returns self\n     */\n    timeout(timeout) {\n        this.flags.timeout = timeout;\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * @example\n     * socket.onAny((event, ...args) => {\n     *   console.log(`got ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * @example\n     * socket.prependAny((event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * }\n     *\n     * socket.onAny(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAny(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAny();\n     *\n     * @param listener\n     */\n    offAny(listener) {\n        if (!this._anyListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAny() {\n        return this._anyListeners || [];\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.onAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.prependAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * }\n     *\n     * socket.onAnyOutgoing(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAnyOutgoing(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAnyOutgoing();\n     *\n     * @param [listener] - the catch-all listener (optional)\n     */\n    offAnyOutgoing(listener) {\n        if (!this._anyOutgoingListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyOutgoingListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyOutgoingListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAnyOutgoing() {\n        return this._anyOutgoingListeners || [];\n    }\n    /**\n     * Notify the listeners for each packet sent\n     *\n     * @param packet\n     *\n     * @private\n     */\n    notifyOutgoingListeners(packet) {\n        if (this._anyOutgoingListeners && this._anyOutgoingListeners.length) {\n            const listeners = this._anyOutgoingListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, packet.data);\n            }\n        }\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACA;;;CAGC,GACD,MAAM,kBAAkB,OAAO,MAAM,CAAC;IAClC,SAAS;IACT,eAAe;IACf,YAAY;IACZ,eAAe;IACf,4FAA4F;IAC5F,aAAa;IACb,gBAAgB;AACpB;AAyBO,MAAM,eAAe,gLAAA,CAAA,UAAO;IAC/B;;KAEC,GACD,YAAY,EAAE,EAAE,GAAG,EAAE,IAAI,CAAE;QACvB,KAAK;QACL;;;;;;;;;;;;;SAaC,GACD,IAAI,CAAC,SAAS,GAAG;QACjB;;;SAGC,GACD,IAAI,CAAC,SAAS,GAAG;QACjB;;SAEC,GACD,IAAI,CAAC,aAAa,GAAG,EAAE;QACvB;;SAEC,GACD,IAAI,CAAC,UAAU,GAAG,EAAE;QACpB;;;;;SAKC,GACD,IAAI,CAAC,MAAM,GAAG,EAAE;QAChB;;;SAGC,GACD,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,GAAG,GAAG;QACX;;;;;;;;;;;;;;;;;;;;;;SAsBC,GACD,IAAI,CAAC,IAAI,GAAG,CAAC;QACb,IAAI,CAAC,KAAK,GAAG,CAAC;QACd,IAAI,CAAC,EAAE,GAAG;QACV,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,QAAQ,KAAK,IAAI,EAAE;YACnB,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI;QACzB;QACA,IAAI,CAAC,KAAK,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG;QAC/B,IAAI,IAAI,CAAC,EAAE,CAAC,YAAY,EACpB,IAAI,CAAC,IAAI;IACjB;IACA;;;;;;;;;;;;;KAaC,GACD,IAAI,eAAe;QACf,OAAO,CAAC,IAAI,CAAC,SAAS;IAC1B;IACA;;;;KAIC,GACD,YAAY;QACR,IAAI,IAAI,CAAC,IAAI,EACT;QACJ,MAAM,KAAK,IAAI,CAAC,EAAE;QAClB,IAAI,CAAC,IAAI,GAAG;YACR,CAAA,GAAA,+JAAA,CAAA,KAAE,AAAD,EAAE,IAAI,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI;YACpC,CAAA,GAAA,+JAAA,CAAA,KAAE,AAAD,EAAE,IAAI,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI;YACxC,CAAA,GAAA,+JAAA,CAAA,KAAE,AAAD,EAAE,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;YACtC,CAAA,GAAA,+JAAA,CAAA,KAAE,AAAD,EAAE,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;SACzC;IACL;IACA;;;;;;;;;;;;;;;;KAgBC,GACD,IAAI,SAAS;QACT,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI;IACtB;IACA;;;;;;;;;KASC,GACD,UAAU;QACN,IAAI,IAAI,CAAC,SAAS,EACd,OAAO,IAAI;QACf,IAAI,CAAC,SAAS;QACd,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,gBAAgB,EACzB,IAAI,CAAC,EAAE,CAAC,IAAI,IAAI,cAAc;QAClC,IAAI,WAAW,IAAI,CAAC,EAAE,CAAC,WAAW,EAC9B,IAAI,CAAC,MAAM;QACf,OAAO,IAAI;IACf;IACA;;KAEC,GACD,OAAO;QACH,OAAO,IAAI,CAAC,OAAO;IACvB;IACA;;;;;;;;;;;;;;KAcC,GACD,KAAK,GAAG,IAAI,EAAE;QACV,KAAK,OAAO,CAAC;QACb,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;QACtB,OAAO,IAAI;IACf;IACA;;;;;;;;;;;;;;;;KAgBC,GACD,KAAK,EAAE,EAAE,GAAG,IAAI,EAAE;QACd,IAAI,IAAI,IAAI;QACZ,IAAI,gBAAgB,cAAc,CAAC,KAAK;YACpC,MAAM,IAAI,MAAM,MAAM,GAAG,QAAQ,KAAK;QAC1C;QACA,KAAK,OAAO,CAAC;QACb,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACrE,IAAI,CAAC,WAAW,CAAC;YACjB,OAAO,IAAI;QACf;QACA,MAAM,SAAS;YACX,MAAM,kKAAA,CAAA,aAAU,CAAC,KAAK;YACtB,MAAM;QACV;QACA,OAAO,OAAO,GAAG,CAAC;QAClB,OAAO,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK;QAClD,qBAAqB;QACrB,IAAI,eAAe,OAAO,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,EAAE;YAC7C,MAAM,KAAK,IAAI,CAAC,GAAG;YACnB,MAAM,MAAM,KAAK,GAAG;YACpB,IAAI,CAAC,oBAAoB,CAAC,IAAI;YAC9B,OAAO,EAAE,GAAG;QAChB;QACA,MAAM,sBAAsB,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,EAAE,CAAC,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,SAAS,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,QAAQ;QAC3J,MAAM,cAAc,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE,CAAC,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,eAAe,EAAE;QACvH,MAAM,gBAAgB,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,CAAC;QAC9C,IAAI,eAAe,CACnB,OACK,IAAI,aAAa;YAClB,IAAI,CAAC,uBAAuB,CAAC;YAC7B,IAAI,CAAC,MAAM,CAAC;QAChB,OACK;YACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;QACzB;QACA,IAAI,CAAC,KAAK,GAAG,CAAC;QACd,OAAO,IAAI;IACf;IACA;;KAEC,GACD,qBAAqB,EAAE,EAAE,GAAG,EAAE;QAC1B,IAAI;QACJ,MAAM,UAAU,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,UAAU;QAChG,IAAI,YAAY,WAAW;YACvB,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG;YAChB;QACJ;QACA,aAAa;QACb,MAAM,QAAQ,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC;YAC/B,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG;YACpB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,IAAK;gBAC7C,IAAI,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,KAAK,IAAI;oBAC9B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG;gBAC9B;YACJ;YACA,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,MAAM;QAC7B,GAAG;QACH,MAAM,KAAK,CAAC,GAAG;YACX,aAAa;YACb,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC;YACvB,IAAI,KAAK,CAAC,IAAI,EAAE;QACpB;QACA,GAAG,SAAS,GAAG;QACf,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG;IACpB;IACA;;;;;;;;;;;;;;;KAeC,GACD,YAAY,EAAE,EAAE,GAAG,IAAI,EAAE;QACrB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,MAAM,KAAK,CAAC,MAAM;gBACd,OAAO,OAAO,OAAO,QAAQ,QAAQ;YACzC;YACA,GAAG,SAAS,GAAG;YACf,KAAK,IAAI,CAAC;YACV,IAAI,CAAC,IAAI,CAAC,OAAO;QACrB;IACJ;IACA;;;;KAIC,GACD,YAAY,IAAI,EAAE;QACd,IAAI;QACJ,IAAI,OAAO,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,KAAK,YAAY;YAC7C,MAAM,KAAK,GAAG;QAClB;QACA,MAAM,SAAS;YACX,IAAI,IAAI,CAAC,SAAS;YAClB,UAAU;YACV,SAAS;YACT;YACA,OAAO,OAAO,MAAM,CAAC;gBAAE,WAAW;YAAK,GAAG,IAAI,CAAC,KAAK;QACxD;QACA,KAAK,IAAI,CAAC,CAAC,KAAK,GAAG;YACf,IAAI,WAAW,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;gBAC3B,2CAA2C;gBAC3C;YACJ;YACA,MAAM,WAAW,QAAQ;YACzB,IAAI,UAAU;gBACV,IAAI,OAAO,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;oBACtC,IAAI,CAAC,MAAM,CAAC,KAAK;oBACjB,IAAI,KAAK;wBACL,IAAI;oBACR;gBACJ;YACJ,OACK;gBACD,IAAI,CAAC,MAAM,CAAC,KAAK;gBACjB,IAAI,KAAK;oBACL,IAAI,SAAS;gBACjB;YACJ;YACA,OAAO,OAAO,GAAG;YACjB,OAAO,IAAI,CAAC,WAAW;QAC3B;QACA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QACjB,IAAI,CAAC,WAAW;IACpB;IACA;;;;;KAKC,GACD,YAAY,QAAQ,KAAK,EAAE;QACvB,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,GAAG;YAC7C;QACJ;QACA,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC,EAAE;QAC7B,IAAI,OAAO,OAAO,IAAI,CAAC,OAAO;YAC1B;QACJ;QACA,OAAO,OAAO,GAAG;QACjB,OAAO,QAAQ;QACf,IAAI,CAAC,KAAK,GAAG,OAAO,KAAK;QACzB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,IAAI;IACrC;IACA;;;;;KAKC,GACD,OAAO,MAAM,EAAE;QACX,OAAO,GAAG,GAAG,IAAI,CAAC,GAAG;QACrB,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC;IACpB;IACA;;;;KAIC,GACD,SAAS;QACL,IAAI,OAAO,IAAI,CAAC,IAAI,IAAI,YAAY;YAChC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACP,IAAI,CAAC,kBAAkB,CAAC;YAC5B;QACJ,OACK;YACD,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI;QACrC;IACJ;IACA;;;;;KAKC,GACD,mBAAmB,IAAI,EAAE;QACrB,IAAI,CAAC,MAAM,CAAC;YACR,MAAM,kKAAA,CAAA,aAAU,CAAC,OAAO;YACxB,MAAM,IAAI,CAAC,IAAI,GACT,OAAO,MAAM,CAAC;gBAAE,KAAK,IAAI,CAAC,IAAI;gBAAE,QAAQ,IAAI,CAAC,WAAW;YAAC,GAAG,QAC5D;QACV;IACJ;IACA;;;;;KAKC,GACD,QAAQ,GAAG,EAAE;QACT,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,IAAI,CAAC,YAAY,CAAC,iBAAiB;QACvC;IACJ;IACA;;;;;;KAMC,GACD,QAAQ,MAAM,EAAE,WAAW,EAAE;QACzB,IAAI,CAAC,SAAS,GAAG;QACjB,OAAO,IAAI,CAAC,EAAE;QACd,IAAI,CAAC,YAAY,CAAC,cAAc,QAAQ;QACxC,IAAI,CAAC,UAAU;IACnB;IACA;;;;;KAKC,GACD,aAAa;QACT,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAC5B,MAAM,aAAa,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,SAAW,OAAO,OAAO,EAAE,MAAM;YAC1E,IAAI,CAAC,YAAY;gBACb,gFAAgF;gBAChF,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG;gBACzB,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG;gBACpB,IAAI,IAAI,SAAS,EAAE;oBACf,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,MAAM;gBAC7B;YACJ;QACJ;IACJ;IACA;;;;;KAKC,GACD,SAAS,MAAM,EAAE;QACb,MAAM,gBAAgB,OAAO,GAAG,KAAK,IAAI,CAAC,GAAG;QAC7C,IAAI,CAAC,eACD;QACJ,OAAQ,OAAO,IAAI;YACf,KAAK,kKAAA,CAAA,aAAU,CAAC,OAAO;gBACnB,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,GAAG,EAAE;oBAChC,IAAI,CAAC,SAAS,CAAC,OAAO,IAAI,CAAC,GAAG,EAAE,OAAO,IAAI,CAAC,GAAG;gBACnD,OACK;oBACD,IAAI,CAAC,YAAY,CAAC,iBAAiB,IAAI,MAAM;gBACjD;gBACA;YACJ,KAAK,kKAAA,CAAA,aAAU,CAAC,KAAK;YACrB,KAAK,kKAAA,CAAA,aAAU,CAAC,YAAY;gBACxB,IAAI,CAAC,OAAO,CAAC;gBACb;YACJ,KAAK,kKAAA,CAAA,aAAU,CAAC,GAAG;YACnB,KAAK,kKAAA,CAAA,aAAU,CAAC,UAAU;gBACtB,IAAI,CAAC,KAAK,CAAC;gBACX;YACJ,KAAK,kKAAA,CAAA,aAAU,CAAC,UAAU;gBACtB,IAAI,CAAC,YAAY;gBACjB;YACJ,KAAK,kKAAA,CAAA,aAAU,CAAC,aAAa;gBACzB,IAAI,CAAC,OAAO;gBACZ,MAAM,MAAM,IAAI,MAAM,OAAO,IAAI,CAAC,OAAO;gBACzC,aAAa;gBACb,IAAI,IAAI,GAAG,OAAO,IAAI,CAAC,IAAI;gBAC3B,IAAI,CAAC,YAAY,CAAC,iBAAiB;gBACnC;QACR;IACJ;IACA;;;;;KAKC,GACD,QAAQ,MAAM,EAAE;QACZ,MAAM,OAAO,OAAO,IAAI,IAAI,EAAE;QAC9B,IAAI,QAAQ,OAAO,EAAE,EAAE;YACnB,KAAK,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE;QAChC;QACA,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,SAAS,CAAC;QACnB,OACK;YACD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,MAAM,CAAC;QAC1C;IACJ;IACA,UAAU,IAAI,EAAE;QACZ,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;YACjD,MAAM,YAAY,IAAI,CAAC,aAAa,CAAC,KAAK;YAC1C,KAAK,MAAM,YAAY,UAAW;gBAC9B,SAAS,KAAK,CAAC,IAAI,EAAE;YACzB;QACJ;QACA,KAAK,CAAC,KAAK,KAAK,CAAC,IAAI,EAAE;QACvB,IAAI,IAAI,CAAC,IAAI,IAAI,KAAK,MAAM,IAAI,OAAO,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,KAAK,UAAU;YACvE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QAC5C;IACJ;IACA;;;;KAIC,GACD,IAAI,EAAE,EAAE;QACJ,MAAM,OAAO,IAAI;QACjB,IAAI,OAAO;QACX,OAAO,SAAU,GAAG,IAAI;YACpB,2BAA2B;YAC3B,IAAI,MACA;YACJ,OAAO;YACP,KAAK,MAAM,CAAC;gBACR,MAAM,kKAAA,CAAA,aAAU,CAAC,GAAG;gBACpB,IAAI;gBACJ,MAAM;YACV;QACJ;IACJ;IACA;;;;;KAKC,GACD,MAAM,MAAM,EAAE;QACV,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;QAChC,IAAI,OAAO,QAAQ,YAAY;YAC3B;QACJ;QACA,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;QAC3B,0DAA0D;QAC1D,IAAI,IAAI,SAAS,EAAE;YACf,OAAO,IAAI,CAAC,OAAO,CAAC;QACxB;QACA,aAAa;QACb,IAAI,KAAK,CAAC,IAAI,EAAE,OAAO,IAAI;IAC/B;IACA;;;;KAIC,GACD,UAAU,EAAE,EAAE,GAAG,EAAE;QACf,IAAI,CAAC,EAAE,GAAG;QACV,IAAI,CAAC,SAAS,GAAG,OAAO,IAAI,CAAC,IAAI,KAAK;QACtC,IAAI,CAAC,IAAI,GAAG,KAAK,uDAAuD;QACxE,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,YAAY;QACjB,IAAI,CAAC,YAAY,CAAC;QAClB,IAAI,CAAC,WAAW,CAAC;IACrB;IACA;;;;KAIC,GACD,eAAe;QACX,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,OAAS,IAAI,CAAC,SAAS,CAAC;QACpD,IAAI,CAAC,aAAa,GAAG,EAAE;QACvB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YACrB,IAAI,CAAC,uBAAuB,CAAC;YAC7B,IAAI,CAAC,MAAM,CAAC;QAChB;QACA,IAAI,CAAC,UAAU,GAAG,EAAE;IACxB;IACA;;;;KAIC,GACD,eAAe;QACX,IAAI,CAAC,OAAO;QACZ,IAAI,CAAC,OAAO,CAAC;IACjB;IACA;;;;;;KAMC,GACD,UAAU;QACN,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,6CAA6C;YAC7C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,aAAe;YAClC,IAAI,CAAC,IAAI,GAAG;QAChB;QACA,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI;IAC5B;IACA;;;;;;;;;;;;;;;KAeC,GACD,aAAa;QACT,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,MAAM,CAAC;gBAAE,MAAM,kKAAA,CAAA,aAAU,CAAC,UAAU;YAAC;QAC9C;QACA,0BAA0B;QAC1B,IAAI,CAAC,OAAO;QACZ,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,cAAc;YACd,IAAI,CAAC,OAAO,CAAC;QACjB;QACA,OAAO,IAAI;IACf;IACA;;;;KAIC,GACD,QAAQ;QACJ,OAAO,IAAI,CAAC,UAAU;IAC1B;IACA;;;;;;;;KAQC,GACD,SAAS,QAAQ,EAAE;QACf,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACtB,OAAO,IAAI;IACf;IACA;;;;;;;;KAQC,GACD,IAAI,WAAW;QACX,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACtB,OAAO,IAAI;IACf;IACA;;;;;;;;;;;;KAYC,GACD,QAAQ,OAAO,EAAE;QACb,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG;QACrB,OAAO,IAAI;IACf;IACA;;;;;;;;;;KAUC,GACD,MAAM,QAAQ,EAAE;QACZ,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,IAAI,EAAE;QAC7C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;QACxB,OAAO,IAAI;IACf;IACA;;;;;;;;;;KAUC,GACD,WAAW,QAAQ,EAAE;QACjB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,IAAI,EAAE;QAC7C,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;QAC3B,OAAO,IAAI;IACf;IACA;;;;;;;;;;;;;;;;;KAiBC,GACD,OAAO,QAAQ,EAAE;QACb,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,OAAO,IAAI;QACf;QACA,IAAI,UAAU;YACV,MAAM,YAAY,IAAI,CAAC,aAAa;YACpC,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;gBACvC,IAAI,aAAa,SAAS,CAAC,EAAE,EAAE;oBAC3B,UAAU,MAAM,CAAC,GAAG;oBACpB,OAAO,IAAI;gBACf;YACJ;QACJ,OACK;YACD,IAAI,CAAC,aAAa,GAAG,EAAE;QAC3B;QACA,OAAO,IAAI;IACf;IACA;;;KAGC,GACD,eAAe;QACX,OAAO,IAAI,CAAC,aAAa,IAAI,EAAE;IACnC;IACA;;;;;;;;;;;;KAYC,GACD,cAAc,QAAQ,EAAE;QACpB,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,IAAI,EAAE;QAC7D,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;QAChC,OAAO,IAAI;IACf;IACA;;;;;;;;;;;;KAYC,GACD,mBAAmB,QAAQ,EAAE;QACzB,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,IAAI,EAAE;QAC7D,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;QACnC,OAAO,IAAI;IACf;IACA;;;;;;;;;;;;;;;;;KAiBC,GACD,eAAe,QAAQ,EAAE;QACrB,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;YAC7B,OAAO,IAAI;QACf;QACA,IAAI,UAAU;YACV,MAAM,YAAY,IAAI,CAAC,qBAAqB;YAC5C,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;gBACvC,IAAI,aAAa,SAAS,CAAC,EAAE,EAAE;oBAC3B,UAAU,MAAM,CAAC,GAAG;oBACpB,OAAO,IAAI;gBACf;YACJ;QACJ,OACK;YACD,IAAI,CAAC,qBAAqB,GAAG,EAAE;QACnC;QACA,OAAO,IAAI;IACf;IACA;;;KAGC,GACD,uBAAuB;QACnB,OAAO,IAAI,CAAC,qBAAqB,IAAI,EAAE;IAC3C;IACA;;;;;;KAMC,GACD,wBAAwB,MAAM,EAAE;QAC5B,IAAI,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE;YACjE,MAAM,YAAY,IAAI,CAAC,qBAAqB,CAAC,KAAK;YAClD,KAAK,MAAM,YAAY,UAAW;gBAC9B,SAAS,KAAK,CAAC,IAAI,EAAE,OAAO,IAAI;YACpC;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3547, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/socket.io-client/build/esm/contrib/backo2.js"], "sourcesContent": ["/**\n * Initialize backoff timer with `opts`.\n *\n * - `min` initial timeout in milliseconds [100]\n * - `max` max timeout [10000]\n * - `jitter` [0]\n * - `factor` [2]\n *\n * @param {Object} opts\n * @api public\n */\nexport function Backoff(opts) {\n    opts = opts || {};\n    this.ms = opts.min || 100;\n    this.max = opts.max || 10000;\n    this.factor = opts.factor || 2;\n    this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n    this.attempts = 0;\n}\n/**\n * Return the backoff duration.\n *\n * @return {Number}\n * @api public\n */\nBackoff.prototype.duration = function () {\n    var ms = this.ms * Math.pow(this.factor, this.attempts++);\n    if (this.jitter) {\n        var rand = Math.random();\n        var deviation = Math.floor(rand * this.jitter * ms);\n        ms = (Math.floor(rand * 10) & 1) == 0 ? ms - deviation : ms + deviation;\n    }\n    return Math.min(ms, this.max) | 0;\n};\n/**\n * Reset the number of attempts.\n *\n * @api public\n */\nBackoff.prototype.reset = function () {\n    this.attempts = 0;\n};\n/**\n * Set the minimum duration\n *\n * @api public\n */\nBackoff.prototype.setMin = function (min) {\n    this.ms = min;\n};\n/**\n * Set the maximum duration\n *\n * @api public\n */\nBackoff.prototype.setMax = function (max) {\n    this.max = max;\n};\n/**\n * Set the jitter\n *\n * @api public\n */\nBackoff.prototype.setJitter = function (jitter) {\n    this.jitter = jitter;\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;AACM,SAAS,QAAQ,IAAI;IACxB,OAAO,QAAQ,CAAC;IAChB,IAAI,CAAC,EAAE,GAAG,KAAK,GAAG,IAAI;IACtB,IAAI,CAAC,GAAG,GAAG,KAAK,GAAG,IAAI;IACvB,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM,IAAI;IAC7B,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM,GAAG,KAAK,KAAK,MAAM,IAAI,IAAI,KAAK,MAAM,GAAG;IAClE,IAAI,CAAC,QAAQ,GAAG;AACpB;AACA;;;;;CAKC,GACD,QAAQ,SAAS,CAAC,QAAQ,GAAG;IACzB,IAAI,KAAK,IAAI,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ;IACtD,IAAI,IAAI,CAAC,MAAM,EAAE;QACb,IAAI,OAAO,KAAK,MAAM;QACtB,IAAI,YAAY,KAAK,KAAK,CAAC,OAAO,IAAI,CAAC,MAAM,GAAG;QAChD,KAAK,CAAC,KAAK,KAAK,CAAC,OAAO,MAAM,CAAC,KAAK,IAAI,KAAK,YAAY,KAAK;IAClE;IACA,OAAO,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,IAAI;AACpC;AACA;;;;CAIC,GACD,QAAQ,SAAS,CAAC,KAAK,GAAG;IACtB,IAAI,CAAC,QAAQ,GAAG;AACpB;AACA;;;;CAIC,GACD,QAAQ,SAAS,CAAC,MAAM,GAAG,SAAU,GAAG;IACpC,IAAI,CAAC,EAAE,GAAG;AACd;AACA;;;;CAIC,GACD,QAAQ,SAAS,CAAC,MAAM,GAAG,SAAU,GAAG;IACpC,IAAI,CAAC,GAAG,GAAG;AACf;AACA;;;;CAIC,GACD,QAAQ,SAAS,CAAC,SAAS,GAAG,SAAU,MAAM;IAC1C,IAAI,CAAC,MAAM,GAAG;AAClB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3616, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/socket.io-client/build/esm/manager.js"], "sourcesContent": ["import { Socket as Engine, installTimerFunctions, nextTick, } from \"engine.io-client\";\nimport { Socket } from \"./socket.js\";\nimport * as parser from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Backoff } from \"./contrib/backo2.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\nexport class Manager extends Emitter {\n    constructor(uri, opts) {\n        var _a;\n        super();\n        this.nsps = {};\n        this.subs = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = undefined;\n        }\n        opts = opts || {};\n        opts.path = opts.path || \"/socket.io\";\n        this.opts = opts;\n        installTimerFunctions(this, opts);\n        this.reconnection(opts.reconnection !== false);\n        this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n        this.reconnectionDelay(opts.reconnectionDelay || 1000);\n        this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n        this.randomizationFactor((_a = opts.randomizationFactor) !== null && _a !== void 0 ? _a : 0.5);\n        this.backoff = new Backoff({\n            min: this.reconnectionDelay(),\n            max: this.reconnectionDelayMax(),\n            jitter: this.randomizationFactor(),\n        });\n        this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n        this._readyState = \"closed\";\n        this.uri = uri;\n        const _parser = opts.parser || parser;\n        this.encoder = new _parser.Encoder();\n        this.decoder = new _parser.Decoder();\n        this._autoConnect = opts.autoConnect !== false;\n        if (this._autoConnect)\n            this.open();\n    }\n    reconnection(v) {\n        if (!arguments.length)\n            return this._reconnection;\n        this._reconnection = !!v;\n        if (!v) {\n            this.skipReconnect = true;\n        }\n        return this;\n    }\n    reconnectionAttempts(v) {\n        if (v === undefined)\n            return this._reconnectionAttempts;\n        this._reconnectionAttempts = v;\n        return this;\n    }\n    reconnectionDelay(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelay;\n        this._reconnectionDelay = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMin(v);\n        return this;\n    }\n    randomizationFactor(v) {\n        var _a;\n        if (v === undefined)\n            return this._randomizationFactor;\n        this._randomizationFactor = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setJitter(v);\n        return this;\n    }\n    reconnectionDelayMax(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelayMax;\n        this._reconnectionDelayMax = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMax(v);\n        return this;\n    }\n    timeout(v) {\n        if (!arguments.length)\n            return this._timeout;\n        this._timeout = v;\n        return this;\n    }\n    /**\n     * Starts trying to reconnect if reconnection is enabled and we have not\n     * started reconnecting yet\n     *\n     * @private\n     */\n    maybeReconnectOnOpen() {\n        // Only try to reconnect if it's the first time we're connecting\n        if (!this._reconnecting &&\n            this._reconnection &&\n            this.backoff.attempts === 0) {\n            // keeps reconnection from firing twice for the same reconnection loop\n            this.reconnect();\n        }\n    }\n    /**\n     * Sets the current transport `socket`.\n     *\n     * @param {Function} fn - optional, callback\n     * @return self\n     * @public\n     */\n    open(fn) {\n        if (~this._readyState.indexOf(\"open\"))\n            return this;\n        this.engine = new Engine(this.uri, this.opts);\n        const socket = this.engine;\n        const self = this;\n        this._readyState = \"opening\";\n        this.skipReconnect = false;\n        // emit `open`\n        const openSubDestroy = on(socket, \"open\", function () {\n            self.onopen();\n            fn && fn();\n        });\n        const onError = (err) => {\n            this.cleanup();\n            this._readyState = \"closed\";\n            this.emitReserved(\"error\", err);\n            if (fn) {\n                fn(err);\n            }\n            else {\n                // Only do this if there is no fn to handle the error\n                this.maybeReconnectOnOpen();\n            }\n        };\n        // emit `error`\n        const errorSub = on(socket, \"error\", onError);\n        if (false !== this._timeout) {\n            const timeout = this._timeout;\n            // set timer\n            const timer = this.setTimeoutFn(() => {\n                openSubDestroy();\n                onError(new Error(\"timeout\"));\n                socket.close();\n            }, timeout);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(() => {\n                this.clearTimeoutFn(timer);\n            });\n        }\n        this.subs.push(openSubDestroy);\n        this.subs.push(errorSub);\n        return this;\n    }\n    /**\n     * Alias for open()\n     *\n     * @return self\n     * @public\n     */\n    connect(fn) {\n        return this.open(fn);\n    }\n    /**\n     * Called upon transport open.\n     *\n     * @private\n     */\n    onopen() {\n        // clear old subs\n        this.cleanup();\n        // mark as open\n        this._readyState = \"open\";\n        this.emitReserved(\"open\");\n        // add new subs\n        const socket = this.engine;\n        this.subs.push(on(socket, \"ping\", this.onping.bind(this)), on(socket, \"data\", this.ondata.bind(this)), on(socket, \"error\", this.onerror.bind(this)), on(socket, \"close\", this.onclose.bind(this)), \n        // @ts-ignore\n        on(this.decoder, \"decoded\", this.ondecoded.bind(this)));\n    }\n    /**\n     * Called upon a ping.\n     *\n     * @private\n     */\n    onping() {\n        this.emitReserved(\"ping\");\n    }\n    /**\n     * Called with data.\n     *\n     * @private\n     */\n    ondata(data) {\n        try {\n            this.decoder.add(data);\n        }\n        catch (e) {\n            this.onclose(\"parse error\", e);\n        }\n    }\n    /**\n     * Called when parser fully decodes a packet.\n     *\n     * @private\n     */\n    ondecoded(packet) {\n        // the nextTick call prevents an exception in a user-provided event listener from triggering a disconnection due to a \"parse error\"\n        nextTick(() => {\n            this.emitReserved(\"packet\", packet);\n        }, this.setTimeoutFn);\n    }\n    /**\n     * Called upon socket error.\n     *\n     * @private\n     */\n    onerror(err) {\n        this.emitReserved(\"error\", err);\n    }\n    /**\n     * Creates a new socket for the given `nsp`.\n     *\n     * @return {Socket}\n     * @public\n     */\n    socket(nsp, opts) {\n        let socket = this.nsps[nsp];\n        if (!socket) {\n            socket = new Socket(this, nsp, opts);\n            this.nsps[nsp] = socket;\n        }\n        else if (this._autoConnect && !socket.active) {\n            socket.connect();\n        }\n        return socket;\n    }\n    /**\n     * Called upon a socket close.\n     *\n     * @param socket\n     * @private\n     */\n    _destroy(socket) {\n        const nsps = Object.keys(this.nsps);\n        for (const nsp of nsps) {\n            const socket = this.nsps[nsp];\n            if (socket.active) {\n                return;\n            }\n        }\n        this._close();\n    }\n    /**\n     * Writes a packet.\n     *\n     * @param packet\n     * @private\n     */\n    _packet(packet) {\n        const encodedPackets = this.encoder.encode(packet);\n        for (let i = 0; i < encodedPackets.length; i++) {\n            this.engine.write(encodedPackets[i], packet.options);\n        }\n    }\n    /**\n     * Clean up transport subscriptions and packet buffer.\n     *\n     * @private\n     */\n    cleanup() {\n        this.subs.forEach((subDestroy) => subDestroy());\n        this.subs.length = 0;\n        this.decoder.destroy();\n    }\n    /**\n     * Close the current socket.\n     *\n     * @private\n     */\n    _close() {\n        this.skipReconnect = true;\n        this._reconnecting = false;\n        this.onclose(\"forced close\");\n    }\n    /**\n     * Alias for close()\n     *\n     * @private\n     */\n    disconnect() {\n        return this._close();\n    }\n    /**\n     * Called when:\n     *\n     * - the low-level engine is closed\n     * - the parser encountered a badly formatted packet\n     * - all sockets are disconnected\n     *\n     * @private\n     */\n    onclose(reason, description) {\n        var _a;\n        this.cleanup();\n        (_a = this.engine) === null || _a === void 0 ? void 0 : _a.close();\n        this.backoff.reset();\n        this._readyState = \"closed\";\n        this.emitReserved(\"close\", reason, description);\n        if (this._reconnection && !this.skipReconnect) {\n            this.reconnect();\n        }\n    }\n    /**\n     * Attempt a reconnection.\n     *\n     * @private\n     */\n    reconnect() {\n        if (this._reconnecting || this.skipReconnect)\n            return this;\n        const self = this;\n        if (this.backoff.attempts >= this._reconnectionAttempts) {\n            this.backoff.reset();\n            this.emitReserved(\"reconnect_failed\");\n            this._reconnecting = false;\n        }\n        else {\n            const delay = this.backoff.duration();\n            this._reconnecting = true;\n            const timer = this.setTimeoutFn(() => {\n                if (self.skipReconnect)\n                    return;\n                this.emitReserved(\"reconnect_attempt\", self.backoff.attempts);\n                // check again for the case socket closed in above events\n                if (self.skipReconnect)\n                    return;\n                self.open((err) => {\n                    if (err) {\n                        self._reconnecting = false;\n                        self.reconnect();\n                        this.emitReserved(\"reconnect_error\", err);\n                    }\n                    else {\n                        self.onreconnect();\n                    }\n                });\n            }, delay);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(() => {\n                this.clearTimeoutFn(timer);\n            });\n        }\n    }\n    /**\n     * Called upon successful reconnect.\n     *\n     * @private\n     */\n    onreconnect() {\n        const attempt = this.backoff.attempts;\n        this._reconnecting = false;\n        this.backoff.reset();\n        this.emitReserved(\"reconnect\", attempt);\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AACO,MAAM,gBAAgB,gLAAA,CAAA,UAAO;IAChC,YAAY,GAAG,EAAE,IAAI,CAAE;QACnB,IAAI;QACJ,KAAK;QACL,IAAI,CAAC,IAAI,GAAG,CAAC;QACb,IAAI,CAAC,IAAI,GAAG,EAAE;QACd,IAAI,OAAO,aAAa,OAAO,KAAK;YAChC,OAAO;YACP,MAAM;QACV;QACA,OAAO,QAAQ,CAAC;QAChB,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI;QACzB,IAAI,CAAC,IAAI,GAAG;QACZ,CAAA,GAAA,iKAAA,CAAA,wBAAqB,AAAD,EAAE,IAAI,EAAE;QAC5B,IAAI,CAAC,YAAY,CAAC,KAAK,YAAY,KAAK;QACxC,IAAI,CAAC,oBAAoB,CAAC,KAAK,oBAAoB,IAAI;QACvD,IAAI,CAAC,iBAAiB,CAAC,KAAK,iBAAiB,IAAI;QACjD,IAAI,CAAC,oBAAoB,CAAC,KAAK,oBAAoB,IAAI;QACvD,IAAI,CAAC,mBAAmB,CAAC,CAAC,KAAK,KAAK,mBAAmB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QAC1F,IAAI,CAAC,OAAO,GAAG,IAAI,8KAAA,CAAA,UAAO,CAAC;YACvB,KAAK,IAAI,CAAC,iBAAiB;YAC3B,KAAK,IAAI,CAAC,oBAAoB;YAC9B,QAAQ,IAAI,CAAC,mBAAmB;QACpC;QACA,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,OAAO,GAAG,QAAQ,KAAK,OAAO;QACxD,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,GAAG,GAAG;QACX,MAAM,UAAU,KAAK,MAAM,IAAI;QAC/B,IAAI,CAAC,OAAO,GAAG,IAAI,QAAQ,OAAO;QAClC,IAAI,CAAC,OAAO,GAAG,IAAI,QAAQ,OAAO;QAClC,IAAI,CAAC,YAAY,GAAG,KAAK,WAAW,KAAK;QACzC,IAAI,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,IAAI;IACjB;IACA,aAAa,CAAC,EAAE;QACZ,IAAI,CAAC,UAAU,MAAM,EACjB,OAAO,IAAI,CAAC,aAAa;QAC7B,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,GAAG;YACJ,IAAI,CAAC,aAAa,GAAG;QACzB;QACA,OAAO,IAAI;IACf;IACA,qBAAqB,CAAC,EAAE;QACpB,IAAI,MAAM,WACN,OAAO,IAAI,CAAC,qBAAqB;QACrC,IAAI,CAAC,qBAAqB,GAAG;QAC7B,OAAO,IAAI;IACf;IACA,kBAAkB,CAAC,EAAE;QACjB,IAAI;QACJ,IAAI,MAAM,WACN,OAAO,IAAI,CAAC,kBAAkB;QAClC,IAAI,CAAC,kBAAkB,GAAG;QAC1B,CAAC,KAAK,IAAI,CAAC,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,CAAC;QACnE,OAAO,IAAI;IACf;IACA,oBAAoB,CAAC,EAAE;QACnB,IAAI;QACJ,IAAI,MAAM,WACN,OAAO,IAAI,CAAC,oBAAoB;QACpC,IAAI,CAAC,oBAAoB,GAAG;QAC5B,CAAC,KAAK,IAAI,CAAC,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,SAAS,CAAC;QACtE,OAAO,IAAI;IACf;IACA,qBAAqB,CAAC,EAAE;QACpB,IAAI;QACJ,IAAI,MAAM,WACN,OAAO,IAAI,CAAC,qBAAqB;QACrC,IAAI,CAAC,qBAAqB,GAAG;QAC7B,CAAC,KAAK,IAAI,CAAC,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,CAAC;QACnE,OAAO,IAAI;IACf;IACA,QAAQ,CAAC,EAAE;QACP,IAAI,CAAC,UAAU,MAAM,EACjB,OAAO,IAAI,CAAC,QAAQ;QACxB,IAAI,CAAC,QAAQ,GAAG;QAChB,OAAO,IAAI;IACf;IACA;;;;;KAKC,GACD,uBAAuB;QACnB,gEAAgE;QAChE,IAAI,CAAC,IAAI,CAAC,aAAa,IACnB,IAAI,CAAC,aAAa,IAClB,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,GAAG;YAC7B,sEAAsE;YACtE,IAAI,CAAC,SAAS;QAClB;IACJ;IACA;;;;;;KAMC,GACD,KAAK,EAAE,EAAE;QACL,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,SAC1B,OAAO,IAAI;QACf,IAAI,CAAC,MAAM,GAAG,IAAI,mKAAA,CAAA,SAAM,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI;QAC5C,MAAM,SAAS,IAAI,CAAC,MAAM;QAC1B,MAAM,OAAO,IAAI;QACjB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,aAAa,GAAG;QACrB,cAAc;QACd,MAAM,iBAAiB,CAAA,GAAA,+JAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,QAAQ;YACtC,KAAK,MAAM;YACX,MAAM;QACV;QACA,MAAM,UAAU,CAAC;YACb,IAAI,CAAC,OAAO;YACZ,IAAI,CAAC,WAAW,GAAG;YACnB,IAAI,CAAC,YAAY,CAAC,SAAS;YAC3B,IAAI,IAAI;gBACJ,GAAG;YACP,OACK;gBACD,qDAAqD;gBACrD,IAAI,CAAC,oBAAoB;YAC7B;QACJ;QACA,eAAe;QACf,MAAM,WAAW,CAAA,GAAA,+JAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,SAAS;QACrC,IAAI,UAAU,IAAI,CAAC,QAAQ,EAAE;YACzB,MAAM,UAAU,IAAI,CAAC,QAAQ;YAC7B,YAAY;YACZ,MAAM,QAAQ,IAAI,CAAC,YAAY,CAAC;gBAC5B;gBACA,QAAQ,IAAI,MAAM;gBAClB,OAAO,KAAK;YAChB,GAAG;YACH,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACrB,MAAM,KAAK;YACf;YACA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;gBACX,IAAI,CAAC,cAAc,CAAC;YACxB;QACJ;QACA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QACf,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QACf,OAAO,IAAI;IACf;IACA;;;;;KAKC,GACD,QAAQ,EAAE,EAAE;QACR,OAAO,IAAI,CAAC,IAAI,CAAC;IACrB;IACA;;;;KAIC,GACD,SAAS;QACL,iBAAiB;QACjB,IAAI,CAAC,OAAO;QACZ,eAAe;QACf,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,YAAY,CAAC;QAClB,eAAe;QACf,MAAM,SAAS,IAAI,CAAC,MAAM;QAC1B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,GAAA,+JAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,CAAA,GAAA,+JAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,CAAA,GAAA,+JAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,SAAS,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAA,GAAA,+JAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,SAAS,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAC/L,aAAa;QACb,CAAA,GAAA,+JAAA,CAAA,KAAE,AAAD,EAAE,IAAI,CAAC,OAAO,EAAE,WAAW,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI;IACxD;IACA;;;;KAIC,GACD,SAAS;QACL,IAAI,CAAC,YAAY,CAAC;IACtB;IACA;;;;KAIC,GACD,OAAO,IAAI,EAAE;QACT,IAAI;YACA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;QACrB,EACA,OAAO,GAAG;YACN,IAAI,CAAC,OAAO,CAAC,eAAe;QAChC;IACJ;IACA;;;;KAIC,GACD,UAAU,MAAM,EAAE;QACd,mIAAmI;QACnI,CAAA,GAAA,oKAAA,CAAA,WAAQ,AAAD,EAAE;YACL,IAAI,CAAC,YAAY,CAAC,UAAU;QAChC,GAAG,IAAI,CAAC,YAAY;IACxB;IACA;;;;KAIC,GACD,QAAQ,GAAG,EAAE;QACT,IAAI,CAAC,YAAY,CAAC,SAAS;IAC/B;IACA;;;;;KAKC,GACD,OAAO,GAAG,EAAE,IAAI,EAAE;QACd,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI;QAC3B,IAAI,CAAC,QAAQ;YACT,SAAS,IAAI,mKAAA,CAAA,SAAM,CAAC,IAAI,EAAE,KAAK;YAC/B,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG;QACrB,OACK,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,OAAO,MAAM,EAAE;YAC1C,OAAO,OAAO;QAClB;QACA,OAAO;IACX;IACA;;;;;KAKC,GACD,SAAS,MAAM,EAAE;QACb,MAAM,OAAO,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI;QAClC,KAAK,MAAM,OAAO,KAAM;YACpB,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI;YAC7B,IAAI,OAAO,MAAM,EAAE;gBACf;YACJ;QACJ;QACA,IAAI,CAAC,MAAM;IACf;IACA;;;;;KAKC,GACD,QAAQ,MAAM,EAAE;QACZ,MAAM,iBAAiB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QAC3C,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;YAC5C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE,EAAE,OAAO,OAAO;QACvD;IACJ;IACA;;;;KAIC,GACD,UAAU;QACN,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,aAAe;QAClC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG;QACnB,IAAI,CAAC,OAAO,CAAC,OAAO;IACxB;IACA;;;;KAIC,GACD,SAAS;QACL,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,OAAO,CAAC;IACjB;IACA;;;;KAIC,GACD,aAAa;QACT,OAAO,IAAI,CAAC,MAAM;IACtB;IACA;;;;;;;;KAQC,GACD,QAAQ,MAAM,EAAE,WAAW,EAAE;QACzB,IAAI;QACJ,IAAI,CAAC,OAAO;QACZ,CAAC,KAAK,IAAI,CAAC,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK;QAChE,IAAI,CAAC,OAAO,CAAC,KAAK;QAClB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,YAAY,CAAC,SAAS,QAAQ;QACnC,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YAC3C,IAAI,CAAC,SAAS;QAClB;IACJ;IACA;;;;KAIC,GACD,YAAY;QACR,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,EACxC,OAAO,IAAI;QACf,MAAM,OAAO,IAAI;QACjB,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,qBAAqB,EAAE;YACrD,IAAI,CAAC,OAAO,CAAC,KAAK;YAClB,IAAI,CAAC,YAAY,CAAC;YAClB,IAAI,CAAC,aAAa,GAAG;QACzB,OACK;YACD,MAAM,QAAQ,IAAI,CAAC,OAAO,CAAC,QAAQ;YACnC,IAAI,CAAC,aAAa,GAAG;YACrB,MAAM,QAAQ,IAAI,CAAC,YAAY,CAAC;gBAC5B,IAAI,KAAK,aAAa,EAClB;gBACJ,IAAI,CAAC,YAAY,CAAC,qBAAqB,KAAK,OAAO,CAAC,QAAQ;gBAC5D,yDAAyD;gBACzD,IAAI,KAAK,aAAa,EAClB;gBACJ,KAAK,IAAI,CAAC,CAAC;oBACP,IAAI,KAAK;wBACL,KAAK,aAAa,GAAG;wBACrB,KAAK,SAAS;wBACd,IAAI,CAAC,YAAY,CAAC,mBAAmB;oBACzC,OACK;wBACD,KAAK,WAAW;oBACpB;gBACJ;YACJ,GAAG;YACH,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACrB,MAAM,KAAK;YACf;YACA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;gBACX,IAAI,CAAC,cAAc,CAAC;YACxB;QACJ;IACJ;IACA;;;;KAIC,GACD,cAAc;QACV,MAAM,UAAU,IAAI,CAAC,OAAO,CAAC,QAAQ;QACrC,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,OAAO,CAAC,KAAK;QAClB,IAAI,CAAC,YAAY,CAAC,aAAa;IACnC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3965, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/socket.io-client/build/esm/index.js"], "sourcesContent": ["import { url } from \"./url.js\";\nimport { Manager } from \"./manager.js\";\nimport { Socket } from \"./socket.js\";\n/**\n * Managers cache.\n */\nconst cache = {};\nfunction lookup(uri, opts) {\n    if (typeof uri === \"object\") {\n        opts = uri;\n        uri = undefined;\n    }\n    opts = opts || {};\n    const parsed = url(uri, opts.path || \"/socket.io\");\n    const source = parsed.source;\n    const id = parsed.id;\n    const path = parsed.path;\n    const sameNamespace = cache[id] && path in cache[id][\"nsps\"];\n    const newConnection = opts.forceNew ||\n        opts[\"force new connection\"] ||\n        false === opts.multiplex ||\n        sameNamespace;\n    let io;\n    if (newConnection) {\n        io = new Manager(source, opts);\n    }\n    else {\n        if (!cache[id]) {\n            cache[id] = new Manager(source, opts);\n        }\n        io = cache[id];\n    }\n    if (parsed.query && !opts.query) {\n        opts.query = parsed.queryKey;\n    }\n    return io.socket(parsed.path, opts);\n}\n// so that \"lookup\" can be used both as a function (e.g. `io(...)`) and as a\n// namespace (e.g. `io.connect(...)`), for backward compatibility\nObject.assign(lookup, {\n    Manager,\n    Socket,\n    io: lookup,\n    connect: lookup,\n});\n/**\n * Protocol version.\n *\n * @public\n */\nexport { protocol } from \"socket.io-parser\";\n/**\n * Expose constructors for standalone build.\n *\n * @public\n */\nexport { Manager, Socket, lookup as io, lookup as connect, lookup as default, };\nexport { Fetch, NodeXHR, XHR, NodeWebSocket, WebSocket, WebTransport, } from \"engine.io-client\";\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AA2CA;;;;CAIC,GACD;AAOA;;;;AAtDA;;CAEC,GACD,MAAM,QAAQ,CAAC;AACf,SAAS,OAAO,GAAG,EAAE,IAAI;IACrB,IAAI,OAAO,QAAQ,UAAU;QACzB,OAAO;QACP,MAAM;IACV;IACA,OAAO,QAAQ,CAAC;IAChB,MAAM,SAAS,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EAAE,KAAK,KAAK,IAAI,IAAI;IACrC,MAAM,SAAS,OAAO,MAAM;IAC5B,MAAM,KAAK,OAAO,EAAE;IACpB,MAAM,OAAO,OAAO,IAAI;IACxB,MAAM,gBAAgB,KAAK,CAAC,GAAG,IAAI,QAAQ,KAAK,CAAC,GAAG,CAAC,OAAO;IAC5D,MAAM,gBAAgB,KAAK,QAAQ,IAC/B,IAAI,CAAC,uBAAuB,IAC5B,UAAU,KAAK,SAAS,IACxB;IACJ,IAAI;IACJ,IAAI,eAAe;QACf,KAAK,IAAI,oKAAA,CAAA,UAAO,CAAC,QAAQ;IAC7B,OACK;QACD,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE;YACZ,KAAK,CAAC,GAAG,GAAG,IAAI,oKAAA,CAAA,UAAO,CAAC,QAAQ;QACpC;QACA,KAAK,KAAK,CAAC,GAAG;IAClB;IACA,IAAI,OAAO,KAAK,IAAI,CAAC,KAAK,KAAK,EAAE;QAC7B,KAAK,KAAK,GAAG,OAAO,QAAQ;IAChC;IACA,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,EAAE;AAClC;AACA,4EAA4E;AAC5E,iEAAiE;AACjE,OAAO,MAAM,CAAC,QAAQ;IAClB,SAAA,oKAAA,CAAA,UAAO;IACP,QAAA,mKAAA,CAAA,SAAM;IACN,IAAI;IACJ,SAAS;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4041, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/%40radix-ui/react-slot/node_modules/%40radix-ui/react-compose-refs/src/compose-refs.tsx"], "sourcesContent": ["import * as React from 'react';\n\ntype PossibleRef<T> = React.Ref<T> | undefined;\n\n/**\n * Set a given ref to a given value\n * This utility takes care of different types of refs: callback refs and RefObject(s)\n */\nfunction setRef<T>(ref: PossibleRef<T>, value: T) {\n  if (typeof ref === 'function') {\n    return ref(value);\n  } else if (ref !== null && ref !== undefined) {\n    ref.current = value;\n  }\n}\n\n/**\n * A utility to compose multiple refs together\n * Accepts callback refs and RefObject(s)\n */\nfunction composeRefs<T>(...refs: PossibleRef<T>[]): React.RefCallback<T> {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == 'function') {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n\n    // React <19 will log an error to the console if a callback ref returns a\n    // value. We don't use ref cleanups internally so this will only happen if a\n    // user's ref callback returns a value, which we only expect if they are\n    // using the cleanup functionality added in React 19.\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == 'function') {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\n\n/**\n * A custom hook that composes multiple refs\n * Accepts callback refs and RefObject(s)\n */\nfunction useComposedRefs<T>(...refs: PossibleRef<T>[]): React.RefCallback<T> {\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  return React.useCallback(composeRefs(...refs), refs);\n}\n\nexport { composeRefs, useComposedRefs };\n"], "names": [], "mappings": ";;;;;AAAA,YAAY,WAAW;;AAQvB,SAAS,OAAU,GAAA,EAAqB,KAAA,EAAU;IAChD,IAAI,OAAO,QAAQ,YAAY;QAC7B,OAAO,IAAI,KAAK;IAClB,OAAA,IAAW,QAAQ,QAAQ,QAAQ,KAAA,GAAW;QAC5C,IAAI,OAAA,GAAU;IAChB;AACF;AAMA,SAAS,YAAA,GAAkB,IAAA,EAA8C;IACvE,OAAO,CAAC,SAAS;QACf,IAAI,aAAa;QACjB,MAAM,WAAW,KAAK,GAAA,CAAI,CAAC,QAAQ;YACjC,MAAM,UAAU,OAAO,KAAK,IAAI;YAChC,IAAI,CAAC,cAAc,OAAO,WAAW,YAAY;gBAC/C,aAAa;YACf;YACA,OAAO;QACT,CAAC;QAMD,IAAI,YAAY;YACd,OAAO,MAAM;gBACX,IAAA,IAAS,IAAI,GAAG,IAAI,SAAS,MAAA,EAAQ,IAAK;oBACxC,MAAM,UAAU,QAAA,CAAS,CAAC,CAAA;oBAC1B,IAAI,OAAO,WAAW,YAAY;wBAChC,QAAQ;oBACV,OAAO;wBACL,OAAO,IAAA,CAAK,CAAC,CAAA,EAAG,IAAI;oBACtB;gBACF;YACF;QACF;IACF;AACF;AAMA,SAAS,gBAAA,GAAsB,IAAA,EAA8C;IAE3E,yKAAa,cAAA,EAAY,YAAY,GAAG,IAAI,GAAG,IAAI;AACrD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4090, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/%40radix-ui/react-slot/src/slot.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeRefs } from '@radix-ui/react-compose-refs';\n\n/* -------------------------------------------------------------------------------------------------\n * Slot\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotProps extends React.HTMLAttributes<HTMLElement> {\n  children?: React.ReactNode;\n}\n\n/* @__NO_SIDE_EFFECTS__ */ export function createSlot(ownerName: string) {\n  const SlotClone = createSlotClone(ownerName);\n  const Slot = React.forwardRef<HTMLElement, SlotProps>((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    const childrenArray = React.Children.toArray(children);\n    const slottable = childrenArray.find(isSlottable);\n\n    if (slottable) {\n      // the new element to render is the one passed as a child of `Slottable`\n      const newElement = slottable.props.children;\n\n      const newChildren = childrenArray.map((child) => {\n        if (child === slottable) {\n          // because the new element will be the one rendered, we are only interested\n          // in grabbing its children (`newElement.props.children`)\n          if (React.Children.count(newElement) > 1) return React.Children.only(null);\n          return React.isValidElement(newElement)\n            ? (newElement.props as { children: React.ReactNode }).children\n            : null;\n        } else {\n          return child;\n        }\n      });\n\n      return (\n        <SlotClone {...slotProps} ref={forwardedRef}>\n          {React.isValidElement(newElement)\n            ? React.cloneElement(newElement, undefined, newChildren)\n            : null}\n        </SlotClone>\n      );\n    }\n\n    return (\n      <SlotClone {...slotProps} ref={forwardedRef}>\n        {children}\n      </SlotClone>\n    );\n  });\n\n  Slot.displayName = `${ownerName}.Slot`;\n  return Slot;\n}\n\nconst Slot = createSlot('Slot');\n\n/* -------------------------------------------------------------------------------------------------\n * SlotClone\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotCloneProps {\n  children: React.ReactNode;\n}\n\n/* @__NO_SIDE_EFFECTS__ */ function createSlotClone(ownerName: string) {\n  const SlotClone = React.forwardRef<any, SlotCloneProps>((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n\n    if (React.isValidElement(children)) {\n      const childrenRef = getElementRef(children);\n      const props = mergeProps(slotProps, children.props as AnyProps);\n      // do not pass ref to React.Fragment for React 19 compatibility\n      if (children.type !== React.Fragment) {\n        props.ref = forwardedRef ? composeRefs(forwardedRef, childrenRef) : childrenRef;\n      }\n      return React.cloneElement(children, props);\n    }\n\n    return React.Children.count(children) > 1 ? React.Children.only(null) : null;\n  });\n\n  SlotClone.displayName = `${ownerName}.SlotClone`;\n  return SlotClone;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * Slottable\n * -----------------------------------------------------------------------------------------------*/\n\nconst SLOTTABLE_IDENTIFIER = Symbol('radix.slottable');\n\ninterface SlottableProps {\n  children: React.ReactNode;\n}\n\ninterface SlottableComponent extends React.FC<SlottableProps> {\n  __radixId: symbol;\n}\n\n/* @__NO_SIDE_EFFECTS__ */ export function createSlottable(ownerName: string) {\n  const Slottable: SlottableComponent = ({ children }) => {\n    return <>{children}</>;\n  };\n  Slottable.displayName = `${ownerName}.Slottable`;\n  Slottable.__radixId = SLOTTABLE_IDENTIFIER;\n  return Slottable;\n}\n\nconst Slottable = createSlottable('Slottable');\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype AnyProps = Record<string, any>;\n\nfunction isSlottable(\n  child: React.ReactNode\n): child is React.ReactElement<SlottableProps, typeof Slottable> {\n  return (\n    React.isValidElement(child) &&\n    typeof child.type === 'function' &&\n    '__radixId' in child.type &&\n    child.type.__radixId === SLOTTABLE_IDENTIFIER\n  );\n}\n\nfunction mergeProps(slotProps: AnyProps, childProps: AnyProps) {\n  // all child props should override\n  const overrideProps = { ...childProps };\n\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      // if the handler exists on both, we compose them\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args: unknown[]) => {\n          const result = childPropValue(...args);\n          slotPropValue(...args);\n          return result;\n        };\n      }\n      // but if it exists only on the slot, we use only this one\n      else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    }\n    // if it's `style`, we merge them\n    else if (propName === 'style') {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === 'className') {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(' ');\n    }\n  }\n\n  return { ...slotProps, ...overrideProps };\n}\n\n// Before React 19 accessing `element.props.ref` will throw a warning and suggest using `element.ref`\n// After React 19 accessing `element.ref` does the opposite.\n// https://github.com/facebook/react/pull/28348\n//\n// Access the ref using the method that doesn't yield a warning.\nfunction getElementRef(element: React.ReactElement) {\n  // React <=18 in DEV\n  let getter = Object.getOwnPropertyDescriptor(element.props, 'ref')?.get;\n  let mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return (element as any).ref;\n  }\n\n  // React 19 in DEV\n  getter = Object.getOwnPropertyDescriptor(element, 'ref')?.get;\n  mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return (element.props as { ref?: React.Ref<unknown> }).ref;\n  }\n\n  // Not DEV\n  return (element.props as { ref?: React.Ref<unknown> }).ref || (element as any).ref;\n}\n\nexport {\n  Slot,\n  Slottable,\n  //\n  Slot as Root,\n};\nexport type { SlotProps };\n"], "names": ["Fragment", "Slot", "props", "Slottable"], "mappings": ";;;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,mBAAmB;AAmCpB,SAkEG,YAAAA,WAlEH;;;;AAAA,uBAAA;AAzB0B,SAAS,WAAW,SAAA,EAAmB;IACvE,MAAM,YAAY,aAAA,GAAA,gBAAgB,SAAS;IAC3C,MAAMC,0KAAa,aAAA,EAAmC,CAAC,OAAO,iBAAiB;QAC7E,MAAM,EAAE,QAAA,EAAU,GAAG,UAAU,CAAA,GAAI;QACnC,MAAM,8KAAsB,WAAA,CAAS,OAAA,CAAQ,QAAQ;QACrD,MAAM,YAAY,cAAc,IAAA,CAAK,WAAW;QAEhD,IAAI,WAAW;YAEb,MAAM,aAAa,UAAU,KAAA,CAAM,QAAA;YAEnC,MAAM,cAAc,cAAc,GAAA,CAAI,CAAC,UAAU;gBAC/C,IAAI,UAAU,WAAW;oBAGvB,IAAU,yKAAA,CAAS,KAAA,CAAM,UAAU,IAAI,EAAG,CAAA,qKAAa,WAAA,CAAS,IAAA,CAAK,IAAI;oBACzE,WAAa,+KAAA,EAAe,UAAU,IACjC,WAAW,KAAA,CAAwC,QAAA,GACpD;gBACN,OAAO;oBACL,OAAO;gBACT;YACF,CAAC;YAED,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,WAAA;gBAAW,GAAG,SAAA;gBAAW,KAAK;gBAC5B,4KAAM,iBAAA,EAAe,UAAU,QACtB,6KAAA,EAAa,YAAY,KAAA,GAAW,WAAW,IACrD;YAAA,CACN;QAEJ;QAEA,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,WAAA;YAAW,GAAG,SAAA;YAAW,KAAK;YAC5B;QAAA,CACH;IAEJ,CAAC;IAEDA,MAAK,WAAA,GAAc,GAAG,SAAS,CAAA,KAAA,CAAA;IAC/B,OAAOA;AACT;AAEA,IAAM,OAAO,aAAA,GAAA,WAAW,MAAM;AAAA,uBAAA;AAUH,SAAS,gBAAgB,SAAA,EAAmB;IACrE,MAAM,6KAAkB,cAAA,EAAgC,CAAC,OAAO,iBAAiB;QAC/E,MAAM,EAAE,QAAA,EAAU,GAAG,UAAU,CAAA,GAAI;QAEnC,QAAU,+KAAA,EAAe,QAAQ,GAAG;YAClC,MAAM,cAAc,cAAc,QAAQ;YAC1C,MAAMC,SAAQ,WAAW,WAAW,SAAS,KAAiB;YAE9D,IAAI,SAAS,IAAA,mKAAe,WAAA,EAAU;gBACpCA,OAAM,GAAA,GAAM,gBAAe,kPAAA,EAAY,cAAc,WAAW,IAAI;YACtE;YACA,yKAAa,eAAA,EAAa,UAAUA,MAAK;QAC3C;QAEA,qKAAa,WAAA,CAAS,KAAA,CAAM,QAAQ,IAAI,kKAAU,WAAA,CAAS,IAAA,CAAK,IAAI,IAAI;IAC1E,CAAC;IAED,UAAU,WAAA,GAAc,GAAG,SAAS,CAAA,UAAA,CAAA;IACpC,OAAO;AACT;AAMA,IAAM,uBAAuB,OAAO,iBAAiB;AAAA,uBAAA;AAUnB,SAAS,gBAAgB,SAAA,EAAmB;IAC5E,MAAMC,aAAgC,CAAC,EAAE,QAAA,CAAS,CAAA,KAAM;QACtD,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,yKAAAH,WAAAA,EAAA;YAAG;QAAA,CAAS;IACrB;IACAG,WAAU,WAAA,GAAc,GAAG,SAAS,CAAA,UAAA,CAAA;IACpCA,WAAU,SAAA,GAAY;IACtB,OAAOA;AACT;AAEA,IAAM,YAAY,aAAA,GAAA,gBAAgB,WAAW;AAM7C,SAAS,YACP,KAAA,EAC+D;IAC/D,QACQ,kLAAA,EAAe,KAAK,KAC1B,OAAO,MAAM,IAAA,KAAS,cACtB,eAAe,MAAM,IAAA,IACrB,MAAM,IAAA,CAAK,SAAA,KAAc;AAE7B;AAEA,SAAS,WAAW,SAAA,EAAqB,UAAA,EAAsB;IAE7D,MAAM,gBAAgB;QAAE,GAAG,UAAA;IAAW;IAEtC,IAAA,MAAW,YAAY,WAAY;QACjC,MAAM,gBAAgB,SAAA,CAAU,QAAQ,CAAA;QACxC,MAAM,iBAAiB,UAAA,CAAW,QAAQ,CAAA;QAE1C,MAAM,YAAY,WAAW,IAAA,CAAK,QAAQ;QAC1C,IAAI,WAAW;YAEb,IAAI,iBAAiB,gBAAgB;gBACnC,aAAA,CAAc,QAAQ,CAAA,GAAI,CAAA,GAAI,SAAoB;oBAChD,MAAM,SAAS,eAAe,GAAG,IAAI;oBACrC,cAAc,GAAG,IAAI;oBACrB,OAAO;gBACT;YACF,OAAA,IAES,eAAe;gBACtB,aAAA,CAAc,QAAQ,CAAA,GAAI;YAC5B;QACF,OAAA,IAES,aAAa,SAAS;YAC7B,aAAA,CAAc,QAAQ,CAAA,GAAI;gBAAE,GAAG,aAAA;gBAAe,GAAG,cAAA;YAAe;QAClE,OAAA,IAAW,aAAa,aAAa;YACnC,aAAA,CAAc,QAAQ,CAAA,GAAI;gBAAC;gBAAe,cAAc;aAAA,CAAE,MAAA,CAAO,OAAO,EAAE,IAAA,CAAK,GAAG;QACpF;IACF;IAEA,OAAO;QAAE,GAAG,SAAA;QAAW,GAAG,aAAA;IAAc;AAC1C;AAOA,SAAS,cAAc,OAAA,EAA6B;IAElD,IAAI,SAAS,OAAO,wBAAA,CAAyB,QAAQ,KAAA,EAAO,KAAK,GAAG;IACpE,IAAI,UAAU,UAAU,oBAAoB,UAAU,OAAO,cAAA;IAC7D,IAAI,SAAS;QACX,OAAQ,QAAgB,GAAA;IAC1B;IAGA,SAAS,OAAO,wBAAA,CAAyB,SAAS,KAAK,GAAG;IAC1D,UAAU,UAAU,oBAAoB,UAAU,OAAO,cAAA;IACzD,IAAI,SAAS;QACX,OAAQ,QAAQ,KAAA,CAAuC,GAAA;IACzD;IAGA,OAAQ,QAAQ,KAAA,CAAuC,GAAA,IAAQ,QAAgB,GAAA;AACjF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4226, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/%40radix-ui/react-label/src/label.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * Label\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'Label';\n\ntype LabelElement = React.ElementRef<typeof Primitive.label>;\ntype PrimitiveLabelProps = React.ComponentPropsWithoutRef<typeof Primitive.label>;\ninterface LabelProps extends PrimitiveLabelProps {}\n\nconst Label = React.forwardRef<LabelElement, LabelProps>((props, forwardedRef) => {\n  return (\n    <Primitive.label\n      {...props}\n      ref={forwardedRef}\n      onMouseDown={(event) => {\n        // only prevent text selection if clicking inside the label itself\n        const target = event.target as HTMLElement;\n        if (target.closest('button, input, select, textarea')) return;\n\n        props.onMouseDown?.(event);\n        // prevent text selection when double clicking label\n        if (!event.defaultPrevented && event.detail > 1) event.preventDefault();\n      }}\n    />\n  );\n});\n\nLabel.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Label;\n\nexport {\n  Label,\n  //\n  Root,\n};\nexport type { LabelProps };\n"], "names": [], "mappings": ";;;;;AAAA,YAAY,WAAW;AACvB,SAAS,iBAAiB;AActB;;;;;AARJ,IAAM,OAAO;AAMb,IAAM,0KAAc,aAAA,EAAqC,CAAC,OAAO,iBAAiB;IAChF,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,KAAA,EAAV;QACE,GAAG,KAAA;QACJ,KAAK;QACL,aAAa,CAAC,UAAU;YAEtB,MAAM,SAAS,MAAM,MAAA;YACrB,IAAI,OAAO,OAAA,CAAQ,iCAAiC,EAAG,CAAA;YAEvD,MAAM,WAAA,GAAc,KAAK;YAEzB,IAAI,CAAC,MAAM,gBAAA,IAAoB,MAAM,MAAA,GAAS,EAAG,CAAA,MAAM,cAAA,CAAe;QACxE;IAAA;AAGN,CAAC;AAED,MAAM,WAAA,GAAc;AAIpB,IAAM,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4261, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/%40radix-ui/number/src/number.ts"], "sourcesContent": ["function clamp(value: number, [min, max]: [number, number]): number {\n  return Math.min(max, Math.max(min, value));\n}\n\nexport { clamp };\n"], "names": [], "mappings": ";;;;AAAA,SAAS,MAAM,KAAA,EAAe,CAAC,KAAK,GAAG,CAAA,EAA6B;IAClE,OAAO,KAAK,GAAA,CAAI,KAAK,KAAK,GAAA,CAAI,KAAK,KAAK,CAAC;AAC3C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4276, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/%40radix-ui/react-direction/src/Direction.tsx"], "sourcesContent": ["import * as React from 'react';\n\ntype Direction = 'ltr' | 'rtl';\nconst DirectionContext = React.createContext<Direction | undefined>(undefined);\n\n/* -------------------------------------------------------------------------------------------------\n * Direction\n * -----------------------------------------------------------------------------------------------*/\n\ninterface DirectionProviderProps {\n  children?: React.ReactNode;\n  dir: Direction;\n}\nconst DirectionProvider: React.FC<DirectionProviderProps> = (props) => {\n  const { dir, children } = props;\n  return <DirectionContext.Provider value={dir}>{children}</DirectionContext.Provider>;\n};\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction useDirection(localDir?: Direction) {\n  const globalDir = React.useContext(DirectionContext);\n  return localDir || globalDir || 'ltr';\n}\n\nconst Provider = DirectionProvider;\n\nexport {\n  useDirection,\n  //\n  Provider,\n  //\n  DirectionProvider,\n};\n"], "names": [], "mappings": ";;;;;;AAAA,YAAY,WAAW;AAed;;;AAZT,IAAM,qLAAyB,gBAAA,EAAqC,KAAA,CAAS;AAU7E,IAAM,oBAAsD,CAAC,UAAU;IACrE,MAAM,EAAE,GAAA,EAAK,QAAA,CAAS,CAAA,GAAI;IAC1B,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,iBAAiB,QAAA,EAAjB;QAA0B,OAAO;QAAM;IAAA,CAAS;AAC1D;AAIA,SAAS,aAAa,QAAA,EAAsB;IAC1C,MAAM,8KAAkB,aAAA,EAAW,gBAAgB;IACnD,OAAO,YAAY,aAAa;AAClC;AAEA,IAAM,WAAW", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4307, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/%40radix-ui/react-use-previous/src/usePrevious.tsx"], "sourcesContent": ["import * as React from 'react';\n\nfunction usePrevious<T>(value: T) {\n  const ref = React.useRef({ value, previous: value });\n\n  // We compare values before making an update to ensure that\n  // a change has been made. This ensures the previous value is\n  // persisted correctly between renders.\n  return React.useMemo(() => {\n    if (ref.current.value !== value) {\n      ref.current.previous = ref.current.value;\n      ref.current.value = value;\n    }\n    return ref.current.previous;\n  }, [value]);\n}\n\nexport { usePrevious };\n"], "names": [], "mappings": ";;;;AAAA,YAAY,WAAW;;AAEvB,SAAS,YAAe,KAAA,EAAU;IAChC,MAAM,wKAAY,SAAA,EAAO;QAAE;QAAO,UAAU;IAAM,CAAC;IAKnD,yKAAa,UAAA;+BAAQ,MAAM;YACzB,IAAI,IAAI,OAAA,CAAQ,KAAA,KAAU,OAAO;gBAC/B,IAAI,OAAA,CAAQ,QAAA,GAAW,IAAI,OAAA,CAAQ,KAAA;gBACnC,IAAI,OAAA,CAAQ,KAAA,GAAQ;YACtB;YACA,OAAO,IAAI,OAAA,CAAQ,QAAA;QACrB;8BAAG;QAAC,KAAK;KAAC;AACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4338, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/%40radix-ui/react-use-size/src/useSize.tsx"], "sourcesContent": ["/// <reference types=\"resize-observer-browser\" />\n\nimport * as React from 'react';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\n\nfunction useSize(element: HTMLElement | null) {\n  const [size, setSize] = React.useState<{ width: number; height: number } | undefined>(undefined);\n\n  useLayoutEffect(() => {\n    if (element) {\n      // provide size as early as possible\n      setSize({ width: element.offsetWidth, height: element.offsetHeight });\n\n      const resizeObserver = new ResizeObserver((entries) => {\n        if (!Array.isArray(entries)) {\n          return;\n        }\n\n        // Since we only observe the one element, we don't need to loop over the\n        // array\n        if (!entries.length) {\n          return;\n        }\n\n        const entry = entries[0];\n        let width: number;\n        let height: number;\n\n        if ('borderBoxSize' in entry) {\n          const borderSizeEntry = entry['borderBoxSize'];\n          // iron out differences between browsers\n          const borderSize = Array.isArray(borderSizeEntry) ? borderSizeEntry[0] : borderSizeEntry;\n          width = borderSize['inlineSize'];\n          height = borderSize['blockSize'];\n        } else {\n          // for browsers that don't support `borderBoxSize`\n          // we calculate it ourselves to get the correct border box.\n          width = element.offsetWidth;\n          height = element.offsetHeight;\n        }\n\n        setSize({ width, height });\n      });\n\n      resizeObserver.observe(element, { box: 'border-box' });\n\n      return () => resizeObserver.unobserve(element);\n    } else {\n      // We only want to reset to `undefined` when the element becomes `null`,\n      // not if it changes to another element.\n      setSize(undefined);\n    }\n  }, [element]);\n\n  return size;\n}\n\nexport { useSize };\n"], "names": [], "mappings": ";;;;AAEA,YAAY,WAAW;AACvB,SAAS,uBAAuB;;;AAEhC,SAAS,QAAQ,OAAA,EAA6B;IAC5C,MAAM,CAAC,MAAM,OAAO,CAAA,qKAAU,WAAA,EAAwD,KAAA,CAAS;IAE/F,CAAA,GAAA,sLAAA,CAAA,kBAAA;mCAAgB,MAAM;YACpB,IAAI,SAAS;gBAEX,QAAQ;oBAAE,OAAO,QAAQ,WAAA;oBAAa,QAAQ,QAAQ,YAAA;gBAAa,CAAC;gBAEpE,MAAM,iBAAiB,IAAI;+CAAe,CAAC,YAAY;wBACrD,IAAI,CAAC,MAAM,OAAA,CAAQ,OAAO,GAAG;4BAC3B;wBACF;wBAIA,IAAI,CAAC,QAAQ,MAAA,EAAQ;4BACnB;wBACF;wBAEA,MAAM,QAAQ,OAAA,CAAQ,CAAC,CAAA;wBACvB,IAAI;wBACJ,IAAI;wBAEJ,IAAI,mBAAmB,OAAO;4BAC5B,MAAM,kBAAkB,KAAA,CAAM,eAAe,CAAA;4BAE7C,MAAM,aAAa,MAAM,OAAA,CAAQ,eAAe,IAAI,eAAA,CAAgB,CAAC,CAAA,GAAI;4BACzE,QAAQ,UAAA,CAAW,YAAY,CAAA;4BAC/B,SAAS,UAAA,CAAW,WAAW,CAAA;wBACjC,OAAO;4BAGL,QAAQ,QAAQ,WAAA;4BAChB,SAAS,QAAQ,YAAA;wBACnB;wBAEA,QAAQ;4BAAE;4BAAO;wBAAO,CAAC;oBAC3B,CAAC;;gBAED,eAAe,OAAA,CAAQ,SAAS;oBAAE,KAAK;gBAAa,CAAC;gBAErD;+CAAO,IAAM,eAAe,SAAA,CAAU,OAAO;;YAC/C,OAAO;gBAGL,QAAQ,KAAA,CAAS;YACnB;QACF;kCAAG;QAAC,OAAO;KAAC;IAEZ,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4404, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/%40radix-ui/react-slider/src/slider.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { clamp } from '@radix-ui/number';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { usePrevious } from '@radix-ui/react-use-previous';\nimport { useSize } from '@radix-ui/react-use-size';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { createCollection } from '@radix-ui/react-collection';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\n\nconst PAGE_KEYS = ['PageUp', 'PageDown'];\nconst ARROW_KEYS = ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'];\n\ntype SlideDirection = 'from-left' | 'from-right' | 'from-bottom' | 'from-top';\nconst BACK_KEYS: Record<SlideDirection, string[]> = {\n  'from-left': ['Home', 'PageDown', 'ArrowDown', 'ArrowLeft'],\n  'from-right': ['Home', 'PageDown', 'ArrowDown', 'ArrowRight'],\n  'from-bottom': ['Home', 'PageDown', 'ArrowDown', 'ArrowLeft'],\n  'from-top': ['Home', 'PageDown', 'ArrowUp', 'ArrowLeft'],\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Slider\n * -----------------------------------------------------------------------------------------------*/\n\nconst SLIDER_NAME = 'Slider';\n\nconst [Collection, useCollection, createCollectionScope] =\n  createCollection<SliderThumbElement>(SLIDER_NAME);\n\ntype ScopedProps<P> = P & { __scopeSlider?: Scope };\nconst [createSliderContext, createSliderScope] = createContextScope(SLIDER_NAME, [\n  createCollectionScope,\n]);\n\ntype SliderContextValue = {\n  name: string | undefined;\n  disabled: boolean | undefined;\n  min: number;\n  max: number;\n  values: number[];\n  valueIndexToChangeRef: React.MutableRefObject<number>;\n  thumbs: Set<SliderThumbElement>;\n  orientation: SliderProps['orientation'];\n  form: string | undefined;\n};\n\nconst [SliderProvider, useSliderContext] = createSliderContext<SliderContextValue>(SLIDER_NAME);\n\ntype SliderElement = SliderHorizontalElement | SliderVerticalElement;\ninterface SliderProps\n  extends Omit<\n    SliderHorizontalProps | SliderVerticalProps,\n    keyof SliderOrientationPrivateProps | 'defaultValue'\n  > {\n  name?: string;\n  disabled?: boolean;\n  orientation?: React.AriaAttributes['aria-orientation'];\n  dir?: Direction;\n  min?: number;\n  max?: number;\n  step?: number;\n  minStepsBetweenThumbs?: number;\n  value?: number[];\n  defaultValue?: number[];\n  onValueChange?(value: number[]): void;\n  onValueCommit?(value: number[]): void;\n  inverted?: boolean;\n  form?: string;\n}\n\nconst Slider = React.forwardRef<SliderElement, SliderProps>(\n  (props: ScopedProps<SliderProps>, forwardedRef) => {\n    const {\n      name,\n      min = 0,\n      max = 100,\n      step = 1,\n      orientation = 'horizontal',\n      disabled = false,\n      minStepsBetweenThumbs = 0,\n      defaultValue = [min],\n      value,\n      onValueChange = () => {},\n      onValueCommit = () => {},\n      inverted = false,\n      form,\n      ...sliderProps\n    } = props;\n    const thumbRefs = React.useRef<SliderContextValue['thumbs']>(new Set());\n    const valueIndexToChangeRef = React.useRef<number>(0);\n    const isHorizontal = orientation === 'horizontal';\n    const SliderOrientation = isHorizontal ? SliderHorizontal : SliderVertical;\n\n    const [values = [], setValues] = useControllableState({\n      prop: value,\n      defaultProp: defaultValue,\n      onChange: (value) => {\n        const thumbs = [...thumbRefs.current];\n        thumbs[valueIndexToChangeRef.current]?.focus();\n        onValueChange(value);\n      },\n    });\n    const valuesBeforeSlideStartRef = React.useRef(values);\n\n    function handleSlideStart(value: number) {\n      const closestIndex = getClosestValueIndex(values, value);\n      updateValues(value, closestIndex);\n    }\n\n    function handleSlideMove(value: number) {\n      updateValues(value, valueIndexToChangeRef.current);\n    }\n\n    function handleSlideEnd() {\n      const prevValue = valuesBeforeSlideStartRef.current[valueIndexToChangeRef.current];\n      const nextValue = values[valueIndexToChangeRef.current];\n      const hasChanged = nextValue !== prevValue;\n      if (hasChanged) onValueCommit(values);\n    }\n\n    function updateValues(value: number, atIndex: number, { commit } = { commit: false }) {\n      const decimalCount = getDecimalCount(step);\n      const snapToStep = roundValue(Math.round((value - min) / step) * step + min, decimalCount);\n      const nextValue = clamp(snapToStep, [min, max]);\n\n      setValues((prevValues = []) => {\n        const nextValues = getNextSortedValues(prevValues, nextValue, atIndex);\n        if (hasMinStepsBetweenValues(nextValues, minStepsBetweenThumbs * step)) {\n          valueIndexToChangeRef.current = nextValues.indexOf(nextValue);\n          const hasChanged = String(nextValues) !== String(prevValues);\n          if (hasChanged && commit) onValueCommit(nextValues);\n          return hasChanged ? nextValues : prevValues;\n        } else {\n          return prevValues;\n        }\n      });\n    }\n\n    return (\n      <SliderProvider\n        scope={props.__scopeSlider}\n        name={name}\n        disabled={disabled}\n        min={min}\n        max={max}\n        valueIndexToChangeRef={valueIndexToChangeRef}\n        thumbs={thumbRefs.current}\n        values={values}\n        orientation={orientation}\n        form={form}\n      >\n        <Collection.Provider scope={props.__scopeSlider}>\n          <Collection.Slot scope={props.__scopeSlider}>\n            <SliderOrientation\n              aria-disabled={disabled}\n              data-disabled={disabled ? '' : undefined}\n              {...sliderProps}\n              ref={forwardedRef}\n              onPointerDown={composeEventHandlers(sliderProps.onPointerDown, () => {\n                if (!disabled) valuesBeforeSlideStartRef.current = values;\n              })}\n              min={min}\n              max={max}\n              inverted={inverted}\n              onSlideStart={disabled ? undefined : handleSlideStart}\n              onSlideMove={disabled ? undefined : handleSlideMove}\n              onSlideEnd={disabled ? undefined : handleSlideEnd}\n              onHomeKeyDown={() => !disabled && updateValues(min, 0, { commit: true })}\n              onEndKeyDown={() =>\n                !disabled && updateValues(max, values.length - 1, { commit: true })\n              }\n              onStepKeyDown={({ event, direction: stepDirection }) => {\n                if (!disabled) {\n                  const isPageKey = PAGE_KEYS.includes(event.key);\n                  const isSkipKey = isPageKey || (event.shiftKey && ARROW_KEYS.includes(event.key));\n                  const multiplier = isSkipKey ? 10 : 1;\n                  const atIndex = valueIndexToChangeRef.current;\n                  const value = values[atIndex];\n                  const stepInDirection = step * multiplier * stepDirection;\n                  updateValues(value + stepInDirection, atIndex, { commit: true });\n                }\n              }}\n            />\n          </Collection.Slot>\n        </Collection.Provider>\n      </SliderProvider>\n    );\n  }\n);\n\nSlider.displayName = SLIDER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SliderHorizontal\n * -----------------------------------------------------------------------------------------------*/\n\ntype Side = 'top' | 'right' | 'bottom' | 'left';\n\nconst [SliderOrientationProvider, useSliderOrientationContext] = createSliderContext<{\n  startEdge: Side;\n  endEdge: Side;\n  size: keyof NonNullable<ReturnType<typeof useSize>>;\n  direction: number;\n}>(SLIDER_NAME, {\n  startEdge: 'left',\n  endEdge: 'right',\n  size: 'width',\n  direction: 1,\n});\n\ntype SliderOrientationPrivateProps = {\n  min: number;\n  max: number;\n  inverted: boolean;\n  onSlideStart?(value: number): void;\n  onSlideMove?(value: number): void;\n  onSlideEnd?(): void;\n  onHomeKeyDown(event: React.KeyboardEvent): void;\n  onEndKeyDown(event: React.KeyboardEvent): void;\n  onStepKeyDown(step: { event: React.KeyboardEvent; direction: number }): void;\n};\ninterface SliderOrientationProps\n  extends Omit<SliderImplProps, keyof SliderImplPrivateProps>,\n    SliderOrientationPrivateProps {}\n\ntype SliderHorizontalElement = SliderImplElement;\ninterface SliderHorizontalProps extends SliderOrientationProps {\n  dir?: Direction;\n}\n\nconst SliderHorizontal = React.forwardRef<SliderHorizontalElement, SliderHorizontalProps>(\n  (props: ScopedProps<SliderHorizontalProps>, forwardedRef) => {\n    const {\n      min,\n      max,\n      dir,\n      inverted,\n      onSlideStart,\n      onSlideMove,\n      onSlideEnd,\n      onStepKeyDown,\n      ...sliderProps\n    } = props;\n    const [slider, setSlider] = React.useState<SliderImplElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setSlider(node));\n    const rectRef = React.useRef<DOMRect>(undefined);\n    const direction = useDirection(dir);\n    const isDirectionLTR = direction === 'ltr';\n    const isSlidingFromLeft = (isDirectionLTR && !inverted) || (!isDirectionLTR && inverted);\n\n    function getValueFromPointer(pointerPosition: number) {\n      const rect = rectRef.current || slider!.getBoundingClientRect();\n      const input: [number, number] = [0, rect.width];\n      const output: [number, number] = isSlidingFromLeft ? [min, max] : [max, min];\n      const value = linearScale(input, output);\n\n      rectRef.current = rect;\n      return value(pointerPosition - rect.left);\n    }\n\n    return (\n      <SliderOrientationProvider\n        scope={props.__scopeSlider}\n        startEdge={isSlidingFromLeft ? 'left' : 'right'}\n        endEdge={isSlidingFromLeft ? 'right' : 'left'}\n        direction={isSlidingFromLeft ? 1 : -1}\n        size=\"width\"\n      >\n        <SliderImpl\n          dir={direction}\n          data-orientation=\"horizontal\"\n          {...sliderProps}\n          ref={composedRefs}\n          style={{\n            ...sliderProps.style,\n            ['--radix-slider-thumb-transform' as any]: 'translateX(-50%)',\n          }}\n          onSlideStart={(event) => {\n            const value = getValueFromPointer(event.clientX);\n            onSlideStart?.(value);\n          }}\n          onSlideMove={(event) => {\n            const value = getValueFromPointer(event.clientX);\n            onSlideMove?.(value);\n          }}\n          onSlideEnd={() => {\n            rectRef.current = undefined;\n            onSlideEnd?.();\n          }}\n          onStepKeyDown={(event) => {\n            const slideDirection = isSlidingFromLeft ? 'from-left' : 'from-right';\n            const isBackKey = BACK_KEYS[slideDirection].includes(event.key);\n            onStepKeyDown?.({ event, direction: isBackKey ? -1 : 1 });\n          }}\n        />\n      </SliderOrientationProvider>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * SliderVertical\n * -----------------------------------------------------------------------------------------------*/\n\ntype SliderVerticalElement = SliderImplElement;\ninterface SliderVerticalProps extends SliderOrientationProps {}\n\nconst SliderVertical = React.forwardRef<SliderVerticalElement, SliderVerticalProps>(\n  (props: ScopedProps<SliderVerticalProps>, forwardedRef) => {\n    const {\n      min,\n      max,\n      inverted,\n      onSlideStart,\n      onSlideMove,\n      onSlideEnd,\n      onStepKeyDown,\n      ...sliderProps\n    } = props;\n    const sliderRef = React.useRef<SliderImplElement>(null);\n    const ref = useComposedRefs(forwardedRef, sliderRef);\n    const rectRef = React.useRef<DOMRect>(undefined);\n    const isSlidingFromBottom = !inverted;\n\n    function getValueFromPointer(pointerPosition: number) {\n      const rect = rectRef.current || sliderRef.current!.getBoundingClientRect();\n      const input: [number, number] = [0, rect.height];\n      const output: [number, number] = isSlidingFromBottom ? [max, min] : [min, max];\n      const value = linearScale(input, output);\n\n      rectRef.current = rect;\n      return value(pointerPosition - rect.top);\n    }\n\n    return (\n      <SliderOrientationProvider\n        scope={props.__scopeSlider}\n        startEdge={isSlidingFromBottom ? 'bottom' : 'top'}\n        endEdge={isSlidingFromBottom ? 'top' : 'bottom'}\n        size=\"height\"\n        direction={isSlidingFromBottom ? 1 : -1}\n      >\n        <SliderImpl\n          data-orientation=\"vertical\"\n          {...sliderProps}\n          ref={ref}\n          style={{\n            ...sliderProps.style,\n            ['--radix-slider-thumb-transform' as any]: 'translateY(50%)',\n          }}\n          onSlideStart={(event) => {\n            const value = getValueFromPointer(event.clientY);\n            onSlideStart?.(value);\n          }}\n          onSlideMove={(event) => {\n            const value = getValueFromPointer(event.clientY);\n            onSlideMove?.(value);\n          }}\n          onSlideEnd={() => {\n            rectRef.current = undefined;\n            onSlideEnd?.();\n          }}\n          onStepKeyDown={(event) => {\n            const slideDirection = isSlidingFromBottom ? 'from-bottom' : 'from-top';\n            const isBackKey = BACK_KEYS[slideDirection].includes(event.key);\n            onStepKeyDown?.({ event, direction: isBackKey ? -1 : 1 });\n          }}\n        />\n      </SliderOrientationProvider>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * SliderImpl\n * -----------------------------------------------------------------------------------------------*/\n\ntype SliderImplElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ntype SliderImplPrivateProps = {\n  onSlideStart(event: React.PointerEvent): void;\n  onSlideMove(event: React.PointerEvent): void;\n  onSlideEnd(event: React.PointerEvent): void;\n  onHomeKeyDown(event: React.KeyboardEvent): void;\n  onEndKeyDown(event: React.KeyboardEvent): void;\n  onStepKeyDown(event: React.KeyboardEvent): void;\n};\ninterface SliderImplProps extends PrimitiveDivProps, SliderImplPrivateProps {}\n\nconst SliderImpl = React.forwardRef<SliderImplElement, SliderImplProps>(\n  (props: ScopedProps<SliderImplProps>, forwardedRef) => {\n    const {\n      __scopeSlider,\n      onSlideStart,\n      onSlideMove,\n      onSlideEnd,\n      onHomeKeyDown,\n      onEndKeyDown,\n      onStepKeyDown,\n      ...sliderProps\n    } = props;\n    const context = useSliderContext(SLIDER_NAME, __scopeSlider);\n\n    return (\n      <Primitive.span\n        {...sliderProps}\n        ref={forwardedRef}\n        onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n          if (event.key === 'Home') {\n            onHomeKeyDown(event);\n            // Prevent scrolling to page start\n            event.preventDefault();\n          } else if (event.key === 'End') {\n            onEndKeyDown(event);\n            // Prevent scrolling to page end\n            event.preventDefault();\n          } else if (PAGE_KEYS.concat(ARROW_KEYS).includes(event.key)) {\n            onStepKeyDown(event);\n            // Prevent scrolling for directional key presses\n            event.preventDefault();\n          }\n        })}\n        onPointerDown={composeEventHandlers(props.onPointerDown, (event) => {\n          const target = event.target as HTMLElement;\n          target.setPointerCapture(event.pointerId);\n          // Prevent browser focus behaviour because we focus a thumb manually when values change.\n          event.preventDefault();\n          // Touch devices have a delay before focusing so won't focus if touch immediately moves\n          // away from target (sliding). We want thumb to focus regardless.\n          if (context.thumbs.has(target)) {\n            target.focus();\n          } else {\n            onSlideStart(event);\n          }\n        })}\n        onPointerMove={composeEventHandlers(props.onPointerMove, (event) => {\n          const target = event.target as HTMLElement;\n          if (target.hasPointerCapture(event.pointerId)) onSlideMove(event);\n        })}\n        onPointerUp={composeEventHandlers(props.onPointerUp, (event) => {\n          const target = event.target as HTMLElement;\n          if (target.hasPointerCapture(event.pointerId)) {\n            target.releasePointerCapture(event.pointerId);\n            onSlideEnd(event);\n          }\n        })}\n      />\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * SliderTrack\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRACK_NAME = 'SliderTrack';\n\ntype SliderTrackElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface SliderTrackProps extends PrimitiveSpanProps {}\n\nconst SliderTrack = React.forwardRef<SliderTrackElement, SliderTrackProps>(\n  (props: ScopedProps<SliderTrackProps>, forwardedRef) => {\n    const { __scopeSlider, ...trackProps } = props;\n    const context = useSliderContext(TRACK_NAME, __scopeSlider);\n    return (\n      <Primitive.span\n        data-disabled={context.disabled ? '' : undefined}\n        data-orientation={context.orientation}\n        {...trackProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nSliderTrack.displayName = TRACK_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SliderRange\n * -----------------------------------------------------------------------------------------------*/\n\nconst RANGE_NAME = 'SliderRange';\n\ntype SliderRangeElement = React.ElementRef<typeof Primitive.span>;\ninterface SliderRangeProps extends PrimitiveSpanProps {}\n\nconst SliderRange = React.forwardRef<SliderRangeElement, SliderRangeProps>(\n  (props: ScopedProps<SliderRangeProps>, forwardedRef) => {\n    const { __scopeSlider, ...rangeProps } = props;\n    const context = useSliderContext(RANGE_NAME, __scopeSlider);\n    const orientation = useSliderOrientationContext(RANGE_NAME, __scopeSlider);\n    const ref = React.useRef<HTMLSpanElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const valuesCount = context.values.length;\n    const percentages = context.values.map((value) =>\n      convertValueToPercentage(value, context.min, context.max)\n    );\n    const offsetStart = valuesCount > 1 ? Math.min(...percentages) : 0;\n    const offsetEnd = 100 - Math.max(...percentages);\n\n    return (\n      <Primitive.span\n        data-orientation={context.orientation}\n        data-disabled={context.disabled ? '' : undefined}\n        {...rangeProps}\n        ref={composedRefs}\n        style={{\n          ...props.style,\n          [orientation.startEdge]: offsetStart + '%',\n          [orientation.endEdge]: offsetEnd + '%',\n        }}\n      />\n    );\n  }\n);\n\nSliderRange.displayName = RANGE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SliderThumb\n * -----------------------------------------------------------------------------------------------*/\n\nconst THUMB_NAME = 'SliderThumb';\n\ntype SliderThumbElement = SliderThumbImplElement;\ninterface SliderThumbProps extends Omit<SliderThumbImplProps, 'index'> {}\n\nconst SliderThumb = React.forwardRef<SliderThumbElement, SliderThumbProps>(\n  (props: ScopedProps<SliderThumbProps>, forwardedRef) => {\n    const getItems = useCollection(props.__scopeSlider);\n    const [thumb, setThumb] = React.useState<SliderThumbImplElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setThumb(node));\n    const index = React.useMemo(\n      () => (thumb ? getItems().findIndex((item) => item.ref.current === thumb) : -1),\n      [getItems, thumb]\n    );\n    return <SliderThumbImpl {...props} ref={composedRefs} index={index} />;\n  }\n);\n\ntype SliderThumbImplElement = React.ElementRef<typeof Primitive.span>;\ninterface SliderThumbImplProps extends PrimitiveSpanProps {\n  index: number;\n  name?: string;\n}\n\nconst SliderThumbImpl = React.forwardRef<SliderThumbImplElement, SliderThumbImplProps>(\n  (props: ScopedProps<SliderThumbImplProps>, forwardedRef) => {\n    const { __scopeSlider, index, name, ...thumbProps } = props;\n    const context = useSliderContext(THUMB_NAME, __scopeSlider);\n    const orientation = useSliderOrientationContext(THUMB_NAME, __scopeSlider);\n    const [thumb, setThumb] = React.useState<HTMLSpanElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setThumb(node));\n    // We set this to true by default so that events bubble to forms without JS (SSR)\n    const isFormControl = thumb ? context.form || !!thumb.closest('form') : true;\n    const size = useSize(thumb);\n    // We cast because index could be `-1` which would return undefined\n    const value = context.values[index] as number | undefined;\n    const percent =\n      value === undefined ? 0 : convertValueToPercentage(value, context.min, context.max);\n    const label = getLabel(index, context.values.length);\n    const orientationSize = size?.[orientation.size];\n    const thumbInBoundsOffset = orientationSize\n      ? getThumbInBoundsOffset(orientationSize, percent, orientation.direction)\n      : 0;\n\n    React.useEffect(() => {\n      if (thumb) {\n        context.thumbs.add(thumb);\n        return () => {\n          context.thumbs.delete(thumb);\n        };\n      }\n    }, [thumb, context.thumbs]);\n\n    return (\n      <span\n        style={{\n          transform: 'var(--radix-slider-thumb-transform)',\n          position: 'absolute',\n          [orientation.startEdge]: `calc(${percent}% + ${thumbInBoundsOffset}px)`,\n        }}\n      >\n        <Collection.ItemSlot scope={props.__scopeSlider}>\n          <Primitive.span\n            role=\"slider\"\n            aria-label={props['aria-label'] || label}\n            aria-valuemin={context.min}\n            aria-valuenow={value}\n            aria-valuemax={context.max}\n            aria-orientation={context.orientation}\n            data-orientation={context.orientation}\n            data-disabled={context.disabled ? '' : undefined}\n            tabIndex={context.disabled ? undefined : 0}\n            {...thumbProps}\n            ref={composedRefs}\n            /**\n             * There will be no value on initial render while we work out the index so we hide thumbs\n             * without a value, otherwise SSR will render them in the wrong position before they\n             * snap into the correct position during hydration which would be visually jarring for\n             * slower connections.\n             */\n            style={value === undefined ? { display: 'none' } : props.style}\n            onFocus={composeEventHandlers(props.onFocus, () => {\n              context.valueIndexToChangeRef.current = index;\n            })}\n          />\n        </Collection.ItemSlot>\n\n        {isFormControl && (\n          <BubbleInput\n            key={index}\n            name={\n              name ??\n              (context.name ? context.name + (context.values.length > 1 ? '[]' : '') : undefined)\n            }\n            form={context.form}\n            value={value}\n          />\n        )}\n      </span>\n    );\n  }\n);\n\nSliderThumb.displayName = THUMB_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst BubbleInput = (props: React.ComponentPropsWithoutRef<'input'>) => {\n  const { value, ...inputProps } = props;\n  const ref = React.useRef<HTMLInputElement>(null);\n  const prevValue = usePrevious(value);\n\n  // Bubble value change to parents (e.g form change event)\n  React.useEffect(() => {\n    const input = ref.current!;\n    const inputProto = window.HTMLInputElement.prototype;\n    const descriptor = Object.getOwnPropertyDescriptor(inputProto, 'value') as PropertyDescriptor;\n    const setValue = descriptor.set;\n    if (prevValue !== value && setValue) {\n      const event = new Event('input', { bubbles: true });\n      setValue.call(input, value);\n      input.dispatchEvent(event);\n    }\n  }, [prevValue, value]);\n\n  /**\n   * We purposefully do not use `type=\"hidden\"` here otherwise forms that\n   * wrap it will not be able to access its value via the FormData API.\n   *\n   * We purposefully do not add the `value` attribute here to allow the value\n   * to be set programmatically and bubble to any parent form `onChange` event.\n   * Adding the `value` will cause React to consider the programmatic\n   * dispatch a duplicate and it will get swallowed.\n   */\n  return <input style={{ display: 'none' }} {...inputProps} ref={ref} defaultValue={value} />;\n};\n\nfunction getNextSortedValues(prevValues: number[] = [], nextValue: number, atIndex: number) {\n  const nextValues = [...prevValues];\n  nextValues[atIndex] = nextValue;\n  return nextValues.sort((a, b) => a - b);\n}\n\nfunction convertValueToPercentage(value: number, min: number, max: number) {\n  const maxSteps = max - min;\n  const percentPerStep = 100 / maxSteps;\n  const percentage = percentPerStep * (value - min);\n  return clamp(percentage, [0, 100]);\n}\n\n/**\n * Returns a label for each thumb when there are two or more thumbs\n */\nfunction getLabel(index: number, totalValues: number) {\n  if (totalValues > 2) {\n    return `Value ${index + 1} of ${totalValues}`;\n  } else if (totalValues === 2) {\n    return ['Minimum', 'Maximum'][index];\n  } else {\n    return undefined;\n  }\n}\n\n/**\n * Given a `values` array and a `nextValue`, determine which value in\n * the array is closest to `nextValue` and return its index.\n *\n * @example\n * // returns 1\n * getClosestValueIndex([10, 30], 25);\n */\nfunction getClosestValueIndex(values: number[], nextValue: number) {\n  if (values.length === 1) return 0;\n  const distances = values.map((value) => Math.abs(value - nextValue));\n  const closestDistance = Math.min(...distances);\n  return distances.indexOf(closestDistance);\n}\n\n/**\n * Offsets the thumb centre point while sliding to ensure it remains\n * within the bounds of the slider when reaching the edges\n */\nfunction getThumbInBoundsOffset(width: number, left: number, direction: number) {\n  const halfWidth = width / 2;\n  const halfPercent = 50;\n  const offset = linearScale([0, halfPercent], [0, halfWidth]);\n  return (halfWidth - offset(left) * direction) * direction;\n}\n\n/**\n * Gets an array of steps between each value.\n *\n * @example\n * // returns [1, 9]\n * getStepsBetweenValues([10, 11, 20]);\n */\nfunction getStepsBetweenValues(values: number[]) {\n  return values.slice(0, -1).map((value, index) => values[index + 1] - value);\n}\n\n/**\n * Verifies the minimum steps between all values is greater than or equal\n * to the expected minimum steps.\n *\n * @example\n * // returns false\n * hasMinStepsBetweenValues([1,2,3], 2);\n *\n * @example\n * // returns true\n * hasMinStepsBetweenValues([1,2,3], 1);\n */\nfunction hasMinStepsBetweenValues(values: number[], minStepsBetweenValues: number) {\n  if (minStepsBetweenValues > 0) {\n    const stepsBetweenValues = getStepsBetweenValues(values);\n    const actualMinStepsBetweenValues = Math.min(...stepsBetweenValues);\n    return actualMinStepsBetweenValues >= minStepsBetweenValues;\n  }\n  return true;\n}\n\n// https://github.com/tmcw-up-for-adoption/simple-linear-scale/blob/master/index.js\nfunction linearScale(input: readonly [number, number], output: readonly [number, number]) {\n  return (value: number) => {\n    if (input[0] === input[1] || output[0] === output[1]) return output[0];\n    const ratio = (output[1] - output[0]) / (input[1] - input[0]);\n    return output[0] + ratio * (value - input[0]);\n  };\n}\n\nfunction getDecimalCount(value: number) {\n  return (String(value).split('.')[1] || '').length;\n}\n\nfunction roundValue(value: number, decimalCount: number) {\n  const rounder = Math.pow(10, decimalCount);\n  return Math.round(value * rounder) / rounder;\n}\n\nconst Root = Slider;\nconst Track = SliderTrack;\nconst Range = SliderRange;\nconst Thumb = SliderThumb;\n\nexport {\n  createSliderScope,\n  //\n  Slider,\n  SliderTrack,\n  SliderRange,\n  SliderThumb,\n  //\n  Root,\n  Track,\n  Range,\n  Thumb,\n};\nexport type { SliderProps, SliderTrackProps, SliderRangeProps, SliderThumbProps };\n"], "names": ["value"], "mappings": ";;;;;;;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,aAAa;AACtB,SAAS,4BAA4B;AACrC,SAAS,uBAAuB;AAChC,SAAS,0BAA0B;AACnC,SAAS,4BAA4B;AACrC,SAAS,oBAAoB;AAC7B,SAAS,mBAAmB;AAC5B,SAAS,eAAe;AACxB,SAAS,iBAAiB;AAC1B,SAAS,wBAAwB;AAsJrB,cAwaN,YAxaM;;;;;;;;;;;;;;AAhJZ,IAAM,YAAY;IAAC;IAAU,UAAU;CAAA;AACvC,IAAM,aAAa;IAAC;IAAW;IAAa;IAAa,YAAY;CAAA;AAGrE,IAAM,YAA8C;IAClD,aAAa;QAAC;QAAQ;QAAY;QAAa,WAAW;KAAA;IAC1D,cAAc;QAAC;QAAQ;QAAY;QAAa,YAAY;KAAA;IAC5D,eAAe;QAAC;QAAQ;QAAY;QAAa,WAAW;KAAA;IAC5D,YAAY;QAAC;QAAQ;QAAY;QAAW,WAAW;KAAA;AACzD;AAMA,IAAM,cAAc;AAEpB,IAAM,CAAC,YAAY,eAAe,qBAAqB,CAAA,iLACrD,mBAAA,EAAqC,WAAW;AAGlD,IAAM,CAAC,qBAAqB,iBAAiB,CAAA,6KAAI,sBAAA,EAAmB,aAAa;IAC/E;CACD;AAcD,IAAM,CAAC,gBAAgB,gBAAgB,CAAA,GAAI,oBAAwC,WAAW;AAwB9F,IAAM,2KAAe,aAAA,EACnB,CAAC,OAAiC,iBAAiB;IACjD,MAAM,EACJ,IAAA,EACA,MAAM,CAAA,EACN,MAAM,GAAA,EACN,OAAO,CAAA,EACP,cAAc,YAAA,EACd,WAAW,KAAA,EACX,wBAAwB,CAAA,EACxB,eAAe;QAAC,GAAG;KAAA,EACnB,KAAA,EACA,gBAAgB,KAAO,CAAD,AAAC,EACvB,gBAAgB,KAAO,CAAD,AAAC,EACvB,WAAW,KAAA,EACX,IAAA,EACA,GAAG,aACL,GAAI;IACJ,MAAM,8KAAkB,SAAA,EAAqC,aAAA,GAAA,IAAI,IAAI,CAAC;IACtE,MAAM,0LAA8B,SAAA,EAAe,CAAC;IACpD,MAAM,eAAe,gBAAgB;IACrC,MAAM,oBAAoB,eAAe,mBAAmB;IAE5D,MAAM,CAAC,SAAS,CAAC,CAAA,EAAG,SAAS,CAAA,mMAAI,uBAAA,EAAqB;QACpD,MAAM;QACN,aAAa;QACb,QAAA;2CAAU,CAACA,WAAU;gBACnB,MAAM,SAAS,CAAC;uBAAG,UAAU,OAAO;iBAAA;gBACpC,MAAA,CAAO,sBAAsB,OAAO,CAAA,EAAG,MAAM;gBAC7C,cAAcA,MAAK;YACrB;;IACF,CAAC;IACD,MAAM,8LAAkC,SAAA,EAAO,MAAM;IAErD,SAAS,iBAAiBA,MAAAA,EAAe;QACvC,MAAM,eAAe,qBAAqB,QAAQA,MAAK;QACvD,aAAaA,QAAO,YAAY;IAClC;IAEA,SAAS,gBAAgBA,MAAAA,EAAe;QACtC,aAAaA,QAAO,sBAAsB,OAAO;IACnD;IAEA,SAAS,iBAAiB;QACxB,MAAM,YAAY,0BAA0B,OAAA,CAAQ,sBAAsB,OAAO,CAAA;QACjF,MAAM,YAAY,MAAA,CAAO,sBAAsB,OAAO,CAAA;QACtD,MAAM,aAAa,cAAc;QACjC,IAAI,WAAY,CAAA,cAAc,MAAM;IACtC;IAEA,SAAS,aAAaA,MAAAA,EAAe,OAAA,EAAiB,EAAE,MAAA,CAAO,CAAA,GAAI;QAAE,QAAQ;IAAM,CAAA,EAAG;QACpF,MAAM,eAAe,gBAAgB,IAAI;QACzC,MAAM,aAAa,WAAW,KAAK,KAAA,CAAA,CAAOA,SAAQ,GAAA,IAAO,IAAI,IAAI,OAAO,KAAK,YAAY;QACzF,MAAM,6KAAY,QAAA,EAAM,YAAY;YAAC;YAAK,GAAG;SAAC;QAE9C,UAAU,CAAC,aAAa,CAAC,CAAA,KAAM;YAC7B,MAAM,aAAa,oBAAoB,YAAY,WAAW,OAAO;YACrE,IAAI,yBAAyB,YAAY,wBAAwB,IAAI,GAAG;gBACtE,sBAAsB,OAAA,GAAU,WAAW,OAAA,CAAQ,SAAS;gBAC5D,MAAM,aAAa,OAAO,UAAU,MAAM,OAAO,UAAU;gBAC3D,IAAI,cAAc,OAAQ,CAAA,cAAc,UAAU;gBAClD,OAAO,aAAa,aAAa;YACnC,OAAO;gBACL,OAAO;YACT;QACF,CAAC;IACH;IAEA,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,gBAAA;QACC,OAAO,MAAM,aAAA;QACb;QACA;QACA;QACA;QACA;QACA,QAAQ,UAAU,OAAA;QAClB;QACA;QACA;QAEA,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,WAAW,QAAA,EAAX;YAAoB,OAAO,MAAM,aAAA;YAChC,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,WAAW,IAAA,EAAX;gBAAgB,OAAO,MAAM,aAAA;gBAC5B,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,mBAAA;oBACC,iBAAe;oBACf,iBAAe,WAAW,KAAK,KAAA;oBAC9B,GAAG,WAAA;oBACJ,KAAK;oBACL,mLAAe,uBAAA,EAAqB,YAAY,aAAA,EAAe,MAAM;wBACnE,IAAI,CAAC,SAAU,CAAA,0BAA0B,OAAA,GAAU;oBACrD,CAAC;oBACD;oBACA;oBACA;oBACA,cAAc,WAAW,KAAA,IAAY;oBACrC,aAAa,WAAW,KAAA,IAAY;oBACpC,YAAY,WAAW,KAAA,IAAY;oBACnC,eAAe,IAAM,CAAC,YAAY,aAAa,KAAK,GAAG;4BAAE,QAAQ;wBAAK,CAAC;oBACvE,cAAc,IACZ,CAAC,YAAY,aAAa,KAAK,OAAO,MAAA,GAAS,GAAG;4BAAE,QAAQ;wBAAK,CAAC;oBAEpE,eAAe,CAAC,EAAE,KAAA,EAAO,WAAW,aAAA,CAAc,CAAA,KAAM;wBACtD,IAAI,CAAC,UAAU;4BACb,MAAM,YAAY,UAAU,QAAA,CAAS,MAAM,GAAG;4BAC9C,MAAM,YAAY,aAAc,MAAM,QAAA,IAAY,WAAW,QAAA,CAAS,MAAM,GAAG;4BAC/E,MAAM,aAAa,YAAY,KAAK;4BACpC,MAAM,UAAU,sBAAsB,OAAA;4BACtC,MAAMA,SAAQ,MAAA,CAAO,OAAO,CAAA;4BAC5B,MAAM,kBAAkB,OAAO,aAAa;4BAC5C,aAAaA,SAAQ,iBAAiB,SAAS;gCAAE,QAAQ;4BAAK,CAAC;wBACjE;oBACF;gBAAA;YACF,CACF;QAAA,CACF;IAAA;AAGN;AAGF,OAAO,WAAA,GAAc;AAQrB,IAAM,CAAC,2BAA2B,2BAA2B,CAAA,GAAI,oBAK9D,aAAa;IACd,WAAW;IACX,SAAS;IACT,MAAM;IACN,WAAW;AACb,CAAC;AAsBD,IAAM,oLAAyB,cAAA,EAC7B,CAAC,OAA2C,iBAAiB;IAC3D,MAAM,EACJ,GAAA,EACA,GAAA,EACA,GAAA,EACA,QAAA,EACA,YAAA,EACA,WAAA,EACA,UAAA,EACA,aAAA,EACA,GAAG,aACL,GAAI;IACJ,MAAM,CAAC,QAAQ,SAAS,CAAA,qKAAU,WAAA,EAAmC,IAAI;IACzE,MAAM,gBAAe,oMAAA,EAAgB;0DAAc,CAAC,OAAS,UAAU,IAAI,CAAC;;IAC5E,MAAM,4KAAgB,SAAA,EAAgB,KAAA,CAAS;IAC/C,MAAM,yLAAY,eAAA,EAAa,GAAG;IAClC,MAAM,iBAAiB,cAAc;IACrC,MAAM,oBAAqB,kBAAkB,CAAC,YAAc,CAAC,kBAAkB;IAE/E,SAAS,oBAAoB,eAAA,EAAyB;QACpD,MAAM,OAAO,QAAQ,OAAA,IAAW,OAAQ,qBAAA,CAAsB;QAC9D,MAAM,QAA0B;YAAC;YAAG,KAAK,KAAK;SAAA;QAC9C,MAAM,SAA2B,oBAAoB;YAAC;YAAK,GAAG;SAAA,GAAI;YAAC;YAAK,GAAG;SAAA;QAC3E,MAAM,QAAQ,YAAY,OAAO,MAAM;QAEvC,QAAQ,OAAA,GAAU;QAClB,OAAO,MAAM,kBAAkB,KAAK,IAAI;IAC1C;IAEA,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,2BAAA;QACC,OAAO,MAAM,aAAA;QACb,WAAW,oBAAoB,SAAS;QACxC,SAAS,oBAAoB,UAAU;QACvC,WAAW,oBAAoB,IAAI,CAAA;QACnC,MAAK;QAEL,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,YAAA;YACC,KAAK;YACL,oBAAiB;YAChB,GAAG,WAAA;YACJ,KAAK;YACL,OAAO;gBACL,GAAG,YAAY,KAAA;gBACf,CAAC,gCAAuC,CAAA,EAAG;YAC7C;YACA,cAAc,CAAC,UAAU;gBACvB,MAAM,QAAQ,oBAAoB,MAAM,OAAO;gBAC/C,eAAe,KAAK;YACtB;YACA,aAAa,CAAC,UAAU;gBACtB,MAAM,QAAQ,oBAAoB,MAAM,OAAO;gBAC/C,cAAc,KAAK;YACrB;YACA,YAAY,MAAM;gBAChB,QAAQ,OAAA,GAAU,KAAA;gBAClB,aAAa;YACf;YACA,eAAe,CAAC,UAAU;gBACxB,MAAM,iBAAiB,oBAAoB,cAAc;gBACzD,MAAM,YAAY,SAAA,CAAU,cAAc,CAAA,CAAE,QAAA,CAAS,MAAM,GAAG;gBAC9D,gBAAgB;oBAAE;oBAAO,WAAW,YAAY,CAAA,IAAK;gBAAE,CAAC;YAC1D;QAAA;IACF;AAGN;AAUF,IAAM,mLAAuB,aAAA,EAC3B,CAAC,OAAyC,iBAAiB;IACzD,MAAM,EACJ,GAAA,EACA,GAAA,EACA,QAAA,EACA,YAAA,EACA,WAAA,EACA,UAAA,EACA,aAAA,EACA,GAAG,aACL,GAAI;IACJ,MAAM,8KAAkB,SAAA,EAA0B,IAAI;IACtD,MAAM,wLAAM,mBAAA,EAAgB,cAAc,SAAS;IACnD,MAAM,4KAAgB,SAAA,EAAgB,KAAA,CAAS;IAC/C,MAAM,sBAAsB,CAAC;IAE7B,SAAS,oBAAoB,eAAA,EAAyB;QACpD,MAAM,OAAO,QAAQ,OAAA,IAAW,UAAU,OAAA,CAAS,qBAAA,CAAsB;QACzE,MAAM,QAA0B;YAAC;YAAG,KAAK,MAAM;SAAA;QAC/C,MAAM,SAA2B,sBAAsB;YAAC;YAAK,GAAG;SAAA,GAAI;YAAC;YAAK,GAAG;SAAA;QAC7E,MAAM,QAAQ,YAAY,OAAO,MAAM;QAEvC,QAAQ,OAAA,GAAU;QAClB,OAAO,MAAM,kBAAkB,KAAK,GAAG;IACzC;IAEA,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,2BAAA;QACC,OAAO,MAAM,aAAA;QACb,WAAW,sBAAsB,WAAW;QAC5C,SAAS,sBAAsB,QAAQ;QACvC,MAAK;QACL,WAAW,sBAAsB,IAAI,CAAA;QAErC,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,YAAA;YACC,oBAAiB;YAChB,GAAG,WAAA;YACJ;YACA,OAAO;gBACL,GAAG,YAAY,KAAA;gBACf,CAAC,gCAAuC,CAAA,EAAG;YAC7C;YACA,cAAc,CAAC,UAAU;gBACvB,MAAM,QAAQ,oBAAoB,MAAM,OAAO;gBAC/C,eAAe,KAAK;YACtB;YACA,aAAa,CAAC,UAAU;gBACtB,MAAM,QAAQ,oBAAoB,MAAM,OAAO;gBAC/C,cAAc,KAAK;YACrB;YACA,YAAY,MAAM;gBAChB,QAAQ,OAAA,GAAU,KAAA;gBAClB,aAAa;YACf;YACA,eAAe,CAAC,UAAU;gBACxB,MAAM,iBAAiB,sBAAsB,gBAAgB;gBAC7D,MAAM,YAAY,SAAA,CAAU,cAAc,CAAA,CAAE,QAAA,CAAS,MAAM,GAAG;gBAC9D,gBAAgB;oBAAE;oBAAO,WAAW,YAAY,CAAA,IAAK;gBAAE,CAAC;YAC1D;QAAA;IACF;AAGN;AAmBF,IAAM,+KAAmB,aAAA,EACvB,CAAC,OAAqC,iBAAiB;IACrD,MAAM,EACJ,aAAA,EACA,YAAA,EACA,WAAA,EACA,UAAA,EACA,aAAA,EACA,YAAA,EACA,aAAA,EACA,GAAG,aACL,GAAI;IACJ,MAAM,UAAU,iBAAiB,aAAa,aAAa;IAE3D,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,IAAA,EAAV;QACE,GAAG,WAAA;QACJ,KAAK;QACL,8KAAW,wBAAA,EAAqB,MAAM,SAAA,EAAW,CAAC,UAAU;YAC1D,IAAI,MAAM,GAAA,KAAQ,QAAQ;gBACxB,cAAc,KAAK;gBAEnB,MAAM,cAAA,CAAe;YACvB,OAAA,IAAW,MAAM,GAAA,KAAQ,OAAO;gBAC9B,aAAa,KAAK;gBAElB,MAAM,cAAA,CAAe;YACvB,OAAA,IAAW,UAAU,MAAA,CAAO,UAAU,EAAE,QAAA,CAAS,MAAM,GAAG,GAAG;gBAC3D,cAAc,KAAK;gBAEnB,MAAM,cAAA,CAAe;YACvB;QACF,CAAC;QACD,kLAAe,wBAAA,EAAqB,MAAM,aAAA,EAAe,CAAC,UAAU;YAClE,MAAM,SAAS,MAAM,MAAA;YACrB,OAAO,iBAAA,CAAkB,MAAM,SAAS;YAExC,MAAM,cAAA,CAAe;YAGrB,IAAI,QAAQ,MAAA,CAAO,GAAA,CAAI,MAAM,GAAG;gBAC9B,OAAO,KAAA,CAAM;YACf,OAAO;gBACL,aAAa,KAAK;YACpB;QACF,CAAC;QACD,mLAAe,uBAAA,EAAqB,MAAM,aAAA,EAAe,CAAC,UAAU;YAClE,MAAM,SAAS,MAAM,MAAA;YACrB,IAAI,OAAO,iBAAA,CAAkB,MAAM,SAAS,EAAG,CAAA,YAAY,KAAK;QAClE,CAAC;QACD,iLAAa,uBAAA,EAAqB,MAAM,WAAA,EAAa,CAAC,UAAU;YAC9D,MAAM,SAAS,MAAM,MAAA;YACrB,IAAI,OAAO,iBAAA,CAAkB,MAAM,SAAS,GAAG;gBAC7C,OAAO,qBAAA,CAAsB,MAAM,SAAS;gBAC5C,WAAW,KAAK;YAClB;QACF,CAAC;IAAA;AAGP;AAOF,IAAM,aAAa;AAMnB,IAAM,cAAoB,+KAAA,EACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,aAAA,EAAe,GAAG,WAAW,CAAA,GAAI;IACzC,MAAM,UAAU,iBAAiB,YAAY,aAAa;IAC1D,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,IAAA,EAAV;QACC,iBAAe,QAAQ,QAAA,GAAW,KAAK,KAAA;QACvC,oBAAkB,QAAQ,WAAA;QACzB,GAAG,UAAA;QACJ,KAAK;IAAA;AAGX;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,aAAa;AAKnB,IAAM,eAAoB,8KAAA,EACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,aAAA,EAAe,GAAG,WAAW,CAAA,GAAI;IACzC,MAAM,UAAU,iBAAiB,YAAY,aAAa;IAC1D,MAAM,cAAc,4BAA4B,YAAY,aAAa;IACzE,MAAM,wKAAY,SAAA,EAAwB,IAAI;IAC9C,MAAM,kMAAe,kBAAA,EAAgB,cAAc,GAAG;IACtD,MAAM,cAAc,QAAQ,MAAA,CAAO,MAAA;IACnC,MAAM,cAAc,QAAQ,MAAA,CAAO,GAAA,CAAI,CAAC,QACtC,yBAAyB,OAAO,QAAQ,GAAA,EAAK,QAAQ,GAAG;IAE1D,MAAM,cAAc,cAAc,IAAI,KAAK,GAAA,CAAI,GAAG,WAAW,IAAI;IACjE,MAAM,YAAY,MAAM,KAAK,GAAA,CAAI,GAAG,WAAW;IAE/C,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,IAAA,EAAV;QACC,oBAAkB,QAAQ,WAAA;QAC1B,iBAAe,QAAQ,QAAA,GAAW,KAAK,KAAA;QACtC,GAAG,UAAA;QACJ,KAAK;QACL,OAAO;YACL,GAAG,MAAM,KAAA;YACT,CAAC,YAAY,SAAS,CAAA,EAAG,cAAc;YACvC,CAAC,YAAY,OAAO,CAAA,EAAG,YAAY;QACrC;IAAA;AAGN;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,aAAa;AAKnB,IAAM,gLAAoB,aAAA,EACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,WAAW,cAAc,MAAM,aAAa;IAClD,MAAM,CAAC,OAAO,QAAQ,CAAA,qKAAU,WAAA,EAAwC,IAAI;IAC5E,MAAM,eAAe,qMAAA,EAAgB;qDAAc,CAAC,OAAS,SAAS,IAAI,CAAC;;IAC3E,MAAM,0KAAc,UAAA;sCAClB,IAAO,QAAQ,SAAS,EAAE,SAAA;8CAAU,CAAC,OAAS,KAAK,GAAA,CAAI,OAAA,KAAY,KAAK;+CAAI,CAAA;qCAC5E;QAAC;QAAU,KAAK;KAAA;IAElB,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,iBAAA;QAAiB,GAAG,KAAA;QAAO,KAAK;QAAc;IAAA,CAAc;AACtE;AASF,IAAM,mLAAwB,cAAA,EAC5B,CAAC,OAA0C,iBAAiB;IAC1D,MAAM,EAAE,aAAA,EAAe,KAAA,EAAO,IAAA,EAAM,GAAG,WAAW,CAAA,GAAI;IACtD,MAAM,UAAU,iBAAiB,YAAY,aAAa;IAC1D,MAAM,cAAc,4BAA4B,YAAY,aAAa;IACzE,MAAM,CAAC,OAAO,QAAQ,CAAA,oKAAU,YAAA,EAAiC,IAAI;IACrE,MAAM,kMAAe,kBAAA,EAAgB;yDAAc,CAAC,OAAS,SAAS,IAAI,CAAC;;IAE3E,MAAM,gBAAgB,QAAQ,QAAQ,IAAA,IAAQ,CAAC,CAAC,MAAM,OAAA,CAAQ,MAAM,IAAI;IACxE,MAAM,sLAAO,UAAA,EAAQ,KAAK;IAE1B,MAAM,QAAQ,QAAQ,MAAA,CAAO,KAAK,CAAA;IAClC,MAAM,UACJ,UAAU,KAAA,IAAY,IAAI,yBAAyB,OAAO,QAAQ,GAAA,EAAK,QAAQ,GAAG;IACpF,MAAM,QAAQ,SAAS,OAAO,QAAQ,MAAA,CAAO,MAAM;IACnD,MAAM,kBAAkB,MAAA,CAAO,YAAY,IAAI,CAAA;IAC/C,MAAM,sBAAsB,kBACxB,uBAAuB,iBAAiB,SAAS,YAAY,SAAS,IACtE;qKAEE,aAAA;qCAAU,MAAM;YACpB,IAAI,OAAO;gBACT,QAAQ,MAAA,CAAO,GAAA,CAAI,KAAK;gBACxB;iDAAO,MAAM;wBACX,QAAQ,MAAA,CAAO,MAAA,CAAO,KAAK;oBAC7B;;YACF;QACF;oCAAG;QAAC;QAAO,QAAQ,MAAM;KAAC;IAE1B,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,OAAA,EAAC,QAAA;QACC,OAAO;YACL,WAAW;YACX,UAAU;YACV,CAAC,YAAY,SAAS,CAAA,EAAG,CAAA,KAAA,EAAQ,OAAO,CAAA,IAAA,EAAO,mBAAmB,CAAA,GAAA,CAAA;QACpE;QAEA,UAAA;YAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,WAAW,QAAA,EAAX;gBAAoB,OAAO,MAAM,aAAA;gBAChC,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,IAAA,EAAV;oBACC,MAAK;oBACL,cAAY,KAAA,CAAM,YAAY,CAAA,IAAK;oBACnC,iBAAe,QAAQ,GAAA;oBACvB,iBAAe;oBACf,iBAAe,QAAQ,GAAA;oBACvB,oBAAkB,QAAQ,WAAA;oBAC1B,oBAAkB,QAAQ,WAAA;oBAC1B,iBAAe,QAAQ,QAAA,GAAW,KAAK,KAAA;oBACvC,UAAU,QAAQ,QAAA,GAAW,KAAA,IAAY;oBACxC,GAAG,UAAA;oBACJ,KAAK;oBAOL,OAAO,UAAU,KAAA,IAAY;wBAAE,SAAS;oBAAO,IAAI,MAAM,KAAA;oBACzD,6KAAS,uBAAA,EAAqB,MAAM,OAAA,EAAS,MAAM;wBACjD,QAAQ,qBAAA,CAAsB,OAAA,GAAU;oBAC1C,CAAC;gBAAA;YACH,CACF;YAEC,iBACC,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,aAAA;gBAEC,MACE,QAAA,CACC,QAAQ,IAAA,GAAO,QAAQ,IAAA,GAAA,CAAQ,QAAQ,MAAA,CAAO,MAAA,GAAS,IAAI,OAAO,EAAA,IAAM,KAAA,CAAA;gBAE3E,MAAM,QAAQ,IAAA;gBACd;YAAA,GANK;SAOP;IAAA;AAIR;AAGF,YAAY,WAAA,GAAc;AAI1B,IAAM,cAAc,CAAC,UAAmD;IACtE,MAAM,EAAE,KAAA,EAAO,GAAG,WAAW,CAAA,GAAI;IACjC,MAAM,wKAAY,SAAA,EAAyB,IAAI;IAC/C,MAAM,YAAY,iMAAA,EAAY,KAAK;sKAG7B,YAAA;iCAAU,MAAM;YACpB,MAAM,QAAQ,IAAI,OAAA;YAClB,MAAM,aAAa,OAAO,gBAAA,CAAiB,SAAA;YAC3C,MAAM,aAAa,OAAO,wBAAA,CAAyB,YAAY,OAAO;YACtE,MAAM,WAAW,WAAW,GAAA;YAC5B,IAAI,cAAc,SAAS,UAAU;gBACnC,MAAM,QAAQ,IAAI,MAAM,SAAS;oBAAE,SAAS;gBAAK,CAAC;gBAClD,SAAS,IAAA,CAAK,OAAO,KAAK;gBAC1B,MAAM,aAAA,CAAc,KAAK;YAC3B;QACF;gCAAG;QAAC;QAAW,KAAK;KAAC;IAWrB,OAAO,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,SAAA;QAAM,OAAO;YAAE,SAAS;QAAO;QAAI,GAAG,UAAA;QAAY;QAAU,cAAc;IAAA,CAAO;AAC3F;AAEA,SAAS,oBAAoB,aAAuB,CAAC,CAAA,EAAG,SAAA,EAAmB,OAAA,EAAiB;IAC1F,MAAM,aAAa,CAAC;WAAG,UAAU;KAAA;IACjC,UAAA,CAAW,OAAO,CAAA,GAAI;IACtB,OAAO,WAAW,IAAA,CAAK,CAAC,GAAG,IAAM,IAAI,CAAC;AACxC;AAEA,SAAS,yBAAyB,KAAA,EAAe,GAAA,EAAa,GAAA,EAAa;IACzE,MAAM,WAAW,MAAM;IACvB,MAAM,iBAAiB,MAAM;IAC7B,MAAM,aAAa,iBAAA,CAAkB,QAAQ,GAAA;IAC7C,WAAO,qKAAA,EAAM,YAAY;QAAC;QAAG,GAAG;KAAC;AACnC;AAKA,SAAS,SAAS,KAAA,EAAe,WAAA,EAAqB;IACpD,IAAI,cAAc,GAAG;QACnB,OAAO,CAAA,MAAA,EAAS,QAAQ,CAAC,CAAA,IAAA,EAAO,WAAW,EAAA;IAC7C,OAAA,IAAW,gBAAgB,GAAG;QAC5B,OAAO;YAAC;YAAW,SAAS;SAAA,CAAE,KAAK,CAAA;IACrC,OAAO;QACL,OAAO,KAAA;IACT;AACF;AAUA,SAAS,qBAAqB,MAAA,EAAkB,SAAA,EAAmB;IACjE,IAAI,OAAO,MAAA,KAAW,EAAG,CAAA,OAAO;IAChC,MAAM,YAAY,OAAO,GAAA,CAAI,CAAC,QAAU,KAAK,GAAA,CAAI,QAAQ,SAAS,CAAC;IACnE,MAAM,kBAAkB,KAAK,GAAA,CAAI,GAAG,SAAS;IAC7C,OAAO,UAAU,OAAA,CAAQ,eAAe;AAC1C;AAMA,SAAS,uBAAuB,KAAA,EAAe,IAAA,EAAc,SAAA,EAAmB;IAC9E,MAAM,YAAY,QAAQ;IAC1B,MAAM,cAAc;IACpB,MAAM,SAAS,YAAY;QAAC;QAAG,WAAW;KAAA,EAAG;QAAC;QAAG,SAAS;KAAC;IAC3D,OAAA,CAAQ,YAAY,OAAO,IAAI,IAAI,SAAA,IAAa;AAClD;AASA,SAAS,sBAAsB,MAAA,EAAkB;IAC/C,OAAO,OAAO,KAAA,CAAM,GAAG,CAAA,CAAE,EAAE,GAAA,CAAI,CAAC,OAAO,QAAU,MAAA,CAAO,QAAQ,CAAC,CAAA,GAAI,KAAK;AAC5E;AAcA,SAAS,yBAAyB,MAAA,EAAkB,qBAAA,EAA+B;IACjF,IAAI,wBAAwB,GAAG;QAC7B,MAAM,qBAAqB,sBAAsB,MAAM;QACvD,MAAM,8BAA8B,KAAK,GAAA,CAAI,GAAG,kBAAkB;QAClE,OAAO,+BAA+B;IACxC;IACA,OAAO;AACT;AAGA,SAAS,YAAY,KAAA,EAAkC,MAAA,EAAmC;IACxF,OAAO,CAAC,UAAkB;QACxB,IAAI,KAAA,CAAM,CAAC,CAAA,KAAM,KAAA,CAAM,CAAC,CAAA,IAAK,MAAA,CAAO,CAAC,CAAA,KAAM,MAAA,CAAO,CAAC,CAAA,CAAG,CAAA,OAAO,MAAA,CAAO,CAAC,CAAA;QACrE,MAAM,QAAA,CAAS,MAAA,CAAO,CAAC,CAAA,GAAI,MAAA,CAAO,CAAC,CAAA,IAAA,CAAM,KAAA,CAAM,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA;QAC3D,OAAO,MAAA,CAAO,CAAC,CAAA,GAAI,QAAA,CAAS,QAAQ,KAAA,CAAM,CAAC,CAAA;IAC7C;AACF;AAEA,SAAS,gBAAgB,KAAA,EAAe;IACtC,OAAA,CAAQ,OAAO,KAAK,EAAE,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAA,IAAK,EAAA,EAAI,MAAA;AAC7C;AAEA,SAAS,WAAW,KAAA,EAAe,YAAA,EAAsB;IACvD,MAAM,UAAU,KAAK,GAAA,CAAI,IAAI,YAAY;IACzC,OAAO,KAAK,KAAA,CAAM,QAAQ,OAAO,IAAI;AACvC;AAEA,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAM,QAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5004, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/%40radix-ui/react-switch/src/switch.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { usePrevious } from '@radix-ui/react-use-previous';\nimport { useSize } from '@radix-ui/react-use-size';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Switch\n * -----------------------------------------------------------------------------------------------*/\n\nconst SWITCH_NAME = 'Switch';\n\ntype ScopedProps<P> = P & { __scopeSwitch?: Scope };\nconst [createSwitchContext, createSwitchScope] = createContextScope(SWITCH_NAME);\n\ntype SwitchContextValue = { checked: boolean; disabled?: boolean };\nconst [SwitchProvider, useSwitchContext] = createSwitchContext<SwitchContextValue>(SWITCH_NAME);\n\ntype SwitchElement = React.ElementRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface SwitchProps extends PrimitiveButtonProps {\n  checked?: boolean;\n  defaultChecked?: boolean;\n  required?: boolean;\n  onCheckedChange?(checked: boolean): void;\n}\n\nconst Switch = React.forwardRef<SwitchElement, SwitchProps>(\n  (props: ScopedProps<SwitchProps>, forwardedRef) => {\n    const {\n      __scopeSwitch,\n      name,\n      checked: checkedProp,\n      defaultChecked,\n      required,\n      disabled,\n      value = 'on',\n      onCheckedChange,\n      form,\n      ...switchProps\n    } = props;\n    const [button, setButton] = React.useState<HTMLButtonElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setButton(node));\n    const hasConsumerStoppedPropagationRef = React.useRef(false);\n    // We set this to true by default so that events bubble to forms without JS (SSR)\n    const isFormControl = button ? form || !!button.closest('form') : true;\n    const [checked = false, setChecked] = useControllableState({\n      prop: checkedProp,\n      defaultProp: defaultChecked,\n      onChange: onCheckedChange,\n    });\n\n    return (\n      <SwitchProvider scope={__scopeSwitch} checked={checked} disabled={disabled}>\n        <Primitive.button\n          type=\"button\"\n          role=\"switch\"\n          aria-checked={checked}\n          aria-required={required}\n          data-state={getState(checked)}\n          data-disabled={disabled ? '' : undefined}\n          disabled={disabled}\n          value={value}\n          {...switchProps}\n          ref={composedRefs}\n          onClick={composeEventHandlers(props.onClick, (event) => {\n            setChecked((prevChecked) => !prevChecked);\n            if (isFormControl) {\n              hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n              // if switch is in a form, stop propagation from the button so that we only propagate\n              // one click event (from the input). We propagate changes from an input so that native\n              // form validation works and form events reflect switch updates.\n              if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n            }\n          })}\n        />\n        {isFormControl && (\n          <BubbleInput\n            control={button}\n            bubbles={!hasConsumerStoppedPropagationRef.current}\n            name={name}\n            value={value}\n            checked={checked}\n            required={required}\n            disabled={disabled}\n            form={form}\n            // We transform because the input is absolutely positioned but we have\n            // rendered it **after** the button. This pulls it back to sit on top\n            // of the button.\n            style={{ transform: 'translateX(-100%)' }}\n          />\n        )}\n      </SwitchProvider>\n    );\n  }\n);\n\nSwitch.displayName = SWITCH_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SwitchThumb\n * -----------------------------------------------------------------------------------------------*/\n\nconst THUMB_NAME = 'SwitchThumb';\n\ntype SwitchThumbElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface SwitchThumbProps extends PrimitiveSpanProps {}\n\nconst SwitchThumb = React.forwardRef<SwitchThumbElement, SwitchThumbProps>(\n  (props: ScopedProps<SwitchThumbProps>, forwardedRef) => {\n    const { __scopeSwitch, ...thumbProps } = props;\n    const context = useSwitchContext(THUMB_NAME, __scopeSwitch);\n    return (\n      <Primitive.span\n        data-state={getState(context.checked)}\n        data-disabled={context.disabled ? '' : undefined}\n        {...thumbProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nSwitchThumb.displayName = THUMB_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype InputProps = React.ComponentPropsWithoutRef<'input'>;\ninterface BubbleInputProps extends Omit<InputProps, 'checked'> {\n  checked: boolean;\n  control: HTMLElement | null;\n  bubbles: boolean;\n}\n\nconst BubbleInput = (props: BubbleInputProps) => {\n  const { control, checked, bubbles = true, ...inputProps } = props;\n  const ref = React.useRef<HTMLInputElement>(null);\n  const prevChecked = usePrevious(checked);\n  const controlSize = useSize(control);\n\n  // Bubble checked change to parents (e.g form change event)\n  React.useEffect(() => {\n    const input = ref.current!;\n    const inputProto = window.HTMLInputElement.prototype;\n    const descriptor = Object.getOwnPropertyDescriptor(inputProto, 'checked') as PropertyDescriptor;\n    const setChecked = descriptor.set;\n    if (prevChecked !== checked && setChecked) {\n      const event = new Event('click', { bubbles });\n      setChecked.call(input, checked);\n      input.dispatchEvent(event);\n    }\n  }, [prevChecked, checked, bubbles]);\n\n  return (\n    <input\n      type=\"checkbox\"\n      aria-hidden\n      defaultChecked={checked}\n      {...inputProps}\n      tabIndex={-1}\n      ref={ref}\n      style={{\n        ...props.style,\n        ...controlSize,\n        position: 'absolute',\n        pointerEvents: 'none',\n        opacity: 0,\n        margin: 0,\n      }}\n    />\n  );\n};\n\nfunction getState(checked: boolean) {\n  return checked ? 'checked' : 'unchecked';\n}\n\nconst Root = Switch;\nconst Thumb = SwitchThumb;\n\nexport {\n  createSwitchScope,\n  //\n  Switch,\n  SwitchThumb,\n  //\n  Root,\n  Thumb,\n};\nexport type { SwitchProps, SwitchThumbProps };\n"], "names": [], "mappings": ";;;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,4BAA4B;AACrC,SAAS,uBAAuB;AAChC,SAAS,0BAA0B;AACnC,SAAS,4BAA4B;AACrC,SAAS,mBAAmB;AAC5B,SAAS,eAAe;AACxB,SAAS,iBAAiB;AAmDpB,SACE,KADF;;;;;;;;;;;AA3CN,IAAM,cAAc;AAGpB,IAAM,CAAC,qBAAqB,iBAAiB,CAAA,8KAAI,qBAAA,EAAmB,WAAW;AAG/E,IAAM,CAAC,gBAAgB,gBAAgB,CAAA,GAAI,oBAAwC,WAAW;AAW9F,IAAM,2KAAe,aAAA,EACnB,CAAC,OAAiC,iBAAiB;IACjD,MAAM,EACJ,aAAA,EACA,IAAA,EACA,SAAS,WAAA,EACT,cAAA,EACA,QAAA,EACA,QAAA,EACA,QAAQ,IAAA,EACR,eAAA,EACA,IAAA,EACA,GAAG,aACL,GAAI;IACJ,MAAM,CAAC,QAAQ,SAAS,CAAA,qKAAU,WAAA,EAAmC,IAAI;IACzE,MAAM,eAAe,qMAAA,EAAgB;gDAAc,CAAC,OAAS,UAAU,IAAI,CAAC;;IAC5E,MAAM,qMAAyC,SAAA,EAAO,KAAK;IAE3D,MAAM,gBAAgB,SAAS,QAAQ,CAAC,CAAC,OAAO,OAAA,CAAQ,MAAM,IAAI;IAClE,MAAM,CAAC,UAAU,KAAA,EAAO,UAAU,CAAA,OAAI,mNAAA,EAAqB;QACzD,MAAM;QACN,aAAa;QACb,UAAU;IACZ,CAAC;IAED,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,OAAA,EAAC,gBAAA;QAAe,OAAO;QAAe;QAAkB;QACtD,UAAA;YAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,MAAA,EAAV;gBACC,MAAK;gBACL,MAAK;gBACL,gBAAc;gBACd,iBAAe;gBACf,cAAY,SAAS,OAAO;gBAC5B,iBAAe,WAAW,KAAK,KAAA;gBAC/B;gBACA;gBACC,GAAG,WAAA;gBACJ,KAAK;gBACL,aAAS,uLAAA,EAAqB,MAAM,OAAA,EAAS,CAAC,UAAU;oBACtD,WAAW,CAAC,cAAgB,CAAC,WAAW;oBACxC,IAAI,eAAe;wBACjB,iCAAiC,OAAA,GAAU,MAAM,oBAAA,CAAqB;wBAItE,IAAI,CAAC,iCAAiC,OAAA,CAAS,CAAA,MAAM,eAAA,CAAgB;oBACvE;gBACF,CAAC;YAAA;YAEF,iBACC,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,aAAA;gBACC,SAAS;gBACT,SAAS,CAAC,iCAAiC,OAAA;gBAC3C;gBACA;gBACA;gBACA;gBACA;gBACA;gBAIA,OAAO;oBAAE,WAAW;gBAAoB;YAAA;SAC1C;IAAA,CAEJ;AAEJ;AAGF,OAAO,WAAA,GAAc;AAMrB,IAAM,aAAa;AAMnB,IAAM,gLAAoB,aAAA,EACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,aAAA,EAAe,GAAG,WAAW,CAAA,GAAI;IACzC,MAAM,UAAU,iBAAiB,YAAY,aAAa;IAC1D,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,IAAA,EAAV;QACC,cAAY,SAAS,QAAQ,OAAO;QACpC,iBAAe,QAAQ,QAAA,GAAW,KAAK,KAAA;QACtC,GAAG,UAAA;QACJ,KAAK;IAAA;AAGX;AAGF,YAAY,WAAA,GAAc;AAW1B,IAAM,cAAc,CAAC,UAA4B;IAC/C,MAAM,EAAE,OAAA,EAAS,OAAA,EAAS,UAAU,IAAA,EAAM,GAAG,WAAW,CAAA,GAAI;IAC5D,MAAM,wKAAY,SAAA,EAAyB,IAAI;IAC/C,MAAM,iMAAc,cAAA,EAAY,OAAO;IACvC,MAAM,cAAc,yLAAA,EAAQ,OAAO;sKAG7B,YAAA;iCAAU,MAAM;YACpB,MAAM,QAAQ,IAAI,OAAA;YAClB,MAAM,aAAa,OAAO,gBAAA,CAAiB,SAAA;YAC3C,MAAM,aAAa,OAAO,wBAAA,CAAyB,YAAY,SAAS;YACxE,MAAM,aAAa,WAAW,GAAA;YAC9B,IAAI,gBAAgB,WAAW,YAAY;gBACzC,MAAM,QAAQ,IAAI,MAAM,SAAS;oBAAE;gBAAQ,CAAC;gBAC5C,WAAW,IAAA,CAAK,OAAO,OAAO;gBAC9B,MAAM,aAAA,CAAc,KAAK;YAC3B;QACF;gCAAG;QAAC;QAAa;QAAS,OAAO;KAAC;IAElC,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,SAAA;QACC,MAAK;QACL,eAAW;QACX,gBAAgB;QACf,GAAG,UAAA;QACJ,UAAU,CAAA;QACV;QACA,OAAO;YACL,GAAG,MAAM,KAAA;YACT,GAAG,WAAA;YACH,UAAU;YACV,eAAe;YACf,SAAS;YACT,QAAQ;QACV;IAAA;AAGN;AAEA,SAAS,SAAS,OAAA,EAAkB;IAClC,OAAO,UAAU,YAAY;AAC/B;AAEA,IAAM,OAAO;AACb,IAAM,QAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5154, "column": 0}, "map": {"version": 3, "file": "play.js", "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/lucide-react/src/icons/play.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['polygon', { points: '6 3 20 12 6 21 6 3', key: '1oa8hb' }]];\n\n/**\n * @component @name Play\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWdvbiBwb2ludHM9IjYgMyAyMCAxMiA2IDIxIDYgMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/play\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Play = createLucideIcon('Play', __iconNode);\n\nexport default Play;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAuB,CAAA,CAAA;IAAC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW;QAAA,CAAE;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAC;KAAC;CAAA,CAAA;AAa3F,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5193, "column": 0}, "map": {"version": 3, "file": "pause.js", "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/lucide-react/src/icons/pause.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { x: '14', y: '4', width: '4', height: '16', rx: '1', key: 'zuxfzm' }],\n  ['rect', { x: '6', y: '4', width: '4', height: '16', rx: '1', key: '1okwgv' }],\n];\n\n/**\n * @component @name Pause\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB4PSIxNCIgeT0iNCIgd2lkdGg9IjQiIGhlaWdodD0iMTYiIHJ4PSIxIiAvPgogIDxyZWN0IHg9IjYiIHk9IjQiIHdpZHRoPSI0IiBoZWlnaHQ9IjE2IiByeD0iMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/pause\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Pause = createLucideIcon('Pause', __iconNode);\n\nexport default Pause;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAG;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,QAAQ,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IAC9E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAG;YAAK,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,QAAQ,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC/E,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAiB,AAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5247, "column": 0}, "map": {"version": 3, "file": "refresh-cw.js", "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/lucide-react/src/icons/refresh-cw.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8', key: 'v9h5vc' }],\n  ['path', { d: 'M21 3v5h-5', key: '1q7to0' }],\n  ['path', { d: 'M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16', key: '3uifl3' }],\n  ['path', { d: 'M8 16H3v5', key: '1cv678' }],\n];\n\n/**\n * @component @name RefreshCw\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAxMmE5IDkgMCAwIDEgOS05IDkuNzUgOS43NSAwIDAgMSA2Ljc0IDIuNzRMMjEgOCIgLz4KICA8cGF0aCBkPSJNMjEgM3Y1aC01IiAvPgogIDxwYXRoIGQ9Ik0yMSAxMmE5IDkgMCAwIDEtOSA5IDkuNzUgOS43NSAwIDAgMS02Ljc0LTIuNzRMMyAxNiIgLz4KICA8cGF0aCBkPSJNOCAxNkgzdjUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/refresh-cw\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst RefreshCw = createLucideIcon('RefreshCw', __iconNode);\n\nexport default RefreshCw;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAsD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACnF;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAuD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACpF;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC5C,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAa,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5307, "column": 0}, "map": {"version": 3, "file": "settings.js", "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/lucide-react/src/icons/settings.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z',\n      key: '1qme2f',\n    },\n  ],\n  ['circle', { cx: '12', cy: '12', r: '3', key: '1v7zrd' }],\n];\n\n/**\n * @component @name Settings\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIuMjIgMmgtLjQ0YTIgMiAwIDAgMC0yIDJ2LjE4YTIgMiAwIDAgMS0xIDEuNzNsLS40My4yNWEyIDIgMCAwIDEtMiAwbC0uMTUtLjA4YTIgMiAwIDAgMC0yLjczLjczbC0uMjIuMzhhMiAyIDAgMCAwIC43MyAyLjczbC4xNS4xYTIgMiAwIDAgMSAxIDEuNzJ2LjUxYTIgMiAwIDAgMS0xIDEuNzRsLS4xNS4wOWEyIDIgMCAwIDAtLjczIDIuNzNsLjIyLjM4YTIgMiAwIDAgMCAyLjczLjczbC4xNS0uMDhhMiAyIDAgMCAxIDIgMGwuNDMuMjVhMiAyIDAgMCAxIDEgMS43M1YyMGEyIDIgMCAwIDAgMiAyaC40NGEyIDIgMCAwIDAgMi0ydi0uMThhMiAyIDAgMCAxIDEtMS43M2wuNDMtLjI1YTIgMiAwIDAgMSAyIDBsLjE1LjA4YTIgMiAwIDAgMCAyLjczLS43M2wuMjItLjM5YTIgMiAwIDAgMC0uNzMtMi43M2wtLjE1LS4wOGEyIDIgMCAwIDEtMS0xLjc0di0uNWEyIDIgMCAwIDEgMS0xLjc0bC4xNS0uMDlhMiAyIDAgMCAwIC43My0yLjczbC0uMjItLjM4YTIgMiAwIDAgMC0yLjczLS43M2wtLjE1LjA4YTIgMiAwIDAgMS0yIDBsLS40My0uMjVhMiAyIDAgMCAxLTEtMS43M1Y0YTIgMiAwIDAgMC0yLTJ6IiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTIiIHI9IjMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/settings\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Settings = createLucideIcon('Settings', __iconNode);\n\nexport default Settings;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC,CAAA;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA,CAAA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP,CAAA;KACF,CAAA;IACA;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CAC1D,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5355, "column": 0}, "map": {"version": 3, "file": "volume-2.js", "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/lucide-react/src/icons/volume-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z',\n      key: 'uqj9uw',\n    },\n  ],\n  ['path', { d: 'M16 9a5 5 0 0 1 0 6', key: '1q6k2b' }],\n  ['path', { d: 'M19.364 18.364a9 9 0 0 0 0-12.728', key: 'ijwkga' }],\n];\n\n/**\n * @component @name Volume2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTEgNC43MDJhLjcwNS43MDUgMCAwIDAtMS4yMDMtLjQ5OEw2LjQxMyA3LjU4N0ExLjQgMS40IDAgMCAxIDUuNDE2IDhIM2ExIDEgMCAwIDAtMSAxdjZhMSAxIDAgMCAwIDEgMWgyLjQxNmExLjQgMS40IDAgMCAxIC45OTcuNDEzbDMuMzgzIDMuMzg0QS43MDUuNzA1IDAgMCAwIDExIDE5LjI5OHoiIC8+CiAgPHBhdGggZD0iTTE2IDlhNSA1IDAgMCAxIDAgNiIgLz4KICA8cGF0aCBkPSJNMTkuMzY0IDE4LjM2NGE5IDkgMCAwIDAgMC0xMi43MjgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/volume-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Volume2 = createLucideIcon('Volume2', __iconNode);\n\nexport default Volume2;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC,CAAA;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA,CAAA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP,CAAA;KACF,CAAA;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAuB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACpD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAqC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACpE,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAU,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5408, "column": 0}, "map": {"version": 3, "file": "volume-x.js", "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/lucide-react/src/icons/volume-x.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z',\n      key: 'uqj9uw',\n    },\n  ],\n  ['line', { x1: '22', x2: '16', y1: '9', y2: '15', key: '1ewh16' }],\n  ['line', { x1: '16', x2: '22', y1: '9', y2: '15', key: '5ykzw1' }],\n];\n\n/**\n * @component @name VolumeX\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTEgNC43MDJhLjcwNS43MDUgMCAwIDAtMS4yMDMtLjQ5OEw2LjQxMyA3LjU4N0ExLjQgMS40IDAgMCAxIDUuNDE2IDhIM2ExIDEgMCAwIDAtMSAxdjZhMSAxIDAgMCAwIDEgMWgyLjQxNmExLjQgMS40IDAgMCAxIC45OTcuNDEzbDMuMzgzIDMuMzg0QS43MDUuNzA1IDAgMCAwIDExIDE5LjI5OHoiIC8+CiAgPGxpbmUgeDE9IjIyIiB4Mj0iMTYiIHkxPSI5IiB5Mj0iMTUiIC8+CiAgPGxpbmUgeDE9IjE2IiB4Mj0iMjIiIHkxPSI5IiB5Mj0iMTUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/volume-x\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst VolumeX = createLucideIcon('VolumeX', __iconNode);\n\nexport default VolumeX;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC,CAAA;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA,CAAA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP,CAAA;KACF,CAAA;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACjE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACnE,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAU,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,AAAjB,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5467, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/%40radix-ui/react-separator/src/separator.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n *  Separator\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'Separator';\nconst DEFAULT_ORIENTATION = 'horizontal';\nconst ORIENTATIONS = ['horizontal', 'vertical'] as const;\n\ntype Orientation = (typeof ORIENTATIONS)[number];\ntype SeparatorElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface SeparatorProps extends PrimitiveDivProps {\n  /**\n   * Either `vertical` or `horizontal`. Defaults to `horizontal`.\n   */\n  orientation?: Orientation;\n  /**\n   * Whether or not the component is purely decorative. When true, accessibility-related attributes\n   * are updated so that that the rendered element is removed from the accessibility tree.\n   */\n  decorative?: boolean;\n}\n\nconst Separator = React.forwardRef<SeparatorElement, SeparatorProps>((props, forwardedRef) => {\n  const { decorative, orientation: orientationProp = DEFAULT_ORIENTATION, ...domProps } = props;\n  const orientation = isValidOrientation(orientationProp) ? orientationProp : DEFAULT_ORIENTATION;\n  // `aria-orientation` defaults to `horizontal` so we only need it if `orientation` is vertical\n  const ariaOrientation = orientation === 'vertical' ? orientation : undefined;\n  const semanticProps = decorative\n    ? { role: 'none' }\n    : { 'aria-orientation': ariaOrientation, role: 'separator' };\n\n  return (\n    <Primitive.div\n      data-orientation={orientation}\n      {...semanticProps}\n      {...domProps}\n      ref={forwardedRef}\n    />\n  );\n});\n\nSeparator.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction isValidOrientation(orientation: any): orientation is Orientation {\n  return ORIENTATIONS.includes(orientation);\n}\n\nconst Root = Separator;\n\nexport {\n  Separator,\n  //\n  Root,\n};\nexport type { SeparatorProps };\n"], "names": [], "mappings": ";;;;;AAAA,YAAY,WAAW;AACvB,SAAS,iBAAiB;AAmCtB;;;;AA7BJ,IAAM,OAAO;AACb,IAAM,sBAAsB;AAC5B,IAAM,eAAe;IAAC;IAAc,UAAU;CAAA;AAiB9C,IAAM,8KAAkB,aAAA,EAA6C,CAAC,OAAO,iBAAiB;IAC5F,MAAM,EAAE,UAAA,EAAY,aAAa,kBAAkB,mBAAA,EAAqB,GAAG,SAAS,CAAA,GAAI;IACxF,MAAM,cAAc,mBAAmB,eAAe,IAAI,kBAAkB;IAE5E,MAAM,kBAAkB,gBAAgB,aAAa,cAAc,KAAA;IACnE,MAAM,gBAAgB,aAClB;QAAE,MAAM;IAAO,IACf;QAAE,oBAAoB;QAAiB,MAAM;IAAY;IAE7D,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,GAAA,EAAV;QACC,oBAAkB;QACjB,GAAG,aAAA;QACH,GAAG,QAAA;QACJ,KAAK;IAAA;AAGX,CAAC;AAED,UAAU,WAAA,GAAc;AAIxB,SAAS,mBAAmB,WAAA,EAA8C;IACxE,OAAO,aAAa,QAAA,CAAS,WAAW;AAC1C;AAEA,IAAM,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5514, "column": 0}, "map": {"version": 3, "file": "trophy.js", "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/lucide-react/src/icons/trophy.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M6 9H4.5a2.5 2.5 0 0 1 0-5H6', key: '17hqa7' }],\n  ['path', { d: 'M18 9h1.5a2.5 2.5 0 0 0 0-5H18', key: 'lmptdp' }],\n  ['path', { d: 'M4 22h16', key: '57wxv0' }],\n  ['path', { d: 'M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22', key: '1nw9bq' }],\n  ['path', { d: 'M14 14.66V17c0 .*********** 1.21C16.15 18.75 17 20.24 17 22', key: '1np0yb' }],\n  ['path', { d: 'M18 2H6v7a6 6 0 0 0 12 0V2Z', key: 'u46fv3' }],\n];\n\n/**\n * @component @name Trophy\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) - https://lucide.dev/icons/trophy\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Trophy = createLucideIcon('Trophy', __iconNode);\n\nexport default Trophy;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAgC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IAC7D;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAkC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IAC/D;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA8D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IAC3F;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA+D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IAC5F;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA+B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC9D,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAA,AAAjB,CAAA,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5588, "column": 0}, "map": {"version": 3, "file": "circle-check.js", "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/lucide-react/src/icons/circle-check.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['path', { d: 'm9 12 2 2 4-4', key: 'dzmm74' }],\n];\n\n/**\n * @component @name CircleCheck\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cGF0aCBkPSJtOSAxMiAyIDIgNC00IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/circle-check\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleCheck = createLucideIcon('CircleCheck', __iconNode);\n\nexport default CircleCheck;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA,CAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAChD,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5636, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/%40radix-ui/react-scroll-area/src/scroll-area.tsx", "file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/%40radix-ui/react-scroll-area/src/use-state-machine.ts"], "sourcesContent": ["/// <reference types=\"resize-observer-browser\" />\n\nimport * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { Presence } from '@radix-ui/react-presence';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { clamp } from '@radix-ui/number';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useStateMachine } from './use-state-machine';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\ntype Sizes = {\n  content: number;\n  viewport: number;\n  scrollbar: {\n    size: number;\n    paddingStart: number;\n    paddingEnd: number;\n  };\n};\n\n/* -------------------------------------------------------------------------------------------------\n * ScrollArea\n * -----------------------------------------------------------------------------------------------*/\n\nconst SCROLL_AREA_NAME = 'ScrollArea';\n\ntype ScopedProps<P> = P & { __scopeScrollArea?: Scope };\nconst [createScrollAreaContext, createScrollAreaScope] = createContextScope(SCROLL_AREA_NAME);\n\ntype ScrollAreaContextValue = {\n  type: 'auto' | 'always' | 'scroll' | 'hover';\n  dir: Direction;\n  scrollHideDelay: number;\n  scrollArea: ScrollAreaElement | null;\n  viewport: ScrollAreaViewportElement | null;\n  onViewportChange(viewport: ScrollAreaViewportElement | null): void;\n  content: HTMLDivElement | null;\n  onContentChange(content: HTMLDivElement): void;\n  scrollbarX: ScrollAreaScrollbarElement | null;\n  onScrollbarXChange(scrollbar: ScrollAreaScrollbarElement | null): void;\n  scrollbarXEnabled: boolean;\n  onScrollbarXEnabledChange(rendered: boolean): void;\n  scrollbarY: ScrollAreaScrollbarElement | null;\n  onScrollbarYChange(scrollbar: ScrollAreaScrollbarElement | null): void;\n  scrollbarYEnabled: boolean;\n  onScrollbarYEnabledChange(rendered: boolean): void;\n  onCornerWidthChange(width: number): void;\n  onCornerHeightChange(height: number): void;\n};\n\nconst [ScrollAreaProvider, useScrollAreaContext] =\n  createScrollAreaContext<ScrollAreaContextValue>(SCROLL_AREA_NAME);\n\ntype ScrollAreaElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface ScrollAreaProps extends PrimitiveDivProps {\n  type?: ScrollAreaContextValue['type'];\n  dir?: ScrollAreaContextValue['dir'];\n  scrollHideDelay?: number;\n}\n\nconst ScrollArea = React.forwardRef<ScrollAreaElement, ScrollAreaProps>(\n  (props: ScopedProps<ScrollAreaProps>, forwardedRef) => {\n    const {\n      __scopeScrollArea,\n      type = 'hover',\n      dir,\n      scrollHideDelay = 600,\n      ...scrollAreaProps\n    } = props;\n    const [scrollArea, setScrollArea] = React.useState<ScrollAreaElement | null>(null);\n    const [viewport, setViewport] = React.useState<ScrollAreaViewportElement | null>(null);\n    const [content, setContent] = React.useState<HTMLDivElement | null>(null);\n    const [scrollbarX, setScrollbarX] = React.useState<ScrollAreaScrollbarElement | null>(null);\n    const [scrollbarY, setScrollbarY] = React.useState<ScrollAreaScrollbarElement | null>(null);\n    const [cornerWidth, setCornerWidth] = React.useState(0);\n    const [cornerHeight, setCornerHeight] = React.useState(0);\n    const [scrollbarXEnabled, setScrollbarXEnabled] = React.useState(false);\n    const [scrollbarYEnabled, setScrollbarYEnabled] = React.useState(false);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setScrollArea(node));\n    const direction = useDirection(dir);\n\n    return (\n      <ScrollAreaProvider\n        scope={__scopeScrollArea}\n        type={type}\n        dir={direction}\n        scrollHideDelay={scrollHideDelay}\n        scrollArea={scrollArea}\n        viewport={viewport}\n        onViewportChange={setViewport}\n        content={content}\n        onContentChange={setContent}\n        scrollbarX={scrollbarX}\n        onScrollbarXChange={setScrollbarX}\n        scrollbarXEnabled={scrollbarXEnabled}\n        onScrollbarXEnabledChange={setScrollbarXEnabled}\n        scrollbarY={scrollbarY}\n        onScrollbarYChange={setScrollbarY}\n        scrollbarYEnabled={scrollbarYEnabled}\n        onScrollbarYEnabledChange={setScrollbarYEnabled}\n        onCornerWidthChange={setCornerWidth}\n        onCornerHeightChange={setCornerHeight}\n      >\n        <Primitive.div\n          dir={direction}\n          {...scrollAreaProps}\n          ref={composedRefs}\n          style={{\n            position: 'relative',\n            // Pass corner sizes as CSS vars to reduce re-renders of context consumers\n            ['--radix-scroll-area-corner-width' as any]: cornerWidth + 'px',\n            ['--radix-scroll-area-corner-height' as any]: cornerHeight + 'px',\n            ...props.style,\n          }}\n        />\n      </ScrollAreaProvider>\n    );\n  }\n);\n\nScrollArea.displayName = SCROLL_AREA_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ScrollAreaViewport\n * -----------------------------------------------------------------------------------------------*/\n\nconst VIEWPORT_NAME = 'ScrollAreaViewport';\n\ntype ScrollAreaViewportElement = React.ElementRef<typeof Primitive.div>;\ninterface ScrollAreaViewportProps extends PrimitiveDivProps {\n  nonce?: string;\n}\n\nconst ScrollAreaViewport = React.forwardRef<ScrollAreaViewportElement, ScrollAreaViewportProps>(\n  (props: ScopedProps<ScrollAreaViewportProps>, forwardedRef) => {\n    const { __scopeScrollArea, children, nonce, ...viewportProps } = props;\n    const context = useScrollAreaContext(VIEWPORT_NAME, __scopeScrollArea);\n    const ref = React.useRef<ScrollAreaViewportElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref, context.onViewportChange);\n    return (\n      <>\n        {/* Hide scrollbars cross-browser and enable momentum scroll for touch devices */}\n        <style\n          dangerouslySetInnerHTML={{\n            __html: `[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}`,\n          }}\n          nonce={nonce}\n        />\n        <Primitive.div\n          data-radix-scroll-area-viewport=\"\"\n          {...viewportProps}\n          ref={composedRefs}\n          style={{\n            /**\n             * We don't support `visible` because the intention is to have at least one scrollbar\n             * if this component is used and `visible` will behave like `auto` in that case\n             * https://developer.mozilla.org/en-US/docs/Web/CSS/overflow#description\n             *\n             * We don't handle `auto` because the intention is for the native implementation\n             * to be hidden if using this component. We just want to ensure the node is scrollable\n             * so could have used either `scroll` or `auto` here. We picked `scroll` to prevent\n             * the browser from having to work out whether to render native scrollbars or not,\n             * we tell it to with the intention of hiding them in CSS.\n             */\n            overflowX: context.scrollbarXEnabled ? 'scroll' : 'hidden',\n            overflowY: context.scrollbarYEnabled ? 'scroll' : 'hidden',\n            ...props.style,\n          }}\n        >\n          {/**\n           * `display: table` ensures our content div will match the size of its children in both\n           * horizontal and vertical axis so we can determine if scroll width/height changed and\n           * recalculate thumb sizes. This doesn't account for children with *percentage*\n           * widths that change. We'll wait to see what use-cases consumers come up with there\n           * before trying to resolve it.\n           */}\n          <div ref={context.onContentChange} style={{ minWidth: '100%', display: 'table' }}>\n            {children}\n          </div>\n        </Primitive.div>\n      </>\n    );\n  }\n);\n\nScrollAreaViewport.displayName = VIEWPORT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ScrollAreaScrollbar\n * -----------------------------------------------------------------------------------------------*/\n\nconst SCROLLBAR_NAME = 'ScrollAreaScrollbar';\n\ntype ScrollAreaScrollbarElement = ScrollAreaScrollbarVisibleElement;\ninterface ScrollAreaScrollbarProps extends ScrollAreaScrollbarVisibleProps {\n  forceMount?: true;\n}\n\nconst ScrollAreaScrollbar = React.forwardRef<ScrollAreaScrollbarElement, ScrollAreaScrollbarProps>(\n  (props: ScopedProps<ScrollAreaScrollbarProps>, forwardedRef) => {\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const { onScrollbarXEnabledChange, onScrollbarYEnabledChange } = context;\n    const isHorizontal = props.orientation === 'horizontal';\n\n    React.useEffect(() => {\n      isHorizontal ? onScrollbarXEnabledChange(true) : onScrollbarYEnabledChange(true);\n      return () => {\n        isHorizontal ? onScrollbarXEnabledChange(false) : onScrollbarYEnabledChange(false);\n      };\n    }, [isHorizontal, onScrollbarXEnabledChange, onScrollbarYEnabledChange]);\n\n    return context.type === 'hover' ? (\n      <ScrollAreaScrollbarHover {...scrollbarProps} ref={forwardedRef} forceMount={forceMount} />\n    ) : context.type === 'scroll' ? (\n      <ScrollAreaScrollbarScroll {...scrollbarProps} ref={forwardedRef} forceMount={forceMount} />\n    ) : context.type === 'auto' ? (\n      <ScrollAreaScrollbarAuto {...scrollbarProps} ref={forwardedRef} forceMount={forceMount} />\n    ) : context.type === 'always' ? (\n      <ScrollAreaScrollbarVisible {...scrollbarProps} ref={forwardedRef} />\n    ) : null;\n  }\n);\n\nScrollAreaScrollbar.displayName = SCROLLBAR_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype ScrollAreaScrollbarHoverElement = ScrollAreaScrollbarAutoElement;\ninterface ScrollAreaScrollbarHoverProps extends ScrollAreaScrollbarAutoProps {\n  forceMount?: true;\n}\n\nconst ScrollAreaScrollbarHover = React.forwardRef<\n  ScrollAreaScrollbarHoverElement,\n  ScrollAreaScrollbarHoverProps\n>((props: ScopedProps<ScrollAreaScrollbarHoverProps>, forwardedRef) => {\n  const { forceMount, ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const [visible, setVisible] = React.useState(false);\n\n  React.useEffect(() => {\n    const scrollArea = context.scrollArea;\n    let hideTimer = 0;\n    if (scrollArea) {\n      const handlePointerEnter = () => {\n        window.clearTimeout(hideTimer);\n        setVisible(true);\n      };\n      const handlePointerLeave = () => {\n        hideTimer = window.setTimeout(() => setVisible(false), context.scrollHideDelay);\n      };\n      scrollArea.addEventListener('pointerenter', handlePointerEnter);\n      scrollArea.addEventListener('pointerleave', handlePointerLeave);\n      return () => {\n        window.clearTimeout(hideTimer);\n        scrollArea.removeEventListener('pointerenter', handlePointerEnter);\n        scrollArea.removeEventListener('pointerleave', handlePointerLeave);\n      };\n    }\n  }, [context.scrollArea, context.scrollHideDelay]);\n\n  return (\n    <Presence present={forceMount || visible}>\n      <ScrollAreaScrollbarAuto\n        data-state={visible ? 'visible' : 'hidden'}\n        {...scrollbarProps}\n        ref={forwardedRef}\n      />\n    </Presence>\n  );\n});\n\ntype ScrollAreaScrollbarScrollElement = ScrollAreaScrollbarVisibleElement;\ninterface ScrollAreaScrollbarScrollProps extends ScrollAreaScrollbarVisibleProps {\n  forceMount?: true;\n}\n\nconst ScrollAreaScrollbarScroll = React.forwardRef<\n  ScrollAreaScrollbarScrollElement,\n  ScrollAreaScrollbarScrollProps\n>((props: ScopedProps<ScrollAreaScrollbarScrollProps>, forwardedRef) => {\n  const { forceMount, ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const isHorizontal = props.orientation === 'horizontal';\n  const debounceScrollEnd = useDebounceCallback(() => send('SCROLL_END'), 100);\n  const [state, send] = useStateMachine('hidden', {\n    hidden: {\n      SCROLL: 'scrolling',\n    },\n    scrolling: {\n      SCROLL_END: 'idle',\n      POINTER_ENTER: 'interacting',\n    },\n    interacting: {\n      SCROLL: 'interacting',\n      POINTER_LEAVE: 'idle',\n    },\n    idle: {\n      HIDE: 'hidden',\n      SCROLL: 'scrolling',\n      POINTER_ENTER: 'interacting',\n    },\n  });\n\n  React.useEffect(() => {\n    if (state === 'idle') {\n      const hideTimer = window.setTimeout(() => send('HIDE'), context.scrollHideDelay);\n      return () => window.clearTimeout(hideTimer);\n    }\n  }, [state, context.scrollHideDelay, send]);\n\n  React.useEffect(() => {\n    const viewport = context.viewport;\n    const scrollDirection = isHorizontal ? 'scrollLeft' : 'scrollTop';\n\n    if (viewport) {\n      let prevScrollPos = viewport[scrollDirection];\n      const handleScroll = () => {\n        const scrollPos = viewport[scrollDirection];\n        const hasScrollInDirectionChanged = prevScrollPos !== scrollPos;\n        if (hasScrollInDirectionChanged) {\n          send('SCROLL');\n          debounceScrollEnd();\n        }\n        prevScrollPos = scrollPos;\n      };\n      viewport.addEventListener('scroll', handleScroll);\n      return () => viewport.removeEventListener('scroll', handleScroll);\n    }\n  }, [context.viewport, isHorizontal, send, debounceScrollEnd]);\n\n  return (\n    <Presence present={forceMount || state !== 'hidden'}>\n      <ScrollAreaScrollbarVisible\n        data-state={state === 'hidden' ? 'hidden' : 'visible'}\n        {...scrollbarProps}\n        ref={forwardedRef}\n        onPointerEnter={composeEventHandlers(props.onPointerEnter, () => send('POINTER_ENTER'))}\n        onPointerLeave={composeEventHandlers(props.onPointerLeave, () => send('POINTER_LEAVE'))}\n      />\n    </Presence>\n  );\n});\n\ntype ScrollAreaScrollbarAutoElement = ScrollAreaScrollbarVisibleElement;\ninterface ScrollAreaScrollbarAutoProps extends ScrollAreaScrollbarVisibleProps {\n  forceMount?: true;\n}\n\nconst ScrollAreaScrollbarAuto = React.forwardRef<\n  ScrollAreaScrollbarAutoElement,\n  ScrollAreaScrollbarAutoProps\n>((props: ScopedProps<ScrollAreaScrollbarAutoProps>, forwardedRef) => {\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const { forceMount, ...scrollbarProps } = props;\n  const [visible, setVisible] = React.useState(false);\n  const isHorizontal = props.orientation === 'horizontal';\n  const handleResize = useDebounceCallback(() => {\n    if (context.viewport) {\n      const isOverflowX = context.viewport.offsetWidth < context.viewport.scrollWidth;\n      const isOverflowY = context.viewport.offsetHeight < context.viewport.scrollHeight;\n      setVisible(isHorizontal ? isOverflowX : isOverflowY);\n    }\n  }, 10);\n\n  useResizeObserver(context.viewport, handleResize);\n  useResizeObserver(context.content, handleResize);\n\n  return (\n    <Presence present={forceMount || visible}>\n      <ScrollAreaScrollbarVisible\n        data-state={visible ? 'visible' : 'hidden'}\n        {...scrollbarProps}\n        ref={forwardedRef}\n      />\n    </Presence>\n  );\n});\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype ScrollAreaScrollbarVisibleElement = ScrollAreaScrollbarAxisElement;\ninterface ScrollAreaScrollbarVisibleProps\n  extends Omit<ScrollAreaScrollbarAxisProps, keyof ScrollAreaScrollbarAxisPrivateProps> {\n  orientation?: 'horizontal' | 'vertical';\n}\n\nconst ScrollAreaScrollbarVisible = React.forwardRef<\n  ScrollAreaScrollbarVisibleElement,\n  ScrollAreaScrollbarVisibleProps\n>((props: ScopedProps<ScrollAreaScrollbarVisibleProps>, forwardedRef) => {\n  const { orientation = 'vertical', ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const thumbRef = React.useRef<ScrollAreaThumbElement | null>(null);\n  const pointerOffsetRef = React.useRef(0);\n  const [sizes, setSizes] = React.useState<Sizes>({\n    content: 0,\n    viewport: 0,\n    scrollbar: { size: 0, paddingStart: 0, paddingEnd: 0 },\n  });\n  const thumbRatio = getThumbRatio(sizes.viewport, sizes.content);\n\n  type UncommonProps = 'onThumbPositionChange' | 'onDragScroll' | 'onWheelScroll';\n  const commonProps: Omit<ScrollAreaScrollbarAxisPrivateProps, UncommonProps> = {\n    ...scrollbarProps,\n    sizes,\n    onSizesChange: setSizes,\n    hasThumb: Boolean(thumbRatio > 0 && thumbRatio < 1),\n    onThumbChange: (thumb) => (thumbRef.current = thumb),\n    onThumbPointerUp: () => (pointerOffsetRef.current = 0),\n    onThumbPointerDown: (pointerPos) => (pointerOffsetRef.current = pointerPos),\n  };\n\n  function getScrollPosition(pointerPos: number, dir?: Direction) {\n    return getScrollPositionFromPointer(pointerPos, pointerOffsetRef.current, sizes, dir);\n  }\n\n  if (orientation === 'horizontal') {\n    return (\n      <ScrollAreaScrollbarX\n        {...commonProps}\n        ref={forwardedRef}\n        onThumbPositionChange={() => {\n          if (context.viewport && thumbRef.current) {\n            const scrollPos = context.viewport.scrollLeft;\n            const offset = getThumbOffsetFromScroll(scrollPos, sizes, context.dir);\n            thumbRef.current.style.transform = `translate3d(${offset}px, 0, 0)`;\n          }\n        }}\n        onWheelScroll={(scrollPos) => {\n          if (context.viewport) context.viewport.scrollLeft = scrollPos;\n        }}\n        onDragScroll={(pointerPos) => {\n          if (context.viewport) {\n            context.viewport.scrollLeft = getScrollPosition(pointerPos, context.dir);\n          }\n        }}\n      />\n    );\n  }\n\n  if (orientation === 'vertical') {\n    return (\n      <ScrollAreaScrollbarY\n        {...commonProps}\n        ref={forwardedRef}\n        onThumbPositionChange={() => {\n          if (context.viewport && thumbRef.current) {\n            const scrollPos = context.viewport.scrollTop;\n            const offset = getThumbOffsetFromScroll(scrollPos, sizes);\n            thumbRef.current.style.transform = `translate3d(0, ${offset}px, 0)`;\n          }\n        }}\n        onWheelScroll={(scrollPos) => {\n          if (context.viewport) context.viewport.scrollTop = scrollPos;\n        }}\n        onDragScroll={(pointerPos) => {\n          if (context.viewport) context.viewport.scrollTop = getScrollPosition(pointerPos);\n        }}\n      />\n    );\n  }\n\n  return null;\n});\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype ScrollAreaScrollbarAxisPrivateProps = {\n  hasThumb: boolean;\n  sizes: Sizes;\n  onSizesChange(sizes: Sizes): void;\n  onThumbChange(thumb: ScrollAreaThumbElement | null): void;\n  onThumbPointerDown(pointerPos: number): void;\n  onThumbPointerUp(): void;\n  onThumbPositionChange(): void;\n  onWheelScroll(scrollPos: number): void;\n  onDragScroll(pointerPos: number): void;\n};\n\ntype ScrollAreaScrollbarAxisElement = ScrollAreaScrollbarImplElement;\ninterface ScrollAreaScrollbarAxisProps\n  extends Omit<ScrollAreaScrollbarImplProps, keyof ScrollAreaScrollbarImplPrivateProps>,\n    ScrollAreaScrollbarAxisPrivateProps {}\n\nconst ScrollAreaScrollbarX = React.forwardRef<\n  ScrollAreaScrollbarAxisElement,\n  ScrollAreaScrollbarAxisProps\n>((props: ScopedProps<ScrollAreaScrollbarAxisProps>, forwardedRef) => {\n  const { sizes, onSizesChange, ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const [computedStyle, setComputedStyle] = React.useState<CSSStyleDeclaration>();\n  const ref = React.useRef<ScrollAreaScrollbarAxisElement>(null);\n  const composeRefs = useComposedRefs(forwardedRef, ref, context.onScrollbarXChange);\n\n  React.useEffect(() => {\n    if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n  }, [ref]);\n\n  return (\n    <ScrollAreaScrollbarImpl\n      data-orientation=\"horizontal\"\n      {...scrollbarProps}\n      ref={composeRefs}\n      sizes={sizes}\n      style={{\n        bottom: 0,\n        left: context.dir === 'rtl' ? 'var(--radix-scroll-area-corner-width)' : 0,\n        right: context.dir === 'ltr' ? 'var(--radix-scroll-area-corner-width)' : 0,\n        ['--radix-scroll-area-thumb-width' as any]: getThumbSize(sizes) + 'px',\n        ...props.style,\n      }}\n      onThumbPointerDown={(pointerPos) => props.onThumbPointerDown(pointerPos.x)}\n      onDragScroll={(pointerPos) => props.onDragScroll(pointerPos.x)}\n      onWheelScroll={(event, maxScrollPos) => {\n        if (context.viewport) {\n          const scrollPos = context.viewport.scrollLeft + event.deltaX;\n          props.onWheelScroll(scrollPos);\n          // prevent window scroll when wheeling on scrollbar\n          if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n            event.preventDefault();\n          }\n        }\n      }}\n      onResize={() => {\n        if (ref.current && context.viewport && computedStyle) {\n          onSizesChange({\n            content: context.viewport.scrollWidth,\n            viewport: context.viewport.offsetWidth,\n            scrollbar: {\n              size: ref.current.clientWidth,\n              paddingStart: toInt(computedStyle.paddingLeft),\n              paddingEnd: toInt(computedStyle.paddingRight),\n            },\n          });\n        }\n      }}\n    />\n  );\n});\n\nconst ScrollAreaScrollbarY = React.forwardRef<\n  ScrollAreaScrollbarAxisElement,\n  ScrollAreaScrollbarAxisProps\n>((props: ScopedProps<ScrollAreaScrollbarAxisProps>, forwardedRef) => {\n  const { sizes, onSizesChange, ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const [computedStyle, setComputedStyle] = React.useState<CSSStyleDeclaration>();\n  const ref = React.useRef<ScrollAreaScrollbarAxisElement>(null);\n  const composeRefs = useComposedRefs(forwardedRef, ref, context.onScrollbarYChange);\n\n  React.useEffect(() => {\n    if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n  }, [ref]);\n\n  return (\n    <ScrollAreaScrollbarImpl\n      data-orientation=\"vertical\"\n      {...scrollbarProps}\n      ref={composeRefs}\n      sizes={sizes}\n      style={{\n        top: 0,\n        right: context.dir === 'ltr' ? 0 : undefined,\n        left: context.dir === 'rtl' ? 0 : undefined,\n        bottom: 'var(--radix-scroll-area-corner-height)',\n        ['--radix-scroll-area-thumb-height' as any]: getThumbSize(sizes) + 'px',\n        ...props.style,\n      }}\n      onThumbPointerDown={(pointerPos) => props.onThumbPointerDown(pointerPos.y)}\n      onDragScroll={(pointerPos) => props.onDragScroll(pointerPos.y)}\n      onWheelScroll={(event, maxScrollPos) => {\n        if (context.viewport) {\n          const scrollPos = context.viewport.scrollTop + event.deltaY;\n          props.onWheelScroll(scrollPos);\n          // prevent window scroll when wheeling on scrollbar\n          if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n            event.preventDefault();\n          }\n        }\n      }}\n      onResize={() => {\n        if (ref.current && context.viewport && computedStyle) {\n          onSizesChange({\n            content: context.viewport.scrollHeight,\n            viewport: context.viewport.offsetHeight,\n            scrollbar: {\n              size: ref.current.clientHeight,\n              paddingStart: toInt(computedStyle.paddingTop),\n              paddingEnd: toInt(computedStyle.paddingBottom),\n            },\n          });\n        }\n      }}\n    />\n  );\n});\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype ScrollbarContext = {\n  hasThumb: boolean;\n  scrollbar: ScrollAreaScrollbarElement | null;\n  onThumbChange(thumb: ScrollAreaThumbElement | null): void;\n  onThumbPointerUp(): void;\n  onThumbPointerDown(pointerPos: { x: number; y: number }): void;\n  onThumbPositionChange(): void;\n};\n\nconst [ScrollbarProvider, useScrollbarContext] =\n  createScrollAreaContext<ScrollbarContext>(SCROLLBAR_NAME);\n\ntype ScrollAreaScrollbarImplElement = React.ElementRef<typeof Primitive.div>;\ntype ScrollAreaScrollbarImplPrivateProps = {\n  sizes: Sizes;\n  hasThumb: boolean;\n  onThumbChange: ScrollbarContext['onThumbChange'];\n  onThumbPointerUp: ScrollbarContext['onThumbPointerUp'];\n  onThumbPointerDown: ScrollbarContext['onThumbPointerDown'];\n  onThumbPositionChange: ScrollbarContext['onThumbPositionChange'];\n  onWheelScroll(event: WheelEvent, maxScrollPos: number): void;\n  onDragScroll(pointerPos: { x: number; y: number }): void;\n  onResize(): void;\n};\ninterface ScrollAreaScrollbarImplProps\n  extends Omit<PrimitiveDivProps, keyof ScrollAreaScrollbarImplPrivateProps>,\n    ScrollAreaScrollbarImplPrivateProps {}\n\nconst ScrollAreaScrollbarImpl = React.forwardRef<\n  ScrollAreaScrollbarImplElement,\n  ScrollAreaScrollbarImplProps\n>((props: ScopedProps<ScrollAreaScrollbarImplProps>, forwardedRef) => {\n  const {\n    __scopeScrollArea,\n    sizes,\n    hasThumb,\n    onThumbChange,\n    onThumbPointerUp,\n    onThumbPointerDown,\n    onThumbPositionChange,\n    onDragScroll,\n    onWheelScroll,\n    onResize,\n    ...scrollbarProps\n  } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, __scopeScrollArea);\n  const [scrollbar, setScrollbar] = React.useState<ScrollAreaScrollbarElement | null>(null);\n  const composeRefs = useComposedRefs(forwardedRef, (node) => setScrollbar(node));\n  const rectRef = React.useRef<DOMRect | null>(null);\n  const prevWebkitUserSelectRef = React.useRef<string>('');\n  const viewport = context.viewport;\n  const maxScrollPos = sizes.content - sizes.viewport;\n  const handleWheelScroll = useCallbackRef(onWheelScroll);\n  const handleThumbPositionChange = useCallbackRef(onThumbPositionChange);\n  const handleResize = useDebounceCallback(onResize, 10);\n\n  function handleDragScroll(event: React.PointerEvent<HTMLElement>) {\n    if (rectRef.current) {\n      const x = event.clientX - rectRef.current.left;\n      const y = event.clientY - rectRef.current.top;\n      onDragScroll({ x, y });\n    }\n  }\n\n  /**\n   * We bind wheel event imperatively so we can switch off passive\n   * mode for document wheel event to allow it to be prevented\n   */\n  React.useEffect(() => {\n    const handleWheel = (event: WheelEvent) => {\n      const element = event.target as HTMLElement;\n      const isScrollbarWheel = scrollbar?.contains(element);\n      if (isScrollbarWheel) handleWheelScroll(event, maxScrollPos);\n    };\n    document.addEventListener('wheel', handleWheel, { passive: false });\n    return () => document.removeEventListener('wheel', handleWheel, { passive: false } as any);\n  }, [viewport, scrollbar, maxScrollPos, handleWheelScroll]);\n\n  /**\n   * Update thumb position on sizes change\n   */\n  React.useEffect(handleThumbPositionChange, [sizes, handleThumbPositionChange]);\n\n  useResizeObserver(scrollbar, handleResize);\n  useResizeObserver(context.content, handleResize);\n\n  return (\n    <ScrollbarProvider\n      scope={__scopeScrollArea}\n      scrollbar={scrollbar}\n      hasThumb={hasThumb}\n      onThumbChange={useCallbackRef(onThumbChange)}\n      onThumbPointerUp={useCallbackRef(onThumbPointerUp)}\n      onThumbPositionChange={handleThumbPositionChange}\n      onThumbPointerDown={useCallbackRef(onThumbPointerDown)}\n    >\n      <Primitive.div\n        {...scrollbarProps}\n        ref={composeRefs}\n        style={{ position: 'absolute', ...scrollbarProps.style }}\n        onPointerDown={composeEventHandlers(props.onPointerDown, (event) => {\n          const mainPointer = 0;\n          if (event.button === mainPointer) {\n            const element = event.target as HTMLElement;\n            element.setPointerCapture(event.pointerId);\n            rectRef.current = scrollbar!.getBoundingClientRect();\n            // pointer capture doesn't prevent text selection in Safari\n            // so we remove text selection manually when scrolling\n            prevWebkitUserSelectRef.current = document.body.style.webkitUserSelect;\n            document.body.style.webkitUserSelect = 'none';\n            if (context.viewport) context.viewport.style.scrollBehavior = 'auto';\n            handleDragScroll(event);\n          }\n        })}\n        onPointerMove={composeEventHandlers(props.onPointerMove, handleDragScroll)}\n        onPointerUp={composeEventHandlers(props.onPointerUp, (event) => {\n          const element = event.target as HTMLElement;\n          if (element.hasPointerCapture(event.pointerId)) {\n            element.releasePointerCapture(event.pointerId);\n          }\n          document.body.style.webkitUserSelect = prevWebkitUserSelectRef.current;\n          if (context.viewport) context.viewport.style.scrollBehavior = '';\n          rectRef.current = null;\n        })}\n      />\n    </ScrollbarProvider>\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * ScrollAreaThumb\n * -----------------------------------------------------------------------------------------------*/\n\nconst THUMB_NAME = 'ScrollAreaThumb';\n\ntype ScrollAreaThumbElement = ScrollAreaThumbImplElement;\ninterface ScrollAreaThumbProps extends ScrollAreaThumbImplProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst ScrollAreaThumb = React.forwardRef<ScrollAreaThumbElement, ScrollAreaThumbProps>(\n  (props: ScopedProps<ScrollAreaThumbProps>, forwardedRef) => {\n    const { forceMount, ...thumbProps } = props;\n    const scrollbarContext = useScrollbarContext(THUMB_NAME, props.__scopeScrollArea);\n    return (\n      <Presence present={forceMount || scrollbarContext.hasThumb}>\n        <ScrollAreaThumbImpl ref={forwardedRef} {...thumbProps} />\n      </Presence>\n    );\n  }\n);\n\ntype ScrollAreaThumbImplElement = React.ElementRef<typeof Primitive.div>;\ninterface ScrollAreaThumbImplProps extends PrimitiveDivProps {}\n\nconst ScrollAreaThumbImpl = React.forwardRef<ScrollAreaThumbImplElement, ScrollAreaThumbImplProps>(\n  (props: ScopedProps<ScrollAreaThumbImplProps>, forwardedRef) => {\n    const { __scopeScrollArea, style, ...thumbProps } = props;\n    const scrollAreaContext = useScrollAreaContext(THUMB_NAME, __scopeScrollArea);\n    const scrollbarContext = useScrollbarContext(THUMB_NAME, __scopeScrollArea);\n    const { onThumbPositionChange } = scrollbarContext;\n    const composedRef = useComposedRefs(forwardedRef, (node) =>\n      scrollbarContext.onThumbChange(node)\n    );\n    const removeUnlinkedScrollListenerRef = React.useRef<() => void>(undefined);\n    const debounceScrollEnd = useDebounceCallback(() => {\n      if (removeUnlinkedScrollListenerRef.current) {\n        removeUnlinkedScrollListenerRef.current();\n        removeUnlinkedScrollListenerRef.current = undefined;\n      }\n    }, 100);\n\n    React.useEffect(() => {\n      const viewport = scrollAreaContext.viewport;\n      if (viewport) {\n        /**\n         * We only bind to native scroll event so we know when scroll starts and ends.\n         * When scroll starts we start a requestAnimationFrame loop that checks for\n         * changes to scroll position. That rAF loop triggers our thumb position change\n         * when relevant to avoid scroll-linked effects. We cancel the loop when scroll ends.\n         * https://developer.mozilla.org/en-US/docs/Mozilla/Performance/Scroll-linked_effects\n         */\n        const handleScroll = () => {\n          debounceScrollEnd();\n          if (!removeUnlinkedScrollListenerRef.current) {\n            const listener = addUnlinkedScrollListener(viewport, onThumbPositionChange);\n            removeUnlinkedScrollListenerRef.current = listener;\n            onThumbPositionChange();\n          }\n        };\n        onThumbPositionChange();\n        viewport.addEventListener('scroll', handleScroll);\n        return () => viewport.removeEventListener('scroll', handleScroll);\n      }\n    }, [scrollAreaContext.viewport, debounceScrollEnd, onThumbPositionChange]);\n\n    return (\n      <Primitive.div\n        data-state={scrollbarContext.hasThumb ? 'visible' : 'hidden'}\n        {...thumbProps}\n        ref={composedRef}\n        style={{\n          width: 'var(--radix-scroll-area-thumb-width)',\n          height: 'var(--radix-scroll-area-thumb-height)',\n          ...style,\n        }}\n        onPointerDownCapture={composeEventHandlers(props.onPointerDownCapture, (event) => {\n          const thumb = event.target as HTMLElement;\n          const thumbRect = thumb.getBoundingClientRect();\n          const x = event.clientX - thumbRect.left;\n          const y = event.clientY - thumbRect.top;\n          scrollbarContext.onThumbPointerDown({ x, y });\n        })}\n        onPointerUp={composeEventHandlers(props.onPointerUp, scrollbarContext.onThumbPointerUp)}\n      />\n    );\n  }\n);\n\nScrollAreaThumb.displayName = THUMB_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ScrollAreaCorner\n * -----------------------------------------------------------------------------------------------*/\n\nconst CORNER_NAME = 'ScrollAreaCorner';\n\ntype ScrollAreaCornerElement = ScrollAreaCornerImplElement;\ninterface ScrollAreaCornerProps extends ScrollAreaCornerImplProps {}\n\nconst ScrollAreaCorner = React.forwardRef<ScrollAreaCornerElement, ScrollAreaCornerProps>(\n  (props: ScopedProps<ScrollAreaCornerProps>, forwardedRef) => {\n    const context = useScrollAreaContext(CORNER_NAME, props.__scopeScrollArea);\n    const hasBothScrollbarsVisible = Boolean(context.scrollbarX && context.scrollbarY);\n    const hasCorner = context.type !== 'scroll' && hasBothScrollbarsVisible;\n    return hasCorner ? <ScrollAreaCornerImpl {...props} ref={forwardedRef} /> : null;\n  }\n);\n\nScrollAreaCorner.displayName = CORNER_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype ScrollAreaCornerImplElement = React.ElementRef<typeof Primitive.div>;\ninterface ScrollAreaCornerImplProps extends PrimitiveDivProps {}\n\nconst ScrollAreaCornerImpl = React.forwardRef<\n  ScrollAreaCornerImplElement,\n  ScrollAreaCornerImplProps\n>((props: ScopedProps<ScrollAreaCornerImplProps>, forwardedRef) => {\n  const { __scopeScrollArea, ...cornerProps } = props;\n  const context = useScrollAreaContext(CORNER_NAME, __scopeScrollArea);\n  const [width, setWidth] = React.useState(0);\n  const [height, setHeight] = React.useState(0);\n  const hasSize = Boolean(width && height);\n\n  useResizeObserver(context.scrollbarX, () => {\n    const height = context.scrollbarX?.offsetHeight || 0;\n    context.onCornerHeightChange(height);\n    setHeight(height);\n  });\n\n  useResizeObserver(context.scrollbarY, () => {\n    const width = context.scrollbarY?.offsetWidth || 0;\n    context.onCornerWidthChange(width);\n    setWidth(width);\n  });\n\n  return hasSize ? (\n    <Primitive.div\n      {...cornerProps}\n      ref={forwardedRef}\n      style={{\n        width,\n        height,\n        position: 'absolute',\n        right: context.dir === 'ltr' ? 0 : undefined,\n        left: context.dir === 'rtl' ? 0 : undefined,\n        bottom: 0,\n        ...props.style,\n      }}\n    />\n  ) : null;\n});\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction toInt(value?: string) {\n  return value ? parseInt(value, 10) : 0;\n}\n\nfunction getThumbRatio(viewportSize: number, contentSize: number) {\n  const ratio = viewportSize / contentSize;\n  return isNaN(ratio) ? 0 : ratio;\n}\n\nfunction getThumbSize(sizes: Sizes) {\n  const ratio = getThumbRatio(sizes.viewport, sizes.content);\n  const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n  const thumbSize = (sizes.scrollbar.size - scrollbarPadding) * ratio;\n  // minimum of 18 matches macOS minimum\n  return Math.max(thumbSize, 18);\n}\n\nfunction getScrollPositionFromPointer(\n  pointerPos: number,\n  pointerOffset: number,\n  sizes: Sizes,\n  dir: Direction = 'ltr'\n) {\n  const thumbSizePx = getThumbSize(sizes);\n  const thumbCenter = thumbSizePx / 2;\n  const offset = pointerOffset || thumbCenter;\n  const thumbOffsetFromEnd = thumbSizePx - offset;\n  const minPointerPos = sizes.scrollbar.paddingStart + offset;\n  const maxPointerPos = sizes.scrollbar.size - sizes.scrollbar.paddingEnd - thumbOffsetFromEnd;\n  const maxScrollPos = sizes.content - sizes.viewport;\n  const scrollRange = dir === 'ltr' ? [0, maxScrollPos] : [maxScrollPos * -1, 0];\n  const interpolate = linearScale([minPointerPos, maxPointerPos], scrollRange as [number, number]);\n  return interpolate(pointerPos);\n}\n\nfunction getThumbOffsetFromScroll(scrollPos: number, sizes: Sizes, dir: Direction = 'ltr') {\n  const thumbSizePx = getThumbSize(sizes);\n  const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n  const scrollbar = sizes.scrollbar.size - scrollbarPadding;\n  const maxScrollPos = sizes.content - sizes.viewport;\n  const maxThumbPos = scrollbar - thumbSizePx;\n  const scrollClampRange = dir === 'ltr' ? [0, maxScrollPos] : [maxScrollPos * -1, 0];\n  const scrollWithoutMomentum = clamp(scrollPos, scrollClampRange as [number, number]);\n  const interpolate = linearScale([0, maxScrollPos], [0, maxThumbPos]);\n  return interpolate(scrollWithoutMomentum);\n}\n\n// https://github.com/tmcw-up-for-adoption/simple-linear-scale/blob/master/index.js\nfunction linearScale(input: readonly [number, number], output: readonly [number, number]) {\n  return (value: number) => {\n    if (input[0] === input[1] || output[0] === output[1]) return output[0];\n    const ratio = (output[1] - output[0]) / (input[1] - input[0]);\n    return output[0] + ratio * (value - input[0]);\n  };\n}\n\nfunction isScrollingWithinScrollbarBounds(scrollPos: number, maxScrollPos: number) {\n  return scrollPos > 0 && scrollPos < maxScrollPos;\n}\n\n// Custom scroll handler to avoid scroll-linked effects\n// https://developer.mozilla.org/en-US/docs/Mozilla/Performance/Scroll-linked_effects\nconst addUnlinkedScrollListener = (node: HTMLElement, handler = () => {}) => {\n  let prevPosition = { left: node.scrollLeft, top: node.scrollTop };\n  let rAF = 0;\n  (function loop() {\n    const position = { left: node.scrollLeft, top: node.scrollTop };\n    const isHorizontalScroll = prevPosition.left !== position.left;\n    const isVerticalScroll = prevPosition.top !== position.top;\n    if (isHorizontalScroll || isVerticalScroll) handler();\n    prevPosition = position;\n    rAF = window.requestAnimationFrame(loop);\n  })();\n  return () => window.cancelAnimationFrame(rAF);\n};\n\nfunction useDebounceCallback(callback: () => void, delay: number) {\n  const handleCallback = useCallbackRef(callback);\n  const debounceTimerRef = React.useRef(0);\n  React.useEffect(() => () => window.clearTimeout(debounceTimerRef.current), []);\n  return React.useCallback(() => {\n    window.clearTimeout(debounceTimerRef.current);\n    debounceTimerRef.current = window.setTimeout(handleCallback, delay);\n  }, [handleCallback, delay]);\n}\n\nfunction useResizeObserver(element: HTMLElement | null, onResize: () => void) {\n  const handleResize = useCallbackRef(onResize);\n  useLayoutEffect(() => {\n    let rAF = 0;\n    if (element) {\n      /**\n       * Resize Observer will throw an often benign error that says `ResizeObserver loop\n       * completed with undelivered notifications`. This means that ResizeObserver was not\n       * able to deliver all observations within a single animation frame, so we use\n       * `requestAnimationFrame` to ensure we don't deliver unnecessary observations.\n       * Further reading: https://github.com/WICG/resize-observer/issues/38\n       */\n      const resizeObserver = new ResizeObserver(() => {\n        cancelAnimationFrame(rAF);\n        rAF = window.requestAnimationFrame(handleResize);\n      });\n      resizeObserver.observe(element);\n      return () => {\n        window.cancelAnimationFrame(rAF);\n        resizeObserver.unobserve(element);\n      };\n    }\n  }, [element, handleResize]);\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = ScrollArea;\nconst Viewport = ScrollAreaViewport;\nconst Scrollbar = ScrollAreaScrollbar;\nconst Thumb = ScrollAreaThumb;\nconst Corner = ScrollAreaCorner;\n\nexport {\n  createScrollAreaScope,\n  //\n  ScrollArea,\n  ScrollAreaViewport,\n  ScrollAreaScrollbar,\n  ScrollAreaThumb,\n  ScrollAreaCorner,\n  //\n  Root,\n  Viewport,\n  Scrollbar,\n  Thumb,\n  Corner,\n};\nexport type {\n  ScrollAreaProps,\n  ScrollAreaViewportProps,\n  ScrollAreaScrollbarProps,\n  ScrollAreaThumbProps,\n  ScrollAreaCornerProps,\n};\n", "import * as React from 'react';\n\ntype Machine<S> = { [k: string]: { [k: string]: S } };\ntype MachineState<T> = keyof T;\ntype MachineEvent<T> = keyof UnionToIntersection<T[keyof T]>;\n\n// 🤯 https://fettblog.eu/typescript-union-to-intersection/\ntype UnionToIntersection<T> = (T extends any ? (x: T) => any : never) extends (x: infer R) => any\n  ? R\n  : never;\n\nexport function useStateMachine<M>(\n  initialState: MachineState<M>,\n  machine: M & Machine<MachineState<M>>\n) {\n  return React.useReducer((state: MachineState<M>, event: MachineEvent<M>): MachineState<M> => {\n    const nextState = (machine[state] as any)[event];\n    return nextState ?? state;\n  }, initialState);\n}\n"], "names": ["React", "height", "width"], "mappings": ";;;;;;;;;;;;;;AAEA,YAAYA,YAAW;AACvB,SAAS,iBAAiB;AAC1B,SAAS,gBAAgB;AACzB,SAAS,0BAA0B;AACnC,SAAS,uBAAuB;AAChC,SAAS,sBAAsB;AAC/B,SAAS,oBAAoB;AAC7B,SAAS,uBAAuB;AAChC,SAAS,aAAa;AACtB,SAAS,4BAA4B;;AAoG7B,SAqCF,UArCE,KAqCF,YArCE;;;;;;;;;;;;;ACpGD,SAAS,gBACd,YAAA,EACA,OAAA,EACA;IACA,yKAAa,aAAA;sCAAW,CAAC,OAAwB,UAA4C;YAC3F,MAAM,YAAa,OAAA,CAAQ,KAAK,CAAA,CAAU,KAAK,CAAA;YAC/C,OAAO,aAAa;QACtB;qCAAG,YAAY;AACjB;;ADYA,IAAM,mBAAmB;AAGzB,IAAM,CAAC,yBAAyB,qBAAqB,CAAA,8KAAI,qBAAA,EAAmB,gBAAgB;AAuB5F,IAAM,CAAC,oBAAoB,oBAAoB,CAAA,GAC7C,wBAAgD,gBAAgB;AAUlE,IAAM,aAAmB,+KAAA,EACvB,CAAC,OAAqC,iBAAiB;IACrD,MAAM,EACJ,iBAAA,EACA,OAAO,OAAA,EACP,GAAA,EACA,kBAAkB,GAAA,EAClB,GAAG,iBACL,GAAI;IACJ,MAAM,CAAC,YAAY,aAAa,CAAA,GAAU,6KAAA,EAAmC,IAAI;IACjF,MAAM,CAAC,UAAU,WAAW,CAAA,qKAAU,WAAA,EAA2C,IAAI;IACrF,MAAM,CAAC,SAAS,UAAU,CAAA,qKAAU,WAAA,EAAgC,IAAI;IACxE,MAAM,CAAC,YAAY,aAAa,CAAA,qKAAU,WAAA,EAA4C,IAAI;IAC1F,MAAM,CAAC,YAAY,aAAa,CAAA,qKAAU,WAAA,EAA4C,IAAI;IAC1F,MAAM,CAAC,aAAa,cAAc,CAAA,oKAAU,YAAA,EAAS,CAAC;IACtD,MAAM,CAAC,cAAc,eAAe,CAAA,qKAAU,WAAA,EAAS,CAAC;IACxD,MAAM,CAAC,mBAAmB,oBAAoB,CAAA,qKAAU,WAAA,EAAS,KAAK;IACtE,MAAM,CAAC,mBAAmB,oBAAoB,CAAA,IAAU,4KAAA,EAAS,KAAK;IACtE,MAAM,kMAAe,kBAAA,EAAgB;oDAAc,CAAC,OAAS,cAAc,IAAI,CAAC;;IAChF,MAAM,YAAY,4LAAA,EAAa,GAAG;IAElC,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,oBAAA;QACC,OAAO;QACP;QACA,KAAK;QACL;QACA;QACA;QACA,kBAAkB;QAClB;QACA,iBAAiB;QACjB;QACA,oBAAoB;QACpB;QACA,2BAA2B;QAC3B;QACA,oBAAoB;QACpB;QACA,2BAA2B;QAC3B,qBAAqB;QACrB,sBAAsB;QAEtB,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,GAAA,EAAV;YACC,KAAK;YACJ,GAAG,eAAA;YACJ,KAAK;YACL,OAAO;gBACL,UAAU;gBAAA,0EAAA;gBAEV,CAAC,kCAAyC,CAAA,EAAG,cAAc;gBAC3D,CAAC,mCAA0C,CAAA,EAAG,eAAe;gBAC7D,GAAG,MAAM,KAAA;YACX;QAAA;IACF;AAGN;AAGF,WAAW,WAAA,GAAc;AAMzB,IAAM,gBAAgB;AAOtB,IAAM,qBAA2B,+KAAA,EAC/B,CAAC,OAA6C,iBAAiB;IAC7D,MAAM,EAAE,iBAAA,EAAmB,QAAA,EAAU,KAAA,EAAO,GAAG,cAAc,CAAA,GAAI;IACjE,MAAM,UAAU,qBAAqB,eAAe,iBAAiB;IACrE,MAAM,uKAAY,UAAA,EAAkC,IAAI;IACxD,MAAM,kMAAe,kBAAA,EAAgB,cAAc,KAAK,QAAQ,gBAAgB;IAChF,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,OAAA,EAAA,sKAAA,CAAA,WAAA,EAAA;QAEE,UAAA;YAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,SAAA;gBACC,yBAAyB;oBACvB,QAAQ,CAAA,mLAAA,CAAA;gBACV;gBACA;YAAA;YAEF,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,GAAA,EAAV;gBACC,mCAAgC;gBAC/B,GAAG,aAAA;gBACJ,KAAK;gBACL,OAAO;oBAAA;;;;;;;;;;aAAA,GAYL,WAAW,QAAQ,iBAAA,GAAoB,WAAW;oBAClD,WAAW,QAAQ,iBAAA,GAAoB,WAAW;oBAClD,GAAG,MAAM,KAAA;gBACX;gBASA,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,OAAA;oBAAI,KAAK,QAAQ,eAAA;oBAAiB,OAAO;wBAAE,UAAU;wBAAQ,SAAS;oBAAQ;oBAC5E;gBAAA,CACH;YAAA;SACF;IAAA,CACF;AAEJ;AAGF,mBAAmB,WAAA,GAAc;AAMjC,IAAM,iBAAiB;AAOvB,IAAM,wLAA4B,aAAA,EAChC,CAAC,OAA8C,iBAAiB;IAC9D,MAAM,EAAE,UAAA,EAAY,GAAG,eAAe,CAAA,GAAI;IAC1C,MAAM,UAAU,qBAAqB,gBAAgB,MAAM,iBAAiB;IAC5E,MAAM,EAAE,yBAAA,EAA2B,yBAAA,CAA0B,CAAA,GAAI;IACjE,MAAM,eAAe,MAAM,WAAA,KAAgB;sKAErC,YAAA;yCAAU,MAAM;YACpB,eAAe,0BAA0B,IAAI,IAAI,0BAA0B,IAAI;YAC/E;iDAAO,MAAM;oBACX,eAAe,0BAA0B,KAAK,IAAI,0BAA0B,KAAK;gBACnF;;QACF;wCAAG;QAAC;QAAc;QAA2B,yBAAyB;KAAC;IAEvE,OAAO,QAAQ,IAAA,KAAS,UACtB,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,0BAAA;QAA0B,GAAG,cAAA;QAAgB,KAAK;QAAc;IAAA,CAAwB,IACvF,QAAQ,IAAA,KAAS,WACnB,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,2BAAA;QAA2B,GAAG,cAAA;QAAgB,KAAK;QAAc;IAAA,CAAwB,IACxF,QAAQ,IAAA,KAAS,SACnB,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,yBAAA;QAAyB,GAAG,cAAA;QAAgB,KAAK;QAAc;IAAA,CAAwB,IACtF,QAAQ,IAAA,KAAS,WACnB,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,4BAAA;QAA4B,GAAG,cAAA;QAAgB,KAAK;IAAA,CAAc,IACjE;AACN;AAGF,oBAAoB,WAAA,GAAc;AASlC,IAAM,6LAAiC,aAAA,EAGrC,CAAC,OAAmD,iBAAiB;IACrE,MAAM,EAAE,UAAA,EAAY,GAAG,eAAe,CAAA,GAAI;IAC1C,MAAM,UAAU,qBAAqB,gBAAgB,MAAM,iBAAiB;IAC5E,MAAM,CAAC,SAAS,UAAU,CAAA,GAAU,6KAAA,EAAS,KAAK;sKAE5C,YAAA;8CAAU,MAAM;YACpB,MAAM,aAAa,QAAQ,UAAA;YAC3B,IAAI,YAAY;YAChB,IAAI,YAAY;gBACd,MAAM;6EAAqB,MAAM;wBAC/B,OAAO,YAAA,CAAa,SAAS;wBAC7B,WAAW,IAAI;oBACjB;;gBACA,MAAM;6EAAqB,MAAM;wBAC/B,YAAY,OAAO,UAAA;qFAAW,IAAM,WAAW,KAAK;oFAAG,QAAQ,eAAe;oBAChF;;gBACA,WAAW,gBAAA,CAAiB,gBAAgB,kBAAkB;gBAC9D,WAAW,gBAAA,CAAiB,gBAAgB,kBAAkB;gBAC9D;0DAAO,MAAM;wBACX,OAAO,YAAA,CAAa,SAAS;wBAC7B,WAAW,mBAAA,CAAoB,gBAAgB,kBAAkB;wBACjE,WAAW,mBAAA,CAAoB,gBAAgB,kBAAkB;oBACnE;;YACF;QACF;6CAAG;QAAC,QAAQ,UAAA;QAAY,QAAQ,eAAe;KAAC;IAEhD,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,0KAAC,WAAA,EAAA;QAAS,SAAS,cAAc;QAC/B,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,yBAAA;YACC,cAAY,UAAU,YAAY;YACjC,GAAG,cAAA;YACJ,KAAK;QAAA;IACP,CACF;AAEJ,CAAC;AAOD,IAAM,8LAAkC,aAAA,EAGtC,CAAC,OAAoD,iBAAiB;IACtE,MAAM,EAAE,UAAA,EAAY,GAAG,eAAe,CAAA,GAAI;IAC1C,MAAM,UAAU,qBAAqB,gBAAgB,MAAM,iBAAiB;IAC5E,MAAM,eAAe,MAAM,WAAA,KAAgB;IAC3C,MAAM,oBAAoB;4EAAoB,IAAM,KAAK,YAAY;2EAAG,GAAG;IAC3E,MAAM,CAAC,OAAO,IAAI,CAAA,GAAI,gBAAgB,UAAU;QAC9C,QAAQ;YACN,QAAQ;QACV;QACA,WAAW;YACT,YAAY;YACZ,eAAe;QACjB;QACA,aAAa;YACX,QAAQ;YACR,eAAe;QACjB;QACA,MAAM;YACJ,MAAM;YACN,QAAQ;YACR,eAAe;QACjB;IACF,CAAC;sKAEK,YAAA;+CAAU,MAAM;YACpB,IAAI,UAAU,QAAQ;gBACpB,MAAM,YAAY,OAAO,UAAA;qEAAW,IAAM,KAAK,MAAM;oEAAG,QAAQ,eAAe;gBAC/E;2DAAO,IAAM,OAAO,YAAA,CAAa,SAAS;;YAC5C;QACF;8CAAG;QAAC;QAAO,QAAQ,eAAA;QAAiB,IAAI;KAAC;sKAEnC,YAAA;+CAAU,MAAM;YACpB,MAAM,WAAW,QAAQ,QAAA;YACzB,MAAM,kBAAkB,eAAe,eAAe;YAEtD,IAAI,UAAU;gBACZ,IAAI,gBAAgB,QAAA,CAAS,eAAe,CAAA;gBAC5C,MAAM;wEAAe,MAAM;wBACzB,MAAM,YAAY,QAAA,CAAS,eAAe,CAAA;wBAC1C,MAAM,8BAA8B,kBAAkB;wBACtD,IAAI,6BAA6B;4BAC/B,KAAK,QAAQ;4BACb,kBAAkB;wBACpB;wBACA,gBAAgB;oBAClB;;gBACA,SAAS,gBAAA,CAAiB,UAAU,YAAY;gBAChD;2DAAO,IAAM,SAAS,mBAAA,CAAoB,UAAU,YAAY;;YAClE;QACF;8CAAG;QAAC,QAAQ,QAAA;QAAU;QAAc;QAAM,iBAAiB;KAAC;IAE5D,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,0KAAC,WAAA,EAAA;QAAS,SAAS,cAAc,UAAU;QACzC,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,4BAAA;YACC,cAAY,UAAU,WAAW,WAAW;YAC3C,GAAG,cAAA;YACJ,KAAK;YACL,oLAAgB,uBAAA,EAAqB,MAAM,cAAA,EAAgB,IAAM,KAAK,eAAe,CAAC;YACtF,mLAAgB,wBAAA,EAAqB,MAAM,cAAA,EAAgB,IAAM,KAAK,eAAe,CAAC;QAAA;IACxF,CACF;AAEJ,CAAC;AAOD,IAAM,4LAAgC,aAAA,EAGpC,CAAC,OAAkD,iBAAiB;IACpE,MAAM,UAAU,qBAAqB,gBAAgB,MAAM,iBAAiB;IAC5E,MAAM,EAAE,UAAA,EAAY,GAAG,eAAe,CAAA,GAAI;IAC1C,MAAM,CAAC,SAAS,UAAU,CAAA,qKAAU,WAAA,EAAS,KAAK;IAClD,MAAM,eAAe,MAAM,WAAA,KAAgB;IAC3C,MAAM,eAAe;qEAAoB,MAAM;YAC7C,IAAI,QAAQ,QAAA,EAAU;gBACpB,MAAM,cAAc,QAAQ,QAAA,CAAS,WAAA,GAAc,QAAQ,QAAA,CAAS,WAAA;gBACpE,MAAM,cAAc,QAAQ,QAAA,CAAS,YAAA,GAAe,QAAQ,QAAA,CAAS,YAAA;gBACrE,WAAW,eAAe,cAAc,WAAW;YACrD;QACF;oEAAG,EAAE;IAEL,kBAAkB,QAAQ,QAAA,EAAU,YAAY;IAChD,kBAAkB,QAAQ,OAAA,EAAS,YAAY;IAE/C,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,0KAAC,WAAA,EAAA;QAAS,SAAS,cAAc;QAC/B,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,4BAAA;YACC,cAAY,UAAU,YAAY;YACjC,GAAG,cAAA;YACJ,KAAK;QAAA;IACP,CACF;AAEJ,CAAC;AAUD,IAAM,8BAAmC,8KAAA,EAGvC,CAAC,OAAqD,iBAAiB;IACvE,MAAM,EAAE,cAAc,UAAA,EAAY,GAAG,eAAe,CAAA,GAAI;IACxD,MAAM,UAAU,qBAAqB,gBAAgB,MAAM,iBAAiB;IAC5E,MAAM,6KAAiB,SAAA,EAAsC,IAAI;IACjE,MAAM,qLAAyB,SAAA,EAAO,CAAC;IACvC,MAAM,CAAC,OAAO,QAAQ,CAAA,oKAAU,YAAA,EAAgB;QAC9C,SAAS;QACT,UAAU;QACV,WAAW;YAAE,MAAM;YAAG,cAAc;YAAG,YAAY;QAAE;IACvD,CAAC;IACD,MAAM,aAAa,cAAc,MAAM,QAAA,EAAU,MAAM,OAAO;IAG9D,MAAM,cAAwE;QAC5E,GAAG,cAAA;QACH;QACA,eAAe;QACf,UAAU,QAAQ,aAAa,KAAK,aAAa,CAAC;QAClD,eAAe,CAAC,QAAW,SAAS,OAAA,GAAU;QAC9C,kBAAkB,IAAO,iBAAiB,OAAA,GAAU;QACpD,oBAAoB,CAAC,aAAgB,iBAAiB,OAAA,GAAU;IAClE;IAEA,SAAS,kBAAkB,UAAA,EAAoB,GAAA,EAAiB;QAC9D,OAAO,6BAA6B,YAAY,iBAAiB,OAAA,EAAS,OAAO,GAAG;IACtF;IAEA,IAAI,gBAAgB,cAAc;QAChC,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,sBAAA;YACE,GAAG,WAAA;YACJ,KAAK;YACL,uBAAuB,MAAM;gBAC3B,IAAI,QAAQ,QAAA,IAAY,SAAS,OAAA,EAAS;oBACxC,MAAM,YAAY,QAAQ,QAAA,CAAS,UAAA;oBACnC,MAAM,SAAS,yBAAyB,WAAW,OAAO,QAAQ,GAAG;oBACrE,SAAS,OAAA,CAAQ,KAAA,CAAM,SAAA,GAAY,CAAA,YAAA,EAAe,MAAM,CAAA,SAAA,CAAA;gBAC1D;YACF;YACA,eAAe,CAAC,cAAc;gBAC5B,IAAI,QAAQ,QAAA,CAAU,CAAA,QAAQ,QAAA,CAAS,UAAA,GAAa;YACtD;YACA,cAAc,CAAC,eAAe;gBAC5B,IAAI,QAAQ,QAAA,EAAU;oBACpB,QAAQ,QAAA,CAAS,UAAA,GAAa,kBAAkB,YAAY,QAAQ,GAAG;gBACzE;YACF;QAAA;IAGN;IAEA,IAAI,gBAAgB,YAAY;QAC9B,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,sBAAA;YACE,GAAG,WAAA;YACJ,KAAK;YACL,uBAAuB,MAAM;gBAC3B,IAAI,QAAQ,QAAA,IAAY,SAAS,OAAA,EAAS;oBACxC,MAAM,YAAY,QAAQ,QAAA,CAAS,SAAA;oBACnC,MAAM,SAAS,yBAAyB,WAAW,KAAK;oBACxD,SAAS,OAAA,CAAQ,KAAA,CAAM,SAAA,GAAY,CAAA,eAAA,EAAkB,MAAM,CAAA,MAAA,CAAA;gBAC7D;YACF;YACA,eAAe,CAAC,cAAc;gBAC5B,IAAI,QAAQ,QAAA,CAAU,CAAA,QAAQ,QAAA,CAAS,SAAA,GAAY;YACrD;YACA,cAAc,CAAC,eAAe;gBAC5B,IAAI,QAAQ,QAAA,CAAU,CAAA,QAAQ,QAAA,CAAS,SAAA,GAAY,kBAAkB,UAAU;YACjF;QAAA;IAGN;IAEA,OAAO;AACT,CAAC;AAqBD,IAAM,yLAA6B,aAAA,EAGjC,CAAC,OAAkD,iBAAiB;IACpE,MAAM,EAAE,KAAA,EAAO,aAAA,EAAe,GAAG,eAAe,CAAA,GAAI;IACpD,MAAM,UAAU,qBAAqB,gBAAgB,MAAM,iBAAiB;IAC5E,MAAM,CAAC,eAAe,gBAAgB,CAAA,qKAAU,WAAA,CAA8B;IAC9E,MAAM,MAAY,2KAAA,EAAuC,IAAI;IAC7D,MAAM,iMAAc,kBAAA,EAAgB,cAAc,KAAK,QAAQ,kBAAkB;QAE3E,0KAAA;0CAAU,MAAM;YACpB,IAAI,IAAI,OAAA,CAAS,CAAA,iBAAiB,iBAAiB,IAAI,OAAO,CAAC;QACjE;yCAAG;QAAC,GAAG;KAAC;IAER,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,yBAAA;QACC,oBAAiB;QAChB,GAAG,cAAA;QACJ,KAAK;QACL;QACA,OAAO;YACL,QAAQ;YACR,MAAM,QAAQ,GAAA,KAAQ,QAAQ,0CAA0C;YACxE,OAAO,QAAQ,GAAA,KAAQ,QAAQ,0CAA0C;YACzE,CAAC,iCAAwC,CAAA,EAAG,aAAa,KAAK,IAAI;YAClE,GAAG,MAAM,KAAA;QACX;QACA,oBAAoB,CAAC,aAAe,MAAM,kBAAA,CAAmB,WAAW,CAAC;QACzE,cAAc,CAAC,aAAe,MAAM,YAAA,CAAa,WAAW,CAAC;QAC7D,eAAe,CAAC,OAAO,iBAAiB;YACtC,IAAI,QAAQ,QAAA,EAAU;gBACpB,MAAM,YAAY,QAAQ,QAAA,CAAS,UAAA,GAAa,MAAM,MAAA;gBACtD,MAAM,aAAA,CAAc,SAAS;gBAE7B,IAAI,iCAAiC,WAAW,YAAY,GAAG;oBAC7D,MAAM,cAAA,CAAe;gBACvB;YACF;QACF;QACA,UAAU,MAAM;YACd,IAAI,IAAI,OAAA,IAAW,QAAQ,QAAA,IAAY,eAAe;gBACpD,cAAc;oBACZ,SAAS,QAAQ,QAAA,CAAS,WAAA;oBAC1B,UAAU,QAAQ,QAAA,CAAS,WAAA;oBAC3B,WAAW;wBACT,MAAM,IAAI,OAAA,CAAQ,WAAA;wBAClB,cAAc,MAAM,cAAc,WAAW;wBAC7C,YAAY,MAAM,cAAc,YAAY;oBAC9C;gBACF,CAAC;YACH;QACF;IAAA;AAGN,CAAC;AAED,IAAM,wLAA6B,cAAA,EAGjC,CAAC,OAAkD,iBAAiB;IACpE,MAAM,EAAE,KAAA,EAAO,aAAA,EAAe,GAAG,eAAe,CAAA,GAAI;IACpD,MAAM,UAAU,qBAAqB,gBAAgB,MAAM,iBAAiB;IAC5E,MAAM,CAAC,eAAe,gBAAgB,CAAA,qKAAU,WAAA,CAA8B;IAC9E,MAAM,wKAAY,SAAA,EAAuC,IAAI;IAC7D,MAAM,iMAAc,kBAAA,EAAgB,cAAc,KAAK,QAAQ,kBAAkB;sKAE3E,YAAA;0CAAU,MAAM;YACpB,IAAI,IAAI,OAAA,CAAS,CAAA,iBAAiB,iBAAiB,IAAI,OAAO,CAAC;QACjE;yCAAG;QAAC,GAAG;KAAC;IAER,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,yBAAA;QACC,oBAAiB;QAChB,GAAG,cAAA;QACJ,KAAK;QACL;QACA,OAAO;YACL,KAAK;YACL,OAAO,QAAQ,GAAA,KAAQ,QAAQ,IAAI,KAAA;YACnC,MAAM,QAAQ,GAAA,KAAQ,QAAQ,IAAI,KAAA;YAClC,QAAQ;YACR,CAAC,kCAAyC,CAAA,EAAG,aAAa,KAAK,IAAI;YACnE,GAAG,MAAM,KAAA;QACX;QACA,oBAAoB,CAAC,aAAe,MAAM,kBAAA,CAAmB,WAAW,CAAC;QACzE,cAAc,CAAC,aAAe,MAAM,YAAA,CAAa,WAAW,CAAC;QAC7D,eAAe,CAAC,OAAO,iBAAiB;YACtC,IAAI,QAAQ,QAAA,EAAU;gBACpB,MAAM,YAAY,QAAQ,QAAA,CAAS,SAAA,GAAY,MAAM,MAAA;gBACrD,MAAM,aAAA,CAAc,SAAS;gBAE7B,IAAI,iCAAiC,WAAW,YAAY,GAAG;oBAC7D,MAAM,cAAA,CAAe;gBACvB;YACF;QACF;QACA,UAAU,MAAM;YACd,IAAI,IAAI,OAAA,IAAW,QAAQ,QAAA,IAAY,eAAe;gBACpD,cAAc;oBACZ,SAAS,QAAQ,QAAA,CAAS,YAAA;oBAC1B,UAAU,QAAQ,QAAA,CAAS,YAAA;oBAC3B,WAAW;wBACT,MAAM,IAAI,OAAA,CAAQ,YAAA;wBAClB,cAAc,MAAM,cAAc,UAAU;wBAC5C,YAAY,MAAM,cAAc,aAAa;oBAC/C;gBACF,CAAC;YACH;QACF;IAAA;AAGN,CAAC;AAaD,IAAM,CAAC,mBAAmB,mBAAmB,CAAA,GAC3C,wBAA0C,cAAc;AAkB1D,IAAM,4LAAgC,aAAA,EAGpC,CAAC,OAAkD,iBAAiB;IACpE,MAAM,EACJ,iBAAA,EACA,KAAA,EACA,QAAA,EACA,aAAA,EACA,gBAAA,EACA,kBAAA,EACA,qBAAA,EACA,YAAA,EACA,aAAA,EACA,QAAA,EACA,GAAG,gBACL,GAAI;IACJ,MAAM,UAAU,qBAAqB,gBAAgB,iBAAiB;IACtE,MAAM,CAAC,WAAW,YAAY,CAAA,oKAAU,YAAA,EAA4C,IAAI;IACxF,MAAM,iMAAc,kBAAA,EAAgB;gEAAc,CAAC,OAAS,aAAa,IAAI,CAAC;;IAC9E,MAAM,WAAgB,0KAAA,EAAuB,IAAI;IACjD,MAAM,2LAAgC,UAAA,EAAe,EAAE;IACvD,MAAM,WAAW,QAAQ,QAAA;IACzB,MAAM,eAAe,MAAM,OAAA,GAAU,MAAM,QAAA;IAC3C,MAAM,8MAAoB,iBAAA,EAAe,aAAa;IACtD,MAAM,sNAA4B,iBAAA,EAAe,qBAAqB;IACtE,MAAM,eAAe,oBAAoB,UAAU,EAAE;IAErD,SAAS,iBAAiB,KAAA,EAAwC;QAChE,IAAI,QAAQ,OAAA,EAAS;YACnB,MAAM,IAAI,MAAM,OAAA,GAAU,QAAQ,OAAA,CAAQ,IAAA;YAC1C,MAAM,IAAI,MAAM,OAAA,GAAU,QAAQ,OAAA,CAAQ,GAAA;YAC1C,aAAa;gBAAE;gBAAG;YAAE,CAAC;QACvB;IACF;sKAMM,YAAA;6CAAU,MAAM;YACpB,MAAM;iEAAc,CAAC,UAAsB;oBACzC,MAAM,UAAU,MAAM,MAAA;oBACtB,MAAM,mBAAmB,WAAW,SAAS,OAAO;oBACpD,IAAI,iBAAkB,CAAA,kBAAkB,OAAO,YAAY;gBAC7D;;YACA,SAAS,gBAAA,CAAiB,SAAS,aAAa;gBAAE,SAAS;YAAM,CAAC;YAClE;qDAAO,IAAM,SAAS,mBAAA,CAAoB,SAAS,aAAa;wBAAE,SAAS;oBAAM,CAAQ;;QAC3F;4CAAG;QAAC;QAAU;QAAW;QAAc,iBAAiB;KAAC;IAKnD,8KAAA,EAAU,2BAA2B;QAAC;QAAO,yBAAyB;KAAC;IAE7E,kBAAkB,WAAW,YAAY;IACzC,kBAAkB,QAAQ,OAAA,EAAS,YAAY;IAE/C,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,mBAAA;QACC,OAAO;QACP;QACA;QACA,yMAAe,iBAAA,EAAe,aAAa;QAC3C,4MAAkB,iBAAA,EAAe,gBAAgB;QACjD,uBAAuB;QACvB,8MAAoB,iBAAA,EAAe,kBAAkB;QAErD,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,GAAA,EAAV;YACE,GAAG,cAAA;YACJ,KAAK;YACL,OAAO;gBAAE,UAAU;gBAAY,GAAG,eAAe,KAAA;YAAM;YACvD,mLAAe,uBAAA,EAAqB,MAAM,aAAA,EAAe,CAAC,UAAU;gBAClE,MAAM,cAAc;gBACpB,IAAI,MAAM,MAAA,KAAW,aAAa;oBAChC,MAAM,UAAU,MAAM,MAAA;oBACtB,QAAQ,iBAAA,CAAkB,MAAM,SAAS;oBACzC,QAAQ,OAAA,GAAU,UAAW,qBAAA,CAAsB;oBAGnD,wBAAwB,OAAA,GAAU,SAAS,IAAA,CAAK,KAAA,CAAM,gBAAA;oBACtD,SAAS,IAAA,CAAK,KAAA,CAAM,gBAAA,GAAmB;oBACvC,IAAI,QAAQ,QAAA,CAAU,CAAA,QAAQ,QAAA,CAAS,KAAA,CAAM,cAAA,GAAiB;oBAC9D,iBAAiB,KAAK;gBACxB;YACF,CAAC;YACD,mBAAe,uLAAA,EAAqB,MAAM,aAAA,EAAe,gBAAgB;YACzE,iLAAa,uBAAA,EAAqB,MAAM,WAAA,EAAa,CAAC,UAAU;gBAC9D,MAAM,UAAU,MAAM,MAAA;gBACtB,IAAI,QAAQ,iBAAA,CAAkB,MAAM,SAAS,GAAG;oBAC9C,QAAQ,qBAAA,CAAsB,MAAM,SAAS;gBAC/C;gBACA,SAAS,IAAA,CAAK,KAAA,CAAM,gBAAA,GAAmB,wBAAwB,OAAA;gBAC/D,IAAI,QAAQ,QAAA,CAAU,CAAA,QAAQ,QAAA,CAAS,KAAA,CAAM,cAAA,GAAiB;gBAC9D,QAAQ,OAAA,GAAU;YACpB,CAAC;QAAA;IACH;AAGN,CAAC;AAMD,IAAM,aAAa;AAWnB,IAAM,mBAAwB,8KAAA,EAC5B,CAAC,OAA0C,iBAAiB;IAC1D,MAAM,EAAE,UAAA,EAAY,GAAG,WAAW,CAAA,GAAI;IACtC,MAAM,mBAAmB,oBAAoB,YAAY,MAAM,iBAAiB;IAChF,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,0KAAC,WAAA,EAAA;QAAS,SAAS,cAAc,iBAAiB,QAAA;QAChD,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,qBAAA;YAAoB,KAAK;YAAe,GAAG,UAAA;QAAA,CAAY;IAAA,CAC1D;AAEJ;AAMF,IAAM,wLAA4B,aAAA,EAChC,CAAC,OAA8C,iBAAiB;IAC9D,MAAM,EAAE,iBAAA,EAAmB,KAAA,EAAO,GAAG,WAAW,CAAA,GAAI;IACpD,MAAM,oBAAoB,qBAAqB,YAAY,iBAAiB;IAC5E,MAAM,mBAAmB,oBAAoB,YAAY,iBAAiB;IAC1E,MAAM,EAAE,qBAAA,CAAsB,CAAA,GAAI;IAClC,MAAM,iMAAc,kBAAA,EAAgB;4DAAc,CAAC,OACjD,iBAAiB,aAAA,CAAc,IAAI;;IAErC,MAAM,oMAAwC,SAAA,EAAmB,KAAA,CAAS;IAC1E,MAAM,oBAAoB;sEAAoB,MAAM;YAClD,IAAI,gCAAgC,OAAA,EAAS;gBAC3C,gCAAgC,OAAA,CAAQ;gBACxC,gCAAgC,OAAA,GAAU,KAAA;YAC5C;QACF;qEAAG,GAAG;sKAEA,YAAA;yCAAU,MAAM;YACpB,MAAM,WAAW,kBAAkB,QAAA;YACnC,IAAI,UAAU;gBAQZ,MAAM;kEAAe,MAAM;wBACzB,kBAAkB;wBAClB,IAAI,CAAC,gCAAgC,OAAA,EAAS;4BAC5C,MAAM,WAAW,0BAA0B,UAAU,qBAAqB;4BAC1E,gCAAgC,OAAA,GAAU;4BAC1C,sBAAsB;wBACxB;oBACF;;gBACA,sBAAsB;gBACtB,SAAS,gBAAA,CAAiB,UAAU,YAAY;gBAChD;qDAAO,IAAM,SAAS,mBAAA,CAAoB,UAAU,YAAY;;YAClE;QACF;wCAAG;QAAC,kBAAkB,QAAA;QAAU;QAAmB,qBAAqB;KAAC;IAEzE,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,GAAA,EAAV;QACC,cAAY,iBAAiB,QAAA,GAAW,YAAY;QACnD,GAAG,UAAA;QACJ,KAAK;QACL,OAAO;YACL,OAAO;YACP,QAAQ;YACR,GAAG,KAAA;QACL;QACA,0LAAsB,uBAAA,EAAqB,MAAM,oBAAA,EAAsB,CAAC,UAAU;YAChF,MAAM,QAAQ,MAAM,MAAA;YACpB,MAAM,YAAY,MAAM,qBAAA,CAAsB;YAC9C,MAAM,IAAI,MAAM,OAAA,GAAU,UAAU,IAAA;YACpC,MAAM,IAAI,MAAM,OAAA,GAAU,UAAU,GAAA;YACpC,iBAAiB,kBAAA,CAAmB;gBAAE;gBAAG;YAAE,CAAC;QAC9C,CAAC;QACD,iLAAa,uBAAA,EAAqB,MAAM,WAAA,EAAa,iBAAiB,gBAAgB;IAAA;AAG5F;AAGF,gBAAgB,WAAA,GAAc;AAM9B,IAAM,cAAc;AAKpB,IAAM,qLAAyB,aAAA,EAC7B,CAAC,OAA2C,iBAAiB;IAC3D,MAAM,UAAU,qBAAqB,aAAa,MAAM,iBAAiB;IACzE,MAAM,2BAA2B,QAAQ,QAAQ,UAAA,IAAc,QAAQ,UAAU;IACjF,MAAM,YAAY,QAAQ,IAAA,KAAS,YAAY;IAC/C,OAAO,YAAY,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,sBAAA;QAAsB,GAAG,KAAA;QAAO,KAAK;IAAA,CAAc,IAAK;AAC9E;AAGF,iBAAiB,WAAA,GAAc;AAO/B,IAAM,yLAA6B,aAAA,EAGjC,CAAC,OAA+C,iBAAiB;IACjE,MAAM,EAAE,iBAAA,EAAmB,GAAG,YAAY,CAAA,GAAI;IAC9C,MAAM,UAAU,qBAAqB,aAAa,iBAAiB;IACnE,MAAM,CAAC,OAAO,QAAQ,CAAA,GAAU,6KAAA,EAAS,CAAC;IAC1C,MAAM,CAAC,QAAQ,SAAS,CAAA,qKAAU,WAAA,EAAS,CAAC;IAC5C,MAAM,UAAU,QAAQ,SAAS,MAAM;IAEvC,kBAAkB,QAAQ,UAAA;kDAAY,MAAM;YAC1C,MAAMC,UAAS,QAAQ,UAAA,EAAY,gBAAgB;YACnD,QAAQ,oBAAA,CAAqBA,OAAM;YACnC,UAAUA,OAAM;QAClB,CAAC;;IAED,kBAAkB,QAAQ,UAAA;kDAAY,MAAM;YAC1C,MAAMC,SAAQ,QAAQ,UAAA,EAAY,eAAe;YACjD,QAAQ,mBAAA,CAAoBA,MAAK;YACjC,SAASA,MAAK;QAChB,CAAC;;IAED,OAAO,UACL,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,GAAA,EAAV;QACE,GAAG,WAAA;QACJ,KAAK;QACL,OAAO;YACL;YACA;YACA,UAAU;YACV,OAAO,QAAQ,GAAA,KAAQ,QAAQ,IAAI,KAAA;YACnC,MAAM,QAAQ,GAAA,KAAQ,QAAQ,IAAI,KAAA;YAClC,QAAQ;YACR,GAAG,MAAM,KAAA;QACX;IAAA,KAEA;AACN,CAAC;AAID,SAAS,MAAM,KAAA,EAAgB;IAC7B,OAAO,QAAQ,SAAS,OAAO,EAAE,IAAI;AACvC;AAEA,SAAS,cAAc,YAAA,EAAsB,WAAA,EAAqB;IAChE,MAAM,QAAQ,eAAe;IAC7B,OAAO,MAAM,KAAK,IAAI,IAAI;AAC5B;AAEA,SAAS,aAAa,KAAA,EAAc;IAClC,MAAM,QAAQ,cAAc,MAAM,QAAA,EAAU,MAAM,OAAO;IACzD,MAAM,mBAAmB,MAAM,SAAA,CAAU,YAAA,GAAe,MAAM,SAAA,CAAU,UAAA;IACxE,MAAM,YAAA,CAAa,MAAM,SAAA,CAAU,IAAA,GAAO,gBAAA,IAAoB;IAE9D,OAAO,KAAK,GAAA,CAAI,WAAW,EAAE;AAC/B;AAEA,SAAS,6BACP,UAAA,EACA,aAAA,EACA,KAAA,EACA,MAAiB,KAAA,EACjB;IACA,MAAM,cAAc,aAAa,KAAK;IACtC,MAAM,cAAc,cAAc;IAClC,MAAM,SAAS,iBAAiB;IAChC,MAAM,qBAAqB,cAAc;IACzC,MAAM,gBAAgB,MAAM,SAAA,CAAU,YAAA,GAAe;IACrD,MAAM,gBAAgB,MAAM,SAAA,CAAU,IAAA,GAAO,MAAM,SAAA,CAAU,UAAA,GAAa;IAC1E,MAAM,eAAe,MAAM,OAAA,GAAU,MAAM,QAAA;IAC3C,MAAM,cAAc,QAAQ,QAAQ;QAAC;QAAG,YAAY;KAAA,GAAI;QAAC,eAAe,CAAA;QAAI,CAAC;KAAA;IAC7E,MAAM,cAAc,YAAY;QAAC;QAAe,aAAa;KAAA,EAAG,WAA+B;IAC/F,OAAO,YAAY,UAAU;AAC/B;AAEA,SAAS,yBAAyB,SAAA,EAAmB,KAAA,EAAc,MAAiB,KAAA,EAAO;IACzF,MAAM,cAAc,aAAa,KAAK;IACtC,MAAM,mBAAmB,MAAM,SAAA,CAAU,YAAA,GAAe,MAAM,SAAA,CAAU,UAAA;IACxE,MAAM,YAAY,MAAM,SAAA,CAAU,IAAA,GAAO;IACzC,MAAM,eAAe,MAAM,OAAA,GAAU,MAAM,QAAA;IAC3C,MAAM,cAAc,YAAY;IAChC,MAAM,mBAAmB,QAAQ,QAAQ;QAAC;QAAG,YAAY;KAAA,GAAI;QAAC,eAAe,CAAA;QAAI,CAAC;KAAA;IAClF,MAAM,yBAAwB,wKAAA,EAAM,WAAW,gBAAoC;IACnF,MAAM,cAAc,YAAY;QAAC;QAAG,YAAY;KAAA,EAAG;QAAC;QAAG,WAAW;KAAC;IACnE,OAAO,YAAY,qBAAqB;AAC1C;AAGA,SAAS,YAAY,KAAA,EAAkC,MAAA,EAAmC;IACxF,OAAO,CAAC,UAAkB;QACxB,IAAI,KAAA,CAAM,CAAC,CAAA,KAAM,KAAA,CAAM,CAAC,CAAA,IAAK,MAAA,CAAO,CAAC,CAAA,KAAM,MAAA,CAAO,CAAC,CAAA,CAAG,CAAA,OAAO,MAAA,CAAO,CAAC,CAAA;QACrE,MAAM,QAAA,CAAS,MAAA,CAAO,CAAC,CAAA,GAAI,MAAA,CAAO,CAAC,CAAA,IAAA,CAAM,KAAA,CAAM,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA;QAC3D,OAAO,MAAA,CAAO,CAAC,CAAA,GAAI,QAAA,CAAS,QAAQ,KAAA,CAAM,CAAC,CAAA;IAC7C;AACF;AAEA,SAAS,iCAAiC,SAAA,EAAmB,YAAA,EAAsB;IACjF,OAAO,YAAY,KAAK,YAAY;AACtC;AAIA,IAAM,4BAA4B,CAAC,MAAmB,UAAU,KAAO,CAAD,AAAC,KAAM;IAC3E,IAAI,eAAe;QAAE,MAAM,KAAK,UAAA;QAAY,KAAK,KAAK,SAAA;IAAU;IAChE,IAAI,MAAM;IACV,CAAC,SAAS,OAAO;QACf,MAAM,WAAW;YAAE,MAAM,KAAK,UAAA;YAAY,KAAK,KAAK,SAAA;QAAU;QAC9D,MAAM,qBAAqB,aAAa,IAAA,KAAS,SAAS,IAAA;QAC1D,MAAM,mBAAmB,aAAa,GAAA,KAAQ,SAAS,GAAA;QACvD,IAAI,sBAAsB,iBAAkB,CAAA,QAAQ;QACpD,eAAe;QACf,MAAM,OAAO,qBAAA,CAAsB,IAAI;IACzC,CAAA,EAAG;IACH,OAAO,IAAM,OAAO,oBAAA,CAAqB,GAAG;AAC9C;AAEA,SAAS,oBAAoB,QAAA,EAAsB,KAAA,EAAe;IAChE,MAAM,2MAAiB,iBAAA,EAAe,QAAQ;IAC9C,MAAM,qLAAyB,SAAA,EAAO,CAAC;IACjC,8KAAA;yCAAU;iDAAM,IAAM,OAAO,YAAA,CAAa,iBAAiB,OAAO;;wCAAG,CAAC,CAAC;IAC7E,OAAa,gLAAA;2CAAY,MAAM;YAC7B,OAAO,YAAA,CAAa,iBAAiB,OAAO;YAC5C,iBAAiB,OAAA,GAAU,OAAO,UAAA,CAAW,gBAAgB,KAAK;QACpE;0CAAG;QAAC;QAAgB,KAAK;KAAC;AAC5B;AAEA,SAAS,kBAAkB,OAAA,EAA6B,QAAA,EAAsB;IAC5E,MAAM,yMAAe,iBAAA,EAAe,QAAQ;IAC5C,CAAA,GAAA,sLAAA,CAAA,kBAAA;6CAAgB,MAAM;YACpB,IAAI,MAAM;YACV,IAAI,SAAS;gBAQX,MAAM,iBAAiB,IAAI;yDAAe,MAAM;wBAC9C,qBAAqB,GAAG;wBACxB,MAAM,OAAO,qBAAA,CAAsB,YAAY;oBACjD,CAAC;;gBACD,eAAe,OAAA,CAAQ,OAAO;gBAC9B;yDAAO,MAAM;wBACX,OAAO,oBAAA,CAAqB,GAAG;wBAC/B,eAAe,SAAA,CAAU,OAAO;oBAClC;;YACF;QACF;4CAAG;QAAC;QAAS,YAAY;KAAC;AAC5B;AAIA,IAAM,OAAO;AACb,IAAM,WAAW;AACjB,IAAM,YAAY;AAClB,IAAM,QAAQ;AACd,IAAM,SAAS", "ignoreList": [0, 1], "debugId": null}}, {"offset": {"line": 6505, "column": 0}, "map": {"version": 3, "file": "person-standing.js", "sources": ["file:///C:/Users/<USER>/Documents/Fiverr/web3bingo/client/node_modules/lucide-react/src/icons/person-standing.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '5', r: '1', key: 'gxeob9' }],\n  ['path', { d: 'm9 20 3-6 3 6', key: 'se2kox' }],\n  ['path', { d: 'm6 8 6 2 6-2', key: '4o3us4' }],\n  ['path', { d: 'M12 10v4', key: '1kjpxc' }],\n];\n\n/**\n * @component @name PersonStanding\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjUiIHI9IjEiIC8+CiAgPHBhdGggZD0ibTkgMjAgMy02IDMgNiIgLz4KICA8cGF0aCBkPSJtNiA4IDYgMiA2LTIiIC8+CiAgPHBhdGggZD0iTTEyIDEwdjQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/person-standing\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst PersonStanding = createLucideIcon('PersonStanding', __iconNode);\n\nexport default PersonStanding;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA,CAAA;IACvD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IAC9C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IAC7C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC3C,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,cAAA,CAAiB,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAkB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}